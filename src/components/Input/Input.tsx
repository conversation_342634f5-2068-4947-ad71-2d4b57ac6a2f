import React, { InputHTMLAttributes } from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  registration?: UseFormRegisterReturn;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  registration,
  className = '',
  ...props
}) => {
  return (
    <div className="w-full">
      <label htmlFor={props.id} className="block text-sm font-medium text-gray-700 uppercase mb-1">
        {label}
      </label>
      <input
        className={`w-full px-4 py-3 rounded-md bg-gray-100 border ${
          error ? 'border-red-500' : 'border-gray-200'
        } ${className}`}
        {...registration}
        {...props}
      />
      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
};

export default Input;
