import React, { useState } from 'react';

interface AvatarDisplayProps {
  avatarUrl?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  onClick?: () => void;
  showDefault?: boolean;
}

export const AvatarDisplay: React.FC<AvatarDisplayProps> = ({
  avatarUrl,
  size = 'md',
  className = '',
  onClick,
  showDefault = true,
}) => {
  const [error, setError] = useState(false);

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24',
  };

  const defaultAvatar = (
    <div
      className={`${sizeClasses[size]} bg-gray-200 rounded-full flex items-center justify-center ${className}`}
    >
      <svg className="w-1/2 h-1/2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
          clipRule="evenodd"
        />
      </svg>
    </div>
  );

  // Jeśli nie ma URL awatara lub wystąpił błąd, pokaż domyślny
  if (!avatarUrl || error) {
    return showDefault ? (
      onClick ? (
        <button
          onClick={onClick}
          className="focus:outline-none focus:ring-2 focus:ring-primaryBtn rounded-full"
        >
          {defaultAvatar}
        </button>
      ) : (
        defaultAvatar
      )
    ) : null;
  }

  // Przekształć URL modelu 3D na URL podglądu 2D
  const getAvatarImageUrl = (modelUrl: string) => {
    // Model URL format: https://models.readyplayer.me/[AVATAR_ID].glb
    // Image URL format: https://models.readyplayer.me/[AVATAR_ID].png
    return modelUrl.replace(/\.glb$/, '.png');
  };

  const imageUrl = getAvatarImageUrl(avatarUrl);

  const avatarImage = (
    <div className={`${sizeClasses[size]} rounded-full overflow-hidden ${className}`}>
      <img
        src={imageUrl}
        alt="Awatar użytkownika"
        className="w-full h-full object-cover"
        onError={() => setError(true)}
      />
    </div>
  );

  return onClick ? (
    <button
      onClick={onClick}
      className="focus:outline-none focus:ring-2 focus:ring-primaryBtn rounded-full"
    >
      {avatarImage}
    </button>
  ) : (
    avatarImage
  );
};
