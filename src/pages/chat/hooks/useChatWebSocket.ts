import { useRef, useState, useCallback, useEffect } from 'react';
import { Message } from '../types';

interface UseChatWebSocketProps {
  userId?: string;
  chatId?: string;
  onMessageReceived: (message: Message) => void;
}

/**
 * Uproszczona implementacja hooka WebSocket bez faktycznego nawiązywania połączenia.
 * Zachowuje interfejs oryginalnego hooka, ale nie tworzy faktycznego połączenia WebSocket.
 */
export const useChatWebSocket = ({ userId, chatId, onMessageReceived }: UseChatWebSocketProps) => {
  // Zachowujemy podstawowe stany, aby interfejs był zgodny z oryginałem
  const [isWsConnected, setIsWsConnected] = useState(true); // Domyślnie true, aby UI nie pokazywał błędów
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const hasReceivedEnoughContentRef = useRef<boolean>(false);

  // Funkcja symulująca połączenie z WebSocketem
  const connectToWebSocket = useCallback(() => {
    console.log('WebSocket connection simulation started');
    setIsWsConnected(true);
    setLoading(false);
    setError(null);
    hasReceivedEnoughContentRef.current = false;
  }, []);

  // Funkcja symulująca rozłączenie z WebSocketem
  const disconnectWebSocket = useCallback(() => {
    console.log('WebSocket connection simulation stopped');
    setIsWsConnected(false);
  }, []);

  // Funkcja symulująca wysyłanie wiadomości przez WebSocket
  const sendMessage = useCallback((content: string) => {
    console.log('WebSocket message simulation:', content);
    return true; // Zawsze zwracamy sukces
  }, []);

  // Efekt czyszczący przy odmontowaniu komponentu
  useEffect(() => {
    return () => {
      disconnectWebSocket();
    };
  }, [disconnectWebSocket]);

  // Zwracamy ten sam interfejs co oryginalny hook
  return {
    isWsConnected,
    loading,
    error,
    connectToWebSocket,
    disconnectWebSocket,
    sendMessage,
    hasReceivedEnoughContent: hasReceivedEnoughContentRef.current,
  };
};

export default useChatWebSocket;
