import React, { useState } from 'react';
import { questions } from './data/questions';
import { WelcomeScreen } from './components/WelcomeScreen';
import { SingleQuestionView } from './components/SingleQuestionView';
import { QuestionTransition } from './components/QuestionTransition';
import { ResultsSummary } from './components/ResultsSummary';
import { Answer } from './types';
import { calculateResults } from './utils/calculateResults';

type AppState = 'welcome' | 'questionnaire' | 'results';

const QuestionnairePage: React.FC = () => {
  const [appState, setAppState] = useState<AppState>('welcome');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [answers, setAnswers] = useState<Answer[]>([]);
  const [dir, setDir] = useState<'next' | 'previous' | 'none'>('none');

  const start = () => {
    setAppState('questionnaire');
    setCurrentIndex(0);
  };
  const answerChange = (id: number, val: number) => {
    setAnswers(prev => {
      const ex = prev.find(a => a.questionId === id);
      return ex
        ? prev.map(a => (a.questionId === id ? { ...a, value: val } : a))
        : [...prev, { questionId: id, value: val }];
    });
  };
  const next = () => {
    if (currentIndex < questions.length - 1) {
      setDir('next');
      setTimeout(() => {
        setCurrentIndex(i => i + 1);
        setDir('none');
      }, 250);
    } else setAppState('results');
  };
  const prev = () => {
    if (currentIndex > 0) {
      setDir('previous');
      setTimeout(() => {
        setCurrentIndex(i => i - 1);
        setDir('none');
      }, 250);
    }
  };
  const reset = () => {
    setAppState('welcome');
    setCurrentIndex(0);
    setAnswers([]);
    setDir('none');
  };
  const restart = () => {
    setAppState('questionnaire');
    setCurrentIndex(0);
    setAnswers([]);
    setDir('none');
  };

  const currentAns = answers.find(a => a.questionId === questions[currentIndex].id);
  const canNext = currentAns !== undefined;
  const canPrev = currentIndex > 0;

  if (appState === 'welcome')
    return <WelcomeScreen onStart={start} totalQuestions={questions.length} />;
  if (appState === 'results')
    return (
      <ResultsSummary
        results={calculateResults(answers)}
        answers={answers}
        onReset={reset}
        onRestart={restart}
      />
    );

  return (
    <QuestionTransition isVisible={dir === 'none'} direction={dir}>
      <SingleQuestionView
        question={questions[currentIndex]}
        answer={currentAns}
        onAnswerChange={answerChange}
        onNext={next}
        onPrevious={prev}
        questionNumber={currentIndex + 1}
        totalQuestions={questions.length}
        canGoNext={canNext}
        canGoPrevious={canPrev}
      />
    </QuestionTransition>
  );
};

export default QuestionnairePage;
