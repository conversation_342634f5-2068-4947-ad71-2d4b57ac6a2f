import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { chatService } from '../../services/chatService';
import { authService } from '../../services/authService';
import { ChatInput } from '../../components/chat/ChatInput';
import GameInterventionsComponent from '../../components/games/GameInterventionsComponent';
import InterventionsFromIdsComponent from '../../components/interventions/InterventionsFromIdsComponent';
import { detectInterventionsInText, InterventionItem } from '../../pages/chat/utils/chatUtils';

// Definicja typu dla danych odpowiedzi
// Using InterventionItem imported from chatUtils

interface GameItem {
  domain?: string;
  [key: string]: unknown;
}

interface ResponseData {
  text?: string;
  interventions?: InterventionItem[];
  games?: GameItem[];
  message_id?: number;
  [key: string]: unknown;
}

interface Message {
  id: number;
  text: string;
  sender: string;
  timestamp: string;
  type: string;
  isStreaming?: boolean;
  role: string;
  data?: ResponseData;
}

// Globalna zmienna do przechowywania pełnej odpowiedzi JSON
let fullResponseData: ResponseData | undefined = undefined;

// Bufor do przechowywania pełnego tekstu odpowiedzi
let accumulatedText = '';

/**
 * Przetwarza chunk odpowiedzi z API i zwraca zaktualizowany tekst odpowiedzi
 */
const processResponseChunk = (
  chunk: string,
  currentResponseText: string
): {
  responseText: string;
  hasNewContent: boolean;
  responseData: ResponseData | undefined;
  isComplete?: boolean;
} => {
  // Dodajemy chunk do bufora tekstu
  accumulatedText += chunk;

  // Sprawdzamy, czy mamy pełną odpowiedź z [FULL_RESPONSE]
  if (accumulatedText.includes('[FULL_RESPONSE]')) {
    // Wyodrębniamy pełną odpowiedź
    const fullResponseMatch = accumulatedText.match(/\[FULL_RESPONSE\]([\s\S]*?)(?:\[\[END\]\]|$)/);
    if (fullResponseMatch && fullResponseMatch[1]) {
      try {
        const jsonStr = fullResponseMatch[1].trim();
        const jsonData = JSON.parse(jsonStr) as ResponseData;

        // Zapisujemy pełną odpowiedź JSON do zmiennej globalnej
        fullResponseData = jsonData;

        // Zwracamy pełną odpowiedź JSON
        return {
          responseText: jsonData.text || currentResponseText,
          hasNewContent: true,
          responseData: jsonData,
          isComplete: false,
        };
      } catch (_) {
        // ignore JSON parse errors
      }
    }
  }

  // Sprawdzamy, czy chunk zawiera znacznik końca
  if (chunk.includes('[[END]]')) {
    // Resetujemy bufor tekstu
    const finalText = processRawText(accumulatedText);
    accumulatedText = '';

    return {
      responseText: finalText,
      hasNewContent: true,
      responseData: fullResponseData, // Używamy zapisanej pełnej odpowiedzi
      isComplete: true,
    };
  }

  // Przetwarzamy tekst z bufora
  const processedText = processRawText(accumulatedText);

  return {
    responseText: processedText,
    hasNewContent: true,
    responseData: fullResponseData,
    isComplete: false,
  };
};

/**
 * Przetwarza surowy tekst, usuwając prefiksy "data:" i formatując tekst
 */
const processRawText = (rawText: string): string => {
  // Dzielimy tekst na linie
  const lines = rawText.split('\n');
  let result = '';

  for (const line of lines) {
    // Pomijamy puste linie
    if (!line.trim()) continue;

    // Usuwamy prefiks "data:" i białe znaki
    const cleanLine = line.replace(/^data:\s*/i, '').trim();

    // Pomijamy puste linie po wyczyszczeniu
    if (!cleanLine) continue;

    // Pomijamy specjalne znaczniki
    if (
      cleanLine === '[FULL_RESPONSE]' ||
      cleanLine === '[[END]]' ||
      cleanLine.includes('[FULL_RESPONSE]{')
    ) {
      continue;
    }

    // Dodajemy spację przed nowym słowem, jeśli to konieczne
    if (result && result.length > 0 && !result.endsWith(' ') && !cleanLine.startsWith(' ')) {
      result += ' ';
    }

    // Dodajemy oczyszczoną linię do tekstu odpowiedzi
    result += cleanLine;
  }

  return result;
};

interface DocumentChatPageProps {
  documentId?: string;
  documentName?: string;
  documentSummary?: string;
}

// Komponent wiadomości czatu
const ChatMessage: React.FC<{
  message: Message;
  studentId?: number; // Dodanie propsa studentId
}> = ({ message, studentId }) => {
  const { text, sender, data, isStreaming } = message;
  const [displayText, setDisplayText] = useState(text);
  const [interventions, setInterventions] = useState<InterventionItem[]>(
    data && 'interventions' in data && Array.isArray(data.interventions)
      ? (data.interventions as InterventionItem[])
      : []
  );
  const [dots, setDots] = useState('.');

  // Efekt dla animacji wielokropka
  useEffect(() => {
    if (isStreaming) {
      const interval = setInterval(() => {
        setDots(prev => {
          if (prev === '.') return '..';
          if (prev === '..') return '...';
          return '.';
        });
      }, 500);

      return () => clearInterval(interval);
    }
  }, [isStreaming]);

  // Lokalne wykrywanie interwencji oraz czyszczenie tekstu
  useEffect(() => {
    if (!isStreaming && text) {
      let cleanedText = text.trim();

      // Usuń znaczniki bloków kodu ````
      if (cleanedText.startsWith('```')) {
        cleanedText = cleanedText
          .replace(/^```[a-zA-Z]*\n?/, '')
          .replace(/```$/, '')
          .trim();
      }

      const detected = detectInterventionsInText(cleanedText);

      if (detected) {
        setDisplayText(detected.text || '');

        if (detected.interventions && detected.interventions.length > 0) {
          const intv: InterventionItem[] =
            typeof detected.interventions[0] === 'string'
              ? (detected.interventions as string[]).map(id => ({ intervention_id: id }))
              : (detected.interventions as InterventionItem[]);

          setInterventions(intv);
        }
      } else {
        setDisplayText(cleanedText);
      }
    }
  }, [isStreaming, text, message]);

  // Sprawdź, czy tekst zawiera interwencje używając funkcji detectInterventionsInText
  useEffect(() => {
    if (!isStreaming && text && !data) {
      // Użyj funkcji detectInterventionsInText z chatUtils
      const detectedInterventions = detectInterventionsInText(text);

      if (detectedInterventions) {
        // Konwertujemy interventions na InterventionItem[] jeśli potrzeba
        let interventions: InterventionItem[] | undefined;

        if (detectedInterventions.interventions) {
          if (
            Array.isArray(detectedInterventions.interventions) &&
            detectedInterventions.interventions.length > 0
          ) {
            if (typeof detectedInterventions.interventions[0] === 'string') {
              interventions = (detectedInterventions.interventions as string[]).map(id => ({
                intervention_id: id,
              }));
            } else {
              interventions = detectedInterventions.interventions as InterventionItem[];
            }

            // Zaktualizuj wiadomość z danymi interwencji
            message.data = {
              ...message.data,
              interventions: interventions,
              text: detectedInterventions.text || '',
            };

            // Zaktualizuj tekst wiadomości (usuń JSON z interwencjami)
            message.text = detectedInterventions.text || '';
          }
        }
      }
    }
  }, [text, data, isStreaming, message]);

  return (
    <div className={`flex ${sender === 'user' ? 'justify-end' : 'justify-start'} mb-4`}>
      <div
        className={`max-w-[80%] rounded-lg px-4 py-2 ${
          sender === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900'
        }`}
      >
        <div className="whitespace-pre-wrap">{isStreaming ? dots : displayText}</div>

        {/* Wyświetlanie komponentu InterventionsFromIdsComponent, gdy wiadomość zawiera interwencje */}
        {interventions.length > 0 && (
          <div className="max-w-full overflow-x-auto mt-3 mb-1">
            <InterventionsFromIdsComponent interventions={interventions} studentId={studentId} />
          </div>
        )}

        {/* Wyświetlanie komponentu GameInterventionsComponent, gdy wiadomość zawiera dane gier */}
        {data &&
          typeof data === 'object' &&
          'games' in data &&
          Array.isArray(data.games) &&
          data.games.length > 0 && (
            <div className="max-w-full overflow-x-auto mt-3 mb-1">
              <GameInterventionsComponent
                studentId={studentId}
                areaId={(() => {
                  const sanitized = String(data.games[0].domain || '')
                    .toLowerCase()
                    .replace(/\s+/g, '-')
                    .replace(/[&]/g, 'and')
                    .replace(/[^a-z0-9-]/g, '');
                  return sanitized || 'default-area';
                })()}
                areaName={String(data.games[0].domain || 'Default area')}
                gradeLevel="1st grade"
              />
            </div>
          )}
      </div>
    </div>
  );
};

export const DocumentChatPage: React.FC<DocumentChatPageProps> = ({
  documentId: propDocumentId,
  documentName: propDocumentName,
  documentSummary: propDocumentSummary,
}) => {
  const params = useParams<{ documentId: string; studentId: string }>();
  const navigate = useNavigate();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const documentId = propDocumentId || params.documentId;
  const studentId = params.studentId; // Extract studentId from URL
  const documentName = propDocumentName || 'Document';
  const documentSummary = propDocumentSummary || 'No summary available';

  // Initialize chat session
  useEffect(() => {
    const initializeChat = async () => {
      if (!documentId) {
        setError('Document ID is missing');
        setLoading(false);
        return;
      }

      if (!studentId) {
        setError('Student ID is missing in URL');
        setLoading(false);
        return;
      }

      // Check if user is authenticated
      const token = authService.getAccessToken();
      if (!token) {
        setError('Please log in to use the chat');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Ensure documentId is numeric
        const numericDocumentId = Number(documentId);

        if (isNaN(numericDocumentId)) {
          throw new Error(`Invalid documentId format: ${documentId}`);
        }

        // First, check if there are previous sessions for this document
        const documentSessions = await chatService.getSessionsForDocument(numericDocumentId);

        // Sort sessions by creation date (newest first)
        const sortedSessions = documentSessions.sort(
          (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );

        let sessionId: string | null = null;

        if (sortedSessions.length > 0) {
          // Use the latest session for this document
          sessionId = sortedSessions[0].session_id;
          setCurrentSessionId(sessionId);

          // Get the chat history for this session
          const chatHistory = await chatService.getChatHistory(sessionId);

          if (chatHistory && chatHistory.length > 0) {
            // Convert chat history to Message format
            let historyMessages: Message[] = [];

            // Check the format of the chat history
            if ('user_question' in chatHistory[0] && 'assistant' in chatHistory[0]) {
              // Format with user_question and assistant

              // Convert chat history to Message format
              // Safe type casting
              const typedChatHistory = chatHistory as unknown as Array<{
                user_question: string;
                assistant: string;
              }>;
              for (const item of typedChatHistory) {
                // Add the user's question
                historyMessages.push({
                  id: Date.now() + Math.random(),
                  text: item.user_question,
                  sender: 'user',
                  timestamp: new Date().toISOString(),
                  type: 'text',
                  isStreaming: false,
                  role: 'user',
                });

                // Add the assistant's response
                // Check if the response contains interventions
                let responseData: ResponseData | undefined = undefined;
                const assistantText = item.assistant.trim();

                // Use the detectInterventionsInText function from chatUtils to detect interventions
                const detectedInterventions = detectInterventionsInText(assistantText);

                if (detectedInterventions) {
                  // Convert interventions to InterventionItem[] if needed
                  let interventions: InterventionItem[] | undefined;

                  if (detectedInterventions.interventions) {
                    if (
                      Array.isArray(detectedInterventions.interventions) &&
                      detectedInterventions.interventions.length > 0
                    ) {
                      if (typeof detectedInterventions.interventions[0] === 'string') {
                        interventions = (detectedInterventions.interventions as string[]).map(
                          id => ({
                            intervention_id: id,
                          })
                        );
                      } else {
                        interventions = detectedInterventions.interventions as InterventionItem[];
                      }

                      // Set responseData with interventions
                      responseData = {
                        text: detectedInterventions.text || '',
                        interventions: interventions,
                      };
                    }
                  }
                }

                // Create the assistant's message with interventions
                const assistantMessage = {
                  id: Date.now() + Math.random() + 1,
                  text: responseData && responseData.text ? responseData.text : assistantText,
                  sender: 'bot',
                  timestamp: new Date().toISOString(),
                  type: 'text',
                  isStreaming: false,
                  role: 'assistant',
                  data: responseData,
                };

                historyMessages.push(assistantMessage);
              }
            } else {
              // Standard format
              historyMessages = chatHistory.map(msg => {
                // Convert data to ResponseData format
                let responseData: ResponseData | undefined = undefined;
                if (msg.data) {
                  if (Array.isArray(msg.data)) {
                    // Convert to InterventionItem[] with strict typing
                    const interventions: InterventionItem[] = (
                      msg.data as { intervention_id: string }[]
                    ).map(item => ({
                      intervention_id: item.intervention_id || 'unknown',
                    }));

                    responseData = {
                      text: msg.text || '',
                      interventions: interventions,
                    };
                  } else {
                    // If data is an object, use it directly
                    responseData = msg.data as ResponseData;
                  }
                }

                return {
                  id: msg.id || Date.now() + Math.random(),
                  text: msg.text || '',
                  sender: msg.sender || 'bot',
                  timestamp: msg.timestamp || new Date().toISOString(),
                  type: msg.type || 'text',
                  isStreaming: false,
                  role: msg.role || (msg.sender === 'user' ? 'user' : 'assistant'),
                  data: responseData,
                };
              });
            }

            setMessages(historyMessages);
            setError(null);
          } else {
            // If the history is empty, add a system message
            const initialMessage: Message = {
              id: Date.now(),
              text: `You started a conversation about document "${documentName}".

Summary: ${documentSummary}

How can I help?`,
              sender: 'system',
              timestamp: new Date().toISOString(),
              type: 'text',
              isStreaming: false,
              role: 'system',
            };

            setMessages([initialMessage]);
          }
        } else {
          // If there are no previous sessions, create a new one linked to the document
          sessionId = await chatService.startSession(studentId, numericDocumentId);
          setCurrentSessionId(sessionId);

          // Add the initial system message with the document summary
          const initialMessage: Message = {
            id: Date.now(),
            text: `You started a conversation about document "${documentName}".

Summary: ${documentSummary}

How can I help?`,
            sender: 'system',
            timestamp: new Date().toISOString(),
            type: 'text',
            isStreaming: false,
            role: 'system',
          };

          setMessages([initialMessage]);
          setError(null);

          // Nie wysyłamy automatycznego pytania, aby uniknąć wywołania proxy-stream-normal
          // Użytkownik sam rozpocznie rozmowę
        }
      } catch (err) {
        const errorMsg = `Error initializing chat: ${err instanceof Error ? err.message : String(err)}`;
        setError(errorMsg);
      } finally {
        setLoading(false);
      }
    };

    initializeChat();

    return () => {
      // Clean up WebSocket connection when component unmounts
      chatService.disconnectWebSocket();
    };
  }, [documentId, documentName, documentSummary, studentId]);

  /**
   * Funkcja do wysyłania automatycznego pytania po rozpoczęciu sesji
   */
  const sendAutomaticQuestion = async (sessionId: string) => {
    if (!sessionId || !documentId) return;

    try {
      // Prepare the payload with the question
      const payload = {
        session_id: sessionId,
        question: "What are the student's main strengths as described in the report?",
        document_id: parseInt(documentId, 10),
      };

      let responseText = '';
      let _fullResp = false;

      // Send the question to the API
      await chatService.sendChatQuestion(
        payload,
        (chunk: string) => {
          const processed = processResponseChunk(chunk, responseText);
          if (processed.hasNewContent) {
            responseText = processed.responseText;

            // If we received the full JSON response, use it
            if (processed.responseData) {
              _fullResp = true;
            }

            // Update the messages
            setMessages(prev => {
              const botMessage = {
                id: Date.now(),
                text: processed.responseText,
                sender: 'bot',
                timestamp: new Date().toISOString(),
                type: 'text',
                isStreaming: true,
                role: 'assistant',
                data: processed.responseData || undefined,
              };

              // Check if the bot message already exists
              const botMessageIndex = prev.findIndex(
                msg => msg.sender === 'bot' && msg.role === 'assistant'
              );

              if (botMessageIndex !== -1) {
                // Update only the bot message
                const newMessages = [...prev];
                newMessages[botMessageIndex] = botMessage;
                return newMessages;
              } else {
                // Add a new bot message
                return [...prev, botMessage];
              }
            });

            // Auto-scroll on new content
            messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
          }

          // If the response is complete, finalize the message
          if (processed.isComplete) {
            setMessages(prev => {
              const botMessageIndex = prev.findIndex(
                msg => msg.sender === 'bot' && msg.isStreaming
              );
              if (botMessageIndex !== -1) {
                const newMessages = [...prev];
                newMessages[botMessageIndex] = {
                  ...newMessages[botMessageIndex],
                  isStreaming: false,
                };
                return newMessages;
              }
              return prev;
            });
          }
        },
        () => {
          // Finalize the message after streaming ends
          setMessages(prev => {
            const botMessageIndex = prev.findIndex(msg => msg.sender === 'bot' && msg.isStreaming);
            if (botMessageIndex !== -1) {
              const updated = [...prev];
              if (_fullResp && fullResponseData) {
                // If we have the full JSON response, use its text
                updated[botMessageIndex] = {
                  ...updated[botMessageIndex],
                  text: fullResponseData.text ?? '',
                  isStreaming: false,
                  data: fullResponseData,
                };
              } else if (responseText) {
                // Check if the text may contain interventions in the format: [{"intervention_id": "xxx"}, ...] text
                const interventionsWithTextMatch = responseText.match(/^(\[.*?\])\s+(.+)$/s);
                if (interventionsWithTextMatch) {
                  try {
                    const interventionsJson = interventionsWithTextMatch[1];
                    const textContent = interventionsWithTextMatch[2];

                    // Parse the interventions array
                    const interventions = JSON.parse(interventionsJson) as InterventionItem[];

                    // Create a ResponseData object
                    const responseData: ResponseData = {
                      text: textContent,
                      interventions: interventions,
                    };

                    // Update the message with interventions
                    updated[botMessageIndex] = {
                      ...updated[botMessageIndex],
                      text: textContent,
                      isStreaming: false,
                      data: responseData,
                    };
                    return updated;
                  } catch (_) {
                    // ignore secondary parse errors
                  }
                }
                // If parsing interventions from text fails or there are no interventions
                updated[botMessageIndex] = {
                  ...updated[botMessageIndex],
                  text: responseText,
                  isStreaming: false,
                  data: undefined,
                };
              } else {
                // Fallback
                updated[botMessageIndex] = {
                  ...updated[botMessageIndex],
                  isStreaming: false,
                  data: undefined,
                };
              }
              // Reset the text buffer
              accumulatedText = '';
              return updated;
            }
            return prev;
          });
        }
      );
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error sending automatic question:', error);
    }
  };

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = useCallback(
    async (content: string) => {
      if (!content.trim() || !currentSessionId || !documentId) return;

      // Reset global variables
      fullResponseData = undefined;
      accumulatedText = '';

      const userMessage = {
        id: Date.now(),
        text: content,
        sender: 'user',
        timestamp: new Date().toISOString(),
        type: 'text',
        isStreaming: false,
        role: 'user',
      } as Message;

      // Add the user's message to the message list
      setMessages(prev => [...prev, userMessage]);

      // Ensure the user message is displayed by waiting a moment before adding the loading message
      await new Promise(resolve => setTimeout(resolve, 100));

      const loadingMessageId = Date.now();

      try {
        const loadingMessage: Message = {
          id: loadingMessageId,
          text: '',
          sender: 'bot',
          timestamp: new Date().toISOString(),
          type: 'text',
          isStreaming: true,
          role: 'assistant',
          data: undefined,
        };

        // Add the loading message while preserving the user message
        setMessages(prev => {
          // Make sure we're not losing any messages, especially the user message
          const userMessageExists = prev.some(
            msg => msg.sender === 'user' && msg.text === content && msg.role === 'user'
          );

          // If somehow the user message is missing, add it back along with the loading message
          if (!userMessageExists) {
            return [...prev, userMessage, loadingMessage];
          }

          // Otherwise just add the loading message
          return [...prev, loadingMessage];
        });

        const payload = {
          session_id: currentSessionId,
          question: content,
          document_id: parseInt(documentId, 10),
        };

        let responseText = '';
        let _fullResp = false;

        await chatService.sendChatQuestion(
          payload,
          (chunk: string) => {
            const processed = processResponseChunk(chunk, responseText);

            // Update the response text only if there's new content
            if (processed.hasNewContent) {
              responseText = processed.responseText;

              // If we received the full JSON response, use it
              if (processed.responseData) {
                _fullResp = true;
              }

              setMessages(prev => {
                // Make sure we keep all previous messages, especially user messages
                let updated = [...prev];

                // Find the index of the bot message (if it exists)
                const loadingMessageIndex = updated.findIndex(msg => msg.id === loadingMessageId);

                if (loadingMessageIndex !== -1) {
                  // Update only the bot message
                  const newMessages = [...prev];
                  newMessages[loadingMessageIndex] = {
                    ...newMessages[loadingMessageIndex],
                    text: processed.responseText,
                    isStreaming: true,
                    data: processed.responseData || newMessages[loadingMessageIndex].data,
                  };
                  updated = newMessages;
                } else {
                  // Add a new bot message
                  updated.push({
                    id: loadingMessageId,
                    text: responseText,
                    sender: 'bot',
                    timestamp: new Date().toISOString(),
                    type: 'text',
                    isStreaming: true,
                    role: 'assistant',
                    data: processed.responseData || undefined,
                  });
                }

                return updated;
              });

              // Auto-scroll on new content
              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }

            // If the response is complete, finalize the message
            if (processed.isComplete) {
              setMessages(prev => {
                const newMessages = [...prev];
                const loadingMessageIndex = newMessages.findIndex(
                  msg => msg.id === loadingMessageId
                );
                if (loadingMessageIndex !== -1) {
                  newMessages[loadingMessageIndex] = {
                    ...newMessages[loadingMessageIndex],
                    sender: 'bot',
                    isStreaming: false,
                    data: undefined,
                  };
                }
                return newMessages;
              });
            }
          },
          () => {
            // Finalize the message after streaming ends
            setMessages(prev => {
              const newMessages = [...prev];
              const loadingMessageIndex = newMessages.findIndex(msg => msg.id === loadingMessageId);
              if (loadingMessageIndex !== -1) {
                const updated = [...newMessages];
                if (_fullResp && fullResponseData) {
                  // If we have the full JSON response, use its text
                  updated[loadingMessageIndex] = {
                    ...updated[loadingMessageIndex],
                    text: fullResponseData.text ?? '',
                    sender: 'bot',
                    isStreaming: false,
                    data: fullResponseData,
                  };
                } else if (responseText) {
                  // Check if the text may contain interventions in the format: [{"intervention_id": "xxx"}, ...] text
                  const interventionsWithTextMatch = responseText.match(/^(\[.*?\])\s+(.+)$/s);
                  if (interventionsWithTextMatch) {
                    try {
                      const interventionsJson = interventionsWithTextMatch[1];
                      const textContent = interventionsWithTextMatch[2];

                      // Parse the interventions array
                      const interventions = JSON.parse(interventionsJson) as InterventionItem[];

                      // Create a ResponseData object
                      const responseData: ResponseData = {
                        text: textContent,
                        interventions: interventions,
                      };

                      // Update the message with interventions
                      updated[loadingMessageIndex] = {
                        ...updated[loadingMessageIndex],
                        text: textContent,
                        sender: 'bot',
                        isStreaming: false,
                        data: responseData,
                      };
                      return updated;
                    } catch (_) {
                      // ignore secondary parse errors
                    }
                  }
                  // If parsing interventions from text fails or there are no interventions
                  updated[loadingMessageIndex] = {
                    ...updated[loadingMessageIndex],
                    text: responseText,
                    sender: 'bot',
                    isStreaming: false,
                    data: undefined,
                  };
                } else {
                  // Fallback
                  updated[loadingMessageIndex] = {
                    ...updated[loadingMessageIndex],
                    sender: 'bot',
                    isStreaming: false,
                    data: undefined,
                  };
                }
                // Reset the text buffer
                accumulatedText = '';
                return updated;
              }
              return prev;
            });
          }
        );
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Error sending message:', error);
        setMessages(prev => {
          const newMessages = [...prev];
          const loadingMessageIndex = newMessages.findIndex(msg => msg.id === loadingMessageId);
          if (loadingMessageIndex !== -1) {
            newMessages[loadingMessageIndex] = {
              ...newMessages[loadingMessageIndex],
              text: 'Przepraszamy, wystąpił błąd podczas przetwarzania Twojego zapytania.',
              isStreaming: false,
              data: undefined,
            };
          }
          return newMessages;
        });
      }
    },
    [currentSessionId, documentId, documentName, documentSummary, studentId]
  );

  const handleBack = useCallback(() => {
    // Navigate back to the document details
    if (documentId) {
      navigate(`/dashboard/document/${documentId}`);
    } else {
      navigate(-1);
    }
  }, [navigate, documentId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-pulse">Loading chat...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
        <button
          onClick={handleBack}
          className="mt-4 px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white rounded-lg shadow-sm">
      {/* Header */}
      <div className="flex items-center gap-4 p-4 border-b">
        <button
          onClick={handleBack}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          aria-label="Go back"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <div className="flex-1">
          <h1 className="text-lg font-semibold text-gray-900">{documentName}</h1>
          <p className="text-sm text-gray-500">Document Chat</p>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map(message => (
          <ChatMessage key={message.id} message={message} studentId={Number(studentId)} />
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t">
        <ChatInput onSendMessage={handleSendMessage} />
      </div>
    </div>
  );
};
