import React from 'react';
import { DeleteConfirmationModal } from '../modals/DeleteConfirmationModal';

interface StudentDeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  studentName?: string;
}

export const StudentDeleteConfirmationModal: React.FC<StudentDeleteConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  studentName,
}) => {
  const title = 'Delete Student';
  const message = studentName
    ? `Are you sure you want to delete ${studentName}? This action cannot be undone.`
    : 'Are you sure you want to delete this student? This action cannot be undone.';

  return (
    <DeleteConfirmationModal
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onConfirm}
      title={title}
      message={message}
    />
  );
};

export default StudentDeleteConfirmationModal;
