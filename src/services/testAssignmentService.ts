import { authService } from './authService';
import logger from '../utils/logger';

const API_URL = import.meta.env.VITE_API_URL || 'https://api.dev.yubu.ai';

// Types for test assignment system
export interface ScheduleAssessmentRequest {
  student_id: number;
  assessment_id: string;
  start_date: string;
  repeat_every_value: number;
  repeat_every_unit: 'day' | 'week' | 'month';
  repeat_on?: string[];
  ends: {
    mode: 'on' | 'after' | 'never';
    after_times?: number;
    on_date?: string;
  };
}

export interface ScheduleAssessmentResponse {
  assigned_assessment_id: string;
  sessions_created: number;
}

export interface ScheduledAssessment {
  assigned_assessment_id: string;
  assessment_id: string;
  start_date: string;
  end_date?: string;
  repeat_every: string;
}

export interface AssessmentSession {
  session_id: string;
  assigned_assessment_id: string;
  assessment_id: string;
  date: string;
  status: 'planned' | 'in_progress' | 'completed';
}

export interface StudentSessionsResponse {
  student_id: number;
  sessions: AssessmentSession[];
}

export interface TodaysSessionsResponse {
  student_id: number;
  today: string;
  sessions: AssessmentSession[];
}

export interface AssignedAssessmentDetails {
  student: {
    id: number;
    name: string;
  };
  assessment: {
    assessment_id: string;
    title: string;
    scale: string;
    questions: string[];
  };
  start_date: string;
  end_date?: string;
  repeat_every: string;
}

export interface SessionAssessmentDetails {
  student: {
    id: number;
    name: string;
  };
  session_date: string;
  assessment: {
    assessment_id: string;
    title: string;
    scale: string;
    questions: string[];
  };
  assigned_assessment_id: string;
  repeat_every: string;
  start_date: string;
  end_date?: string;
}

export interface SubmitAssessmentAnswersRequest {
  session_id: string;
  answers: Array<{
    question_id: number;
    value: number;
  }>;
}

export interface SubmitAssessmentAnswersResponse {
  message: string;
  status: string;
}

// Test assignment settings interface (similar to InterventionSettings)
export interface TestAssignmentSettings {
  testId: string;
  testTitle: string;
  frequency: {
    startDate?: string;
    repeatEveryUnit?: 'day' | 'week' | 'month';
    repeatEveryValue?: number;
    repeatOn?: string[];
    endType?: 'never' | 'on' | 'after';
    endDate?: string;
    endTimes?: number;
  };
  studentId?: number;
}

const testAssignmentService = {
  // 1. Assign test to student
  async scheduleAssessment(
    request: ScheduleAssessmentRequest
  ): Promise<ScheduleAssessmentResponse> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/assessments/schedule_assessment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to schedule assessment: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      logger.info('Assessment scheduled successfully:', data);
      return data;
    } catch (error) {
      logger.error('Error scheduling assessment:', error);
      throw error;
    }
  },

  // 2. Get student's scheduled assessments
  async getScheduledAssessments(studentId: number): Promise<ScheduledAssessment[]> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/assessments/scheduled_assessments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ student_id: studentId }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Failed to get scheduled assessments: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      logger.error('Error getting scheduled assessments:', error);
      throw error;
    }
  },

  // 3. Get all student sessions
  async getStudentSessions(studentId: number): Promise<StudentSessionsResponse> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/assessments/list_student_sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ student_id: studentId }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Failed to get student sessions: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      logger.error('Error getting student sessions:', error);
      throw error;
    }
  },

  // 4. Get today's sessions for student
  async getTodaysSessions(studentId: number): Promise<TodaysSessionsResponse> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/assessments/todays_sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ student_id: studentId }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Failed to get today's sessions: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      logger.error("Error getting today's sessions:", error);
      throw error;
    }
  },

  // 5. Get assigned assessment details
  async getAssignedAssessmentDetails(
    assignedAssessmentId: string
  ): Promise<AssignedAssessmentDetails> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/assessments/get_assigned_assessment_details`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ assigned_assessment_id: assignedAssessmentId }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Failed to get assigned assessment details: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      logger.error('Error getting assigned assessment details:', error);
      throw error;
    }
  },

  // 6. Get session assessment details
  async getSessionAssessmentDetails(sessionId: string): Promise<SessionAssessmentDetails> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/assessments/get_session_assessment_details`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ session_id: sessionId }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Failed to get session assessment details: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      logger.error('Error getting session assessment details:', error);
      throw error;
    }
  },

  // 7. Submit assessment answers
  async submitAssessmentAnswers(
    request: SubmitAssessmentAnswersRequest
  ): Promise<SubmitAssessmentAnswersResponse> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/submit_assessment_answers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Failed to submit assessment answers: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      logger.info('Assessment answers submitted successfully:', data);
      return data;
    } catch (error) {
      logger.error('Error submitting assessment answers:', error);
      throw error;
    }
  },

  // Helper function to create and assign test (similar to createAndAssignIntervention)
  async createAndAssignTest(
    settings: TestAssignmentSettings,
    studentId: number
  ): Promise<{ assignedAssessmentId: string; detail: string }> {
    try {
      logger.info('Creating and assigning test with settings:', settings);

      // Convert settings to API format
      const scheduleRequest: ScheduleAssessmentRequest = {
        student_id: studentId,
        assessment_id: settings.testId,
        start_date: settings.frequency.startDate || new Date().toISOString().split('T')[0],
        repeat_every_value: settings.frequency.repeatEveryValue || 1,
        repeat_every_unit: settings.frequency.repeatEveryUnit || 'day',
        repeat_on: settings.frequency.repeatOn,
        ends: {
          mode: (settings.frequency.endType || 'never') as 'on' | 'after' | 'never',
          after_times:
            settings.frequency.endType === 'after' ? settings.frequency.endTimes : undefined,
          on_date: settings.frequency.endType === 'on' ? settings.frequency.endDate : undefined,
        },
      };

      const response = await this.scheduleAssessment(scheduleRequest);

      return {
        assignedAssessmentId: response.assigned_assessment_id,
        detail: `Test assigned successfully. Created ${response.sessions_created} sessions.`,
      };
    } catch (error) {
      logger.error('Error creating and assigning test:', error);
      throw error;
    }
  },
};

export default testAssignmentService;
