import React from 'react';
import { useGameName, useGameNames, useGameNameByInterventionId } from '../../hooks/useGameName';

/**
 * Test component to demonstrate the new game name mapping functionality
 */
export const GameNameTest: React.FC = () => {
  // Test individual game name
  const blendGameName = useGameName('blend-game--word-building');
  const mathGameName = useGameName('math-game');
  const memoryGameName = useGameName('memory-game');

  // Test multiple game names
  const gameNames = useGameNames([
    'blend-game--word-building',
    'math-game',
    'memory-game',
    'unknown-game-id',
  ]);

  // Test intervention_id based mapping (preferred method)
  const interventionGameName = useGameNameByInterventionId(
    'd5a90c0c-50c9-459d-9a47-c4abe4f356df', // Example intervention_id
    'blend-game--word-building'
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Game Name Mapping Test</h1>

      <div className="space-y-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
          <h2 className="text-lg font-semibold mb-3">Individual Game Names</h2>
          <div className="space-y-2">
            <div>
              <span className="font-medium">blend-game--word-building:</span> {blendGameName}
            </div>
            <div>
              <span className="font-medium">math-game:</span> {mathGameName}
            </div>
            <div>
              <span className="font-medium">memory-game:</span> {memoryGameName}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
          <h2 className="text-lg font-semibold mb-3">Multiple Game Names</h2>
          <div className="space-y-2">
            {Object.entries(gameNames).map(([gameId, gameName]) => (
              <div key={gameId}>
                <span className="font-medium">{gameId}:</span> {gameName}
              </div>
            ))}
          </div>
        </div>

        <div className="bg-green-50 rounded-lg shadow-sm border border-green-200 p-4">
          <h2 className="text-lg font-semibold mb-3 text-green-800">
            Intervention ID Based Mapping (Preferred)
          </h2>
          <div className="space-y-2">
            <div>
              <span className="font-medium">intervention_id:</span>{' '}
              d5a90c0c-50c9-459d-9a47-c4abe4f356df
            </div>
            <div>
              <span className="font-medium">game_id:</span> blend-game--word-building
            </div>
            <div>
              <span className="font-medium">Result:</span> {interventionGameName}
            </div>
          </div>
        </div>

        <div className="bg-blue-50 rounded-lg p-4">
          <h3 className="text-md font-semibold text-blue-800 mb-2">How it works:</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• The store automatically fetches data from /all_interventions API</li>
            <li>
              • <strong>NEW:</strong> Uses intervention_id from /todays_sessions for accurate
              mapping
            </li>
            <li>
              • Game names are mapped from intervention activities using intervention_id + game_id
            </li>
            <li>• Fallback to game_id mapping if intervention_id is not available</li>
            <li>• Fallback formatting is used for unknown game IDs</li>
            <li>• Data is cached for 5 minutes to reduce API calls</li>
            <li>• Sessions now preserve intervention_id when flattened from groups</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
