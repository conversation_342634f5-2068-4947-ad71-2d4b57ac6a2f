export interface Task {
  image: string;
  word: string;
  options: string[];
  answer: string;
}

export interface FeedbackState {
  show: boolean;
  isCorrect: boolean;
  selectedOption: string | null;
}

export interface GameStats {
  sessionId?: string;
  studentId: string | number;
  gameId: string;
  correctAnswers: number;
  incorrectAnswers: number;
  totalQuestions: number;
  completionPercentage: number;
  startTime: number;
  endTime: number | null;
  timeInSeconds?: number;
  progress?: Array<{
    step: number;
    correct: boolean;
  }>;
}

export interface GameResult {
  studentId: string;
  gameId: string;
  correctAnswers: number;
  totalQuestions: number;
  timeInSeconds: number;
}

export interface GameComponentProps {
  studentId: string;
  gameId: string;
  sessionId?: string;
  sessionDetails?: string;
  onFinish: (result: GameResult) => void;
  onFeedback?: (isCorrect: boolean) => void;
}

// Types for math game
export interface QuizQuestion {
  id: number;
  type: 'subtraction';
  visualObjects: {
    total: number;
    subtract: number;
    objectType: 'mushroom' | 'donut' | 'flower' | 'pencil' | 'backpack' | 'calculator';
  };
  equation: {
    num1: number;
    num2: number;
    result: number;
  };
}

export interface QuizState {
  currentQuestionIndex: number;
  questions: QuizQuestion[];
  score: number;
  userAnswers: (number | null)[];
  quizCompleted: boolean;
}
