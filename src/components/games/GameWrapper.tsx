import React from 'react';
import GameContent from './GameContent';
import { GameStats } from '@games/common/types';

interface GameWrapperProps {
  gameId: string;
  studentId: string;
  onFinish: (result: GameStats & { startTime: number; endTime: number }) => void;
  onFeedback?: (isCorrect: boolean) => void;
}

export const GameWrapper: React.FC<GameWrapperProps> = ({
  gameId,
  studentId,
  onFinish,
  onFeedback,
}) => {
  return (
    <GameContent
      gameId={gameId}
      studentId={studentId}
      onFinish={onFinish}
      onFeedback={onFeedback}
    />
  );
};

export default GameWrapper;
