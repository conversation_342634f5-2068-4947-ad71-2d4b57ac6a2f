import React, { useState, useEffect } from 'react';
import { GameRendererProps } from '../types';
import { MathTask2 } from '../types';
// Import zostanie użyty po stworzeniu komponentu VisualObjects w katalogu math-game2
import { Calculator } from 'lucide-react';

const MathRenderer2: React.FC<GameRendererProps> = props => {
  const {
    task,
    onAnswer,
    showFeedback = false,
    userAnswer = null,
    currentTaskIndex = 0,
    totalTasks = 1,
    stats = {
      correctAnswers: 0,
      incorrectAnswers: 0,
      totalQuestions: 1,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: '',
      studentId: '',
    },
  } = props;

  const mathTask = task as MathTask2;

  const [inputValue, setInputValue] = useState<string>('');
  const [crossedCount, setCrossedCount] = useState(0);

  const isCorrect = userAnswer === mathTask.equation.result;

  useEffect(() => {
    if (showFeedback) {
      setInputValue('');
      setCrossedCount(0);
    }
  }, [showFeedback]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const numericValue = parseInt(inputValue);
    if (!isNaN(numericValue)) {
      // Przekazujemy wartość liczbową jako drugi parametr, a pierwszy to boolean
      // określający czy odpowiedź jest poprawna
      onAnswer(numericValue === mathTask.equation.result, numericValue);
    }
  };

  const handleObjectClick = (index: number) => {
    if (crossedCount < mathTask.visualObjects.total && !showFeedback) {
      setCrossedCount(prevCount => prevCount + 1);
    }
  };

  const progressPercentage = (currentTaskIndex / totalTasks) * 100;

  return (
    <div className="flex flex-col items-center justify-center p-6 bg-green-50 rounded-lg shadow-lg max-w-2xl mx-auto">
      <div className="w-full flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Calculator className="w-6 h-6 text-green-600 mr-2" />
          <h2 className="text-2xl font-bold text-green-700">Math Quiz 2</h2>
        </div>
        <div className="flex items-center">
          <span className="text-green-700 font-semibold mr-2">
            Score: {stats.correctAnswers}/{stats.correctAnswers + stats.incorrectAnswers}
          </span>
        </div>
      </div>

      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-6">
        <div
          className="bg-green-600 h-2.5 rounded-full"
          style={{ width: `${progressPercentage}%` }}
        ></div>
      </div>

      <div className="mb-6 w-full">
        <div className="flex justify-center items-center text-3xl font-bold mb-4 space-x-4">
          <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800">
            {mathTask.equation.num1}
          </div>
          <span>×</span>
          <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800">
            {mathTask.equation.num2}
          </div>
          <span>=</span>
          {showFeedback ? (
            <div
              className={`flex items-center justify-center w-14 h-14 rounded-full border-2 ${
                isCorrect ? 'border-green-500 bg-green-100' : 'border-red-500 bg-red-100'
              }`}
            >
              {mathTask.equation.result}
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="inline-block">
              <input
                type="number"
                value={inputValue}
                onChange={handleInputChange}
                className="w-14 h-14 rounded-full border-2 border-gray-800 text-center text-3xl"
                disabled={showFeedback}
                autoFocus
              />
            </form>
          )}
        </div>
      </div>

      {/* Komponent VisualObjects zostanie użyty po jego stworzeniu */}
      <div className="flex flex-wrap justify-center gap-4 p-4 bg-white rounded-lg shadow-sm">
        {Array.from({ length: mathTask.visualObjects.total }).map((_, i) => (
          <div
            key={i}
            className={`text-4xl cursor-pointer transition-all ${i < crossedCount ? 'opacity-50 line-through' : ''}`}
            onClick={() => !showFeedback && i >= crossedCount && handleObjectClick(i)}
            style={{ userSelect: 'none' }}
          >
            {mathTask.visualObjects.objectType === 'mushroom'
              ? '🍄'
              : mathTask.visualObjects.objectType === 'donut'
                ? '🍩'
                : mathTask.visualObjects.objectType === 'flower'
                  ? '🌸'
                  : mathTask.visualObjects.objectType === 'pencil'
                    ? '✏️'
                    : mathTask.visualObjects.objectType === 'backpack'
                      ? '🎒'
                      : '🧮'}
          </div>
        ))}
      </div>

      {showFeedback && (
        <div
          className={`mt-6 p-4 rounded-lg w-full text-center ${
            isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}
        >
          <p className="text-xl font-bold">
            {isCorrect
              ? 'Great! Correct answer!'
              : 'Unfortunately, that is not the correct answer.'}
          </p>
          {!isCorrect && <p className="mt-2">The correct answer is: {mathTask.equation.result}</p>}
        </div>
      )}
    </div>
  );
};

export default MathRenderer2;
