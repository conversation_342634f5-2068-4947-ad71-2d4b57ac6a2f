import { useMutation, useQueryClient } from '@tanstack/react-query';
import { GameStats } from '@games/common/types';
import { useWebSocketStore } from '../../store/websocketStore';
import { useGameStatsStore } from '../../store/gameStatsStore';
import { clearTodaysSessionsCache } from '../activityService';
import logger from '../../utils/logger';

// WebSocket instance reference (will be set by GameWebSocket)
let wsInstance: WebSocket | null = null;

export const setWebSocketInstance = (ws: WebSocket | null) => {
  wsInstance = ws;
};

// Function to send stats via WebSocket
const sendStatsViaWebSocket = async (stats: GameStats): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (!wsInstance || wsInstance.readyState !== WebSocket.OPEN) {
      reject(new Error('WebSocket not connected'));
      return;
    }

    try {
      const message = JSON.stringify(stats);
      wsInstance.send(message);
      logger.log('[WebSocketQueries] Stats sent successfully via WebSocket');
      resolve(true);
    } catch (error) {
      logger.error('[WebSocketQueries] Error sending stats via WebSocket:', error);
      reject(error);
    }
  });
};

// React Query mutation for sending game stats
export const useSendGameStats = () => {
  const queryClient = useQueryClient();
  const { addToQueue, removeFromQueue } = useWebSocketStore();
  const addGameStats = useGameStatsStore(state => state.addGameStats);

  return useMutation({
    mutationFn: sendStatsViaWebSocket,

    onMutate: async (stats: GameStats) => {
      // Always save to local store first
      addGameStats(stats);
      logger.log('[WebSocketQueries] Stats saved to local store');
    },

    onSuccess: (success: boolean, stats: GameStats) => {
      if (success) {
        // Remove from queue if it was there
        removeFromQueue(stats);
        logger.log('[WebSocketQueries] Stats sent successfully, removed from queue');

        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: ['gameStats', stats.gameId, stats.studentId],
        });

        // Invalidate sessions to refresh progress after game completion
        queryClient.invalidateQueries({
          queryKey: ['todaysSessions'],
        });

        // Also invalidate specific student sessions
        if (stats.studentId) {
          queryClient.invalidateQueries({
            queryKey: ['todaysSessions', Number(stats.studentId)],
          });
        }

        // Invalidate session details if we have session info
        queryClient.invalidateQueries({
          queryKey: ['session'],
        });

        // Invalidate activities to refresh status
        queryClient.invalidateQueries({
          queryKey: ['activities'],
        });

        // Clear the activityService cache to force fresh data
        clearTodaysSessionsCache();

        logger.log('[WebSocketQueries] Invalidated sessions and activities queries, cleared cache');
      }
    },

    onError: (error: Error, stats: GameStats) => {
      logger.warn('[WebSocketQueries] Failed to send stats, adding to queue:', error.message);
      // Add to queue for retry later
      addToQueue(stats);
    },

    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
};

// Mutation for processing queued messages
export const useProcessQueuedMessages = () => {
  const { messageQueue, removeFromQueue, clearQueue } = useWebSocketStore();
  const sendStatsMutation = useSendGameStats();

  return useMutation({
    mutationFn: async () => {
      const queue = [...messageQueue];
      logger.log(`[WebSocketQueries] Processing ${queue.length} queued messages`);

      const results = await Promise.allSettled(queue.map(stats => sendStatsViaWebSocket(stats)));

      // Remove successfully sent messages from queue
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          removeFromQueue(queue[index]);
        }
      });

      const successCount = results.filter(r => r.status === 'fulfilled').length;
      logger.log(
        `[WebSocketQueries] Successfully sent ${successCount}/${queue.length} queued messages`
      );

      return { total: queue.length, success: successCount };
    },

    onError: error => {
      logger.error('[WebSocketQueries] Error processing queued messages:', error);
    },
  });
};

// Hook to get queue status
export const useWebSocketQueue = () => {
  const { messageQueue, getQueueLength } = useWebSocketStore();

  return {
    queue: messageQueue,
    queueLength: getQueueLength(),
    hasQueuedMessages: getQueueLength() > 0,
  };
};
