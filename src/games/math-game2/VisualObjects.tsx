import React from 'react';

interface VisualObjectsProps {
  total: number;
  crossed: number;
  objectType: 'mushroom' | 'donut' | 'flower' | 'pencil' | 'backpack' | 'calculator';
  onObjectClick: (index: number) => void;
  disabled?: boolean;
}

const objectIcons = {
  mushroom: '🍄',
  donut: '🍩',
  flower: '🌸',
  pencil: '✏️',
  backpack: '🎒',
  calculator: '🧮',
};

const VisualObjects: React.FC<VisualObjectsProps> = ({
  total,
  crossed,
  objectType,
  onObjectClick,
  disabled = false,
}) => {
  const renderObjects = () => {
    const objects = [];
    for (let i = 0; i < total; i++) {
      const isCrossed = i < crossed;
      objects.push(
        <div
          key={i}
          className={`text-4xl cursor-pointer transition-all ${
            isCrossed ? 'opacity-50 line-through' : ''
          }`}
          onClick={() => !disabled && !isCrossed && onObjectClick(i)}
          style={{ userSelect: 'none' }}
        >
          {objectIcons[objectType]}
        </div>
      );
    }
    return objects;
  };

  return (
    <div className="w-full">
      <div className="flex flex-wrap justify-center gap-4 p-4 bg-white rounded-lg shadow-sm">
        {renderObjects()}
      </div>
    </div>
  );
};

export default VisualObjects;
