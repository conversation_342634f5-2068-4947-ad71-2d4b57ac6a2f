import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { UserData } from '../services/authService';
import { api } from '../services/api';

interface AvatarResponse {
  avatar_url: string;
  avatar_visible: boolean;
}

interface UserState {
  user: UserData | null;
  setUser: (user: UserData | null) => void;
  clearUser: () => void;
  updateAvatar: (avatarUrl: string) => void;
  getAvatar: () => Promise<string>;
  loadAvatar: () => Promise<void>;
}

export const useUserStore = create<UserState>()(
  persist(
    set => ({
      user: null,
      setUser: user => set({ user }),
      clearUser: () => set({ user: null }),
      updateAvatar: (avatarUrl: string) =>
        set(state => ({
          user: state.user ? { ...state.user, avatar_url: avatarUrl } : null,
        })),
      getAvatar: async () => {
        try {
          const response = await api.get<AvatarResponse>('/users/get_avatar');
          return response.data.avatar_url;
        } catch (error) {
          window.location.href = '/dashboard/settings/avatar-creator';
          throw error;
        }
      },
      loadAvatar: async () => {
        try {
          const response = await api.get<AvatarResponse>('/users/get_avatar');
          set(state => ({
            user: state.user ? { ...state.user, avatar_url: response.data.avatar_url } : null,
          }));
        } catch (error) {
          console.error('Error loading avatar:', error);
          window.location.href = '/dashboard/settings/avatar-creator';
        }
      },
    }),
    {
      name: 'yubu-user',
    }
  )
);
