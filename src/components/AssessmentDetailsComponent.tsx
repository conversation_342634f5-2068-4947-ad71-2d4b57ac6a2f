import React from 'react';
import { AssessmentResult } from '../types/assessment';
import pinIcon from '../assets/pinIcon.svg';
import sendIcon from '../assets/sendIcon.svg';
import editIcon from '../assets/editPen1.svg';
import logger from '../utils/logger';
import deleteIcon from '../assets/deleteIcon.svg';
import { useState } from 'react';
import ChatComponent from './ChatComponent';

interface AssessmentDetailsComponentProps {
  result: AssessmentResult;
  onClose: () => void;
  onEdit?: () => void;
  onPromptClick?: (promptId: string, promptText: string) => void;
  userId?: string;
  chatId?: string;
}

const AssessmentDetailsComponent: React.FC<AssessmentDetailsComponentProps> = ({
  result,
  onClose,
  onEdit,
  onPromptClick,
  userId = '1',
  chatId = '1',
}) => {
  const [selectedPrompt, setSelectedPrompt] = useState<string | null>(null);
  const [showChat, setShowChat] = useState(false);
  const [chatPrompt, setChatPrompt] = useState<string | undefined>(undefined);

  const handlePromptClick = (promptId: string, promptText: string) => {
    setSelectedPrompt(promptId);
    if (onPromptClick) {
      onPromptClick(promptId, promptText);
    }
    setChatPrompt(promptText);
  };

  const handleChatClick = () => {
    setShowChat(true);
  };

  return (
    <>
      <div className="mt-8 border-2 border-gray-200 rounded-xl p-6 w-[1024px] relative bg-white mx-auto">
        {/* Header with title and actions */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-[24px] font-bold text-black">{result.title}</h2>
          <div className="flex gap-3">
            {onEdit && (
              <button
                onClick={onEdit}
                className="flex items-center gap-1 text-gray-700 hover:text-blue-500 transition-colors"
                title="Edit assessment"
              >
                <img src={editIcon} alt="Edit" className="w-5 h-5" />
                <span className="text-[14px]">Edit assessment</span>
              </button>
            )}
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-red-500 transition-colors"
              title="Close"
            >
              <img src={deleteIcon} alt="Close" className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Full-width border */}
        <div className="border-b border-gray-200 -mx-6 mb-6"></div>

        {/* Summary */}
        <div className="mb-6">
          <h3 className="text-[20px] font-bold text-black mb-2">Assessment Summary:</h3>
          <p className="text-gray-700">{result.summary}</p>
        </div>

        {/* Question-specific responses */}
        {result.question_responses && result.question_responses.length > 0 ? (
          <div className="mb-8">
            <h3 className="text-[20px] font-bold text-black mb-4">Detailed Assessment Results:</h3>

            {result.question_responses.map((response, index) => (
              <div
                key={`question-${response.question_id}`}
                className="mb-8 p-5 border border-gray-200 rounded-lg bg-white shadow-sm"
              >
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-[#005773] text-white rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    {/* Question and Area */}
                    <h4 className="text-[18px] font-semibold text-black mb-2">
                      {response.question}
                    </h4>
                    <div className="inline-block mb-3 px-3 py-1 bg-blue-50 text-[#005773] text-sm rounded-full">
                      Area: {response.area}
                    </div>

                    {/* Diagnosis and Impact */}
                    <div className="mb-4 p-4 bg-gray-50 rounded-md">
                      <p className="font-medium text-gray-800 mb-2">Diagnosis:</p>
                      <p className="text-gray-700 mb-3">{response.diagnosis}</p>

                      <p className="font-medium text-gray-800 mb-2">Impact on Learning:</p>
                      <p className="text-gray-700">{response.impact}</p>
                    </div>

                    {/* Recommendations */}
                    {response.recommendations && response.recommendations.length > 0 && (
                      <div className="mb-4">
                        <h5 className="text-[16px] font-semibold text-black mb-2">
                          Recommendations:
                        </h5>
                        <div className="space-y-3">
                          {response.recommendations.map((rec, recIndex) => (
                            <div key={`rec-${recIndex}`} className="p-3 bg-blue-50 rounded-md">
                              <p className="font-medium text-[#005773] mb-1">{rec.title}</p>
                              <p className="text-gray-700">{rec.description}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Activities */}
                    {response.activities && response.activities.length > 0 && (
                      <div>
                        <h5 className="text-[16px] font-semibold text-black mb-2">
                          Suggested Activities:
                        </h5>
                        <div className="space-y-4">
                          {response.activities.map((activity, actIndex) => (
                            <div
                              key={`activity-${actIndex}`}
                              className="p-4 border border-gray-200 rounded-md"
                            >
                              <p className="font-medium text-gray-800 mb-1">{activity.title}</p>
                              <p className="text-gray-700 mb-2">{activity.description}</p>

                              {activity.steps && activity.steps.length > 0 && (
                                <div className="mt-2">
                                  <p className="font-medium text-gray-700 mb-1">Steps:</p>
                                  <ol className="list-decimal ml-5 text-gray-700 space-y-1">
                                    {activity.steps.map((step, stepIndex) => (
                                      <li key={`step-${stepIndex}`}>{step}</li>
                                    ))}
                                  </ol>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-yellow-700">
              No specific issues were identified in the assessment. Consider reviewing the questions
              and answers to ensure accuracy.
            </p>
          </div>
        )}

        {/* Clickable prompts */}
        {result.prompts && result.prompts.length > 0 && (
          <div className="mb-8">
            <h3 className="text-[20px] font-bold text-black mb-4">Recommended Resources:</h3>
            <div className="grid grid-cols-2 gap-4">
              {result.prompts.map(prompt => (
                <button
                  key={prompt.id}
                  onClick={() => handlePromptClick(prompt.id, prompt.text)}
                  className={`p-4 border rounded-lg text-left transition-all ${
                    selectedPrompt === prompt.id
                      ? 'border-[#005773] bg-blue-50 shadow-md'
                      : 'border-gray-200 hover:border-[#005773] hover:shadow-sm'
                  }`}
                >
                  <h4 className="text-[16px] font-semibold text-black mb-1">{prompt.text}</h4>
                  <p className="text-gray-600 text-sm">{prompt.description}</p>
                  {prompt.area && (
                    <span className="inline-block mt-2 px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                      {prompt.area}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default AssessmentDetailsComponent;
