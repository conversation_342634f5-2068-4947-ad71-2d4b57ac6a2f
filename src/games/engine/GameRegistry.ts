import { ComponentType } from 'react';
import { GameRendererProps } from './types';

class GameRegistry {
  private renderers: Record<string, ComponentType<GameRendererProps>> = {};

  /**
   * @param type The game type identifier (e.g., 'blend', 'math')
   * @param renderer The React component that renders this game type
   */
  register(type: string, renderer: ComponentType<GameRendererProps>): void {
    this.renderers[type] = renderer;
  }

  /**
   * @param type The game type to get the renderer for
   * @returns The renderer component or undefined if not found
   */
  getRenderer(type: string): ComponentType<GameRendererProps> | undefined {
    return this.renderers[type];
  }

  /**
   * @param type The game type to check
   * @returns True if a renderer exists, false otherwise
   */
  hasRenderer(type: string): boolean {
    return !!this.renderers[type];
  }

  /**
   * @returns Array of registered game type strings
   */
  getRegisteredTypes(): string[] {
    return Object.keys(this.renderers);
  }
}

const gameRegistry = new GameRegistry();
export default gameRegistry;
