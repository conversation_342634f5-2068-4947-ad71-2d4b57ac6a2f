import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { RecentCard } from '../../components/students/RecentCard';
import { StudentsList } from '../../components/students/StudentsList';
import {
  studentService,
  StudentListItem,
  StudentRegistrationResponse,
} from '../../services/studentService';
import avatarFull from '../../assets/kid.svg';
import plusIcon from '../../assets/plus.svg';
import { useUserStore } from '../../store/userStore';
import { USER_ROLES } from '../../contants.ts';
import { useForm, SubmitHandler } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { StudentDeleteConfirmationModal } from '../../components/students/StudentDeleteConfirmationModal';
import { toast } from 'sonner';
import closeIcon from '../../assets/close-button.svg';
import { useChatStore } from '../../store/ChatStore';
import { FloatingInput } from '../../components/ui/FloatingInput';
import { Button } from '../../components/Button/Button';

type ModalType = null | 'add' | 'edit' | 'register' | 'success';

const studentSchema = z.object({
  first_name: z.string().trim().min(1, 'First name is required'),
  last_name: z.string().trim().min(1, 'Last name is required'),
  age: z.coerce
    .number({
      required_error: 'Age is required',
      invalid_type_error: 'Age must be a number',
    })
    .min(1, 'Age must be at least 1')
    .max(120, 'Age must be realistic'),
  gender: z
    .string()
    .min(1, 'Gender selection is required')
    .refine(val => ['male', 'female', 'prefer_not_to_say'].includes(val), {
      message: 'Please select a valid gender option',
    }),
  additional_info: z.string().optional(),
});

type StudentFormData = z.infer<typeof studentSchema>;

interface StudentResponseData {
  student_user_id: number;
  temporary_email: string;
  temporary_password: string;
}

export const StudentsPage: React.FC = () => {
  const { user } = useUserStore();
  const queryClient = useQueryClient();
  const [students, setStudents] = useState<StudentListItem[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [modal, setModal] = useState<ModalType>(null);
  const [editId, setEditId] = useState<number | null>(null);

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [studentToDelete, setStudentToDelete] = useState<number | null>(null);
  const [studentNameToDelete, setStudentNameToDelete] = useState<string>('');

  const { recentChats, loading: recentChatsLoading, fetchRecentChats } = useChatStore();

  useEffect(() => {
    fetchRecentChats();
  }, [fetchRecentChats]);

  const [studentResponseData, setStudentResponseData] = useState<StudentResponseData | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
  } = useForm<StudentFormData>({
    resolver: zodResolver(studentSchema),
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: {
      first_name: '',
      last_name: '',
      age: 1,
      gender: '',
      additional_info: '',
    },
  });

  const resetForm = () => {
    reset({
      first_name: '',
      last_name: '',
      age: 1,
      gender: '',
      additional_info: '',
    });
  };

  const {
    data,
    isLoading,
    error: queryError,
  } = useQuery({
    queryKey: ['students'],
    queryFn: studentService.getStudents,
  });

  const addStudentMutation = useMutation({
    mutationFn: (data: StudentFormData) => {
      const studentData = {
        ...data,
        email: `${data.first_name.toLowerCase()}.${data.last_name.toLowerCase()}@example.com`,
        password: 'temporaryPassword123!',
        role_id: 3, // Assuming 3 is the role ID for students
      };
      return studentService.addStudent(studentData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] });
    },
  });

  const editStudentMutation = useMutation({
    mutationFn: ({ id, student }: { id: number; student: StudentFormData }) =>
      studentService.editStudent(id, student),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] });
    },
  });

  const deleteStudentMutation = useMutation({
    mutationFn: (id: number) => studentService.deleteStudent(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] });
    },
  });

  const fetchStudents = async () => {
    try {
      const data = await studentService.getStudents();
      setStudents(data.students || []);
      setError(null);
    } catch {
      setError('Failed to fetch students');
    }
  };

  useEffect(() => {
    if (data) {
      setStudents(data.students || []);
    }
  }, [data]);

  useEffect(() => {
    if (queryError) {
      setError('Failed to load students. Please try again.');
    }
  }, [queryError]);

  const handleAddStudent: SubmitHandler<StudentFormData> = async data => {
    try {
      await addStudentMutation.mutateAsync(data);
      setModal(null);
      resetForm();
    } catch {
      toast.error('Failed to add student', {
        description: 'An error occurred while adding the student.',
      });
    }
  };

  const handleEditStudent: SubmitHandler<StudentFormData> = async data => {
    if (editId === null) return;

    try {
      await editStudentMutation.mutateAsync({ id: editId, student: data });
      setModal(null);
      resetForm();
      setEditId(null);
    } catch {
      setError('Failed to update student');
    }
  };

  const handleRegisterStudent: SubmitHandler<StudentFormData> = async data => {
    try {
      const studentData = {
        first_name: data.first_name,
        last_name: data.last_name,
        age: data.age,
        gender: data.gender,
        additional_info: data.additional_info || '',
        email: `${data.first_name.toLowerCase()}.${data.last_name.toLowerCase()}@example.com`,
        password: 'temporaryPassword123!',
        role_id: 3, // Assuming 3 is the role ID for students
      };

      let response: StudentRegistrationResponse;
      if (user?.role === USER_ROLES.TEACHER) {
        response = await studentService.registerStudentByTeacher(studentData);
      } else if (user?.role === USER_ROLES.PARENT) {
        response = await studentService.registerStudentByParent(studentData);
      } else {
        response = await studentService.addStudent(studentData);
      }

      if (
        response &&
        response.student_user_id &&
        response.temporary_email &&
        response.temporary_password
      ) {
        setStudentResponseData({
          student_user_id: response.student_user_id,
          temporary_email: response.temporary_email,
          temporary_password: response.temporary_password,
        });
        setModal('success');
      } else {
        setModal(null);
        resetForm();
        fetchStudents();
        toast.success('Student registered successfully');
      }
    } catch {
      toast.error('Failed to register student', {
        description: 'An error occurred while registering the student.',
      });
    }
  };

  const handleDeleteStudent = async () => {
    if (!studentToDelete) return;

    try {
      await deleteStudentMutation.mutateAsync(studentToDelete);
      setDeleteModalOpen(false);
      setStudentToDelete(null);
      toast.success('Student deleted successfully');
    } catch {
      setError('Failed to delete student');
    }
  };

  const openDeleteModal = (id: number) => {
    const student = students.find(s => s.id === id);
    if (student) {
      setStudentToDelete(id);
      setStudentNameToDelete(`${student.first_name} ${student.last_name}`);
      setDeleteModalOpen(true);
    }
  };

  const openEditModal = (student: StudentListItem) => {
    setValue('first_name', student.first_name);
    setValue('last_name', student.last_name);
    setValue('age', student.age);
    setValue('gender', student.gender);
    setValue('additional_info', student.additional_info || '');

    setEditId(student.id);
    setModal('edit');
  };

  const openRegisterModal = () => {
    resetForm();
    setModal('register');
  };

  const renderModal = () => {
    if (!modal) return null;
    const isEdit = modal === 'edit';
    const isRegister = modal === 'register';
    const isSuccess = modal === 'success';

    if (isSuccess && studentResponseData) {
      return (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="relative bg-white p-8 rounded-lg w-full max-w-lg">
            <button
              className="absolute top-4 right-4"
              onClick={() => {
                setModal(null);
                resetForm();
                fetchStudents();
              }}
            >
              <img src={closeIcon} alt="Close" className="w-[30px] h-[30px]" />
            </button>

            <div className="mb-6">
              <div className="text-sm font-medium text-gray-500 mb-2"></div>
              <h1 className="text-3xl font-bold text-gray-900">Student Created Successfully</h1>
            </div>

            <div className="mb-6">
              <div className="bg-gray-50 p-6 rounded-lg border">
                <div className="space-y-3">
                  <div>
                    <span className="font-semibold text-gray-900">Student ID:</span>
                    <span className="ml-2 text-gray-700">
                      {studentResponseData.student_user_id}
                    </span>
                  </div>
                  <div>
                    <span className="font-semibold text-gray-900">Temporary Email:</span>
                    <span className="ml-2 text-gray-700">
                      {studentResponseData.temporary_email}
                    </span>
                  </div>
                  <div>
                    <span className="font-semibold text-gray-900">Temporary Password:</span>
                    <span className="ml-2 text-gray-700">
                      {studentResponseData.temporary_password}
                    </span>
                  </div>
                </div>
              </div>
              <p className="mt-4 text-sm text-gray-600">
                Save this information to log in to the student account.
              </p>
            </div>

            <Button
              fullWidth
              onClick={() => {
                setModal(null);
                resetForm();
                fetchStudents();
              }}
            >
              CLOSE
            </Button>
          </div>
        </div>
      );
    }

    const onSubmit = isEdit
      ? handleSubmit(handleEditStudent)
      : isRegister
        ? handleSubmit(handleRegisterStudent)
        : handleSubmit(handleAddStudent);

    return (
      <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
        <div className="relative bg-white p-8 rounded-lg w-full max-w-lg max-h-[90vh] overflow-y-auto">
          <button className="absolute top-4 right-4 cursor-pointer" onClick={() => setModal(null)}>
            <img src={closeIcon} alt="Close" className="w-[30px] h-[30px]" />
          </button>

          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">
              {isEdit ? 'Edit Student' : 'Add Student'}
            </h1>
          </div>

          <form onSubmit={onSubmit} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <FloatingInput
                  id="first_name"
                  type="text"
                  label="First Name"
                  required
                  mb="mb-0"
                  {...register('first_name')}
                />
                {errors.first_name && (
                  <p className="text-red-500 text-xs mt-1">{errors.first_name.message}</p>
                )}
              </div>

              <div>
                <FloatingInput
                  id="last_name"
                  type="text"
                  label="Last Name"
                  mb="mb-0"
                  required
                  {...register('last_name')}
                />
                {errors.last_name && (
                  <p className="text-red-500 text-xs mt-1">{errors.last_name.message}</p>
                )}
              </div>
            </div>

            <div>
              <FloatingInput id="age" type="number" label="Age" required {...register('age')} />
              {errors.age && <p className="text-red-500 text-xs mt-1">{errors.age.message}</p>}
            </div>

            <div className="relative z-0 w-full mb-5 group">
              <select
                id="gender"
                {...register('gender')}
                className={`block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 appearance-none focus:outline-none focus:ring-0 focus:border-primaryBtn peer ${
                  errors.gender ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="" disabled>
                  Select gender
                </option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="prefer_not_to_say">Prefer not to say</option>
              </select>
              <label
                htmlFor="gender"
                className="peer-focus:font-medium absolute text-sm text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:start-0 peer-focus:text-primaryBtn peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:z-[-10] peer-focus:scale-75 peer-focus:z-[10] peer-focus:-translate-y-6 peer-valid:z-[10]"
              >
                Gender
              </label>
              {errors.gender && (
                <p className="text-red-500 text-xs mt-1">{errors.gender.message}</p>
              )}
            </div>

            <div className="relative z-0 w-full mb-5 group">
              <textarea
                id="additional_info"
                {...register('additional_info')}
                className="block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-primaryBtn peer"
                placeholder=" "
                rows={1}
              />
              <label
                htmlFor="additional_info"
                className="peer-focus:font-medium absolute text-sm text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:start-0 peer-focus:text-primaryBtn peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:z-[-10] peer-focus:scale-75 peer-focus:z-[10] peer-focus:-translate-y-6 peer-valid:z-[10]"
              >
                Additional Information (optional)
              </label>
            </div>

            <Button className="cursor-pointer" type="submit" fullWidth>
              {isEdit ? 'SAVE CHANGES' : 'ADD STUDENT'}
            </Button>
          </form>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-8 mt-8">
      {renderModal()}

      <StudentDeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteStudent}
        studentName={studentNameToDelete}
      />
      <section className="mb-5.5">
        <h2 className="text-[32px] leading-[32px] tracking-[0%] font-[400] font-['Fira_Sans'] text-[#5E6C84] mb-8">
          Recent
        </h2>
        <div className="flex gap-[28px] overflow-x-auto pb-4">
          {recentChats.length === 0 && !recentChatsLoading ? (
            <div className="text-gray-500">No recent chats</div>
          ) : recentChatsLoading ? (
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          ) : (
            recentChats.map(chat => (
              <RecentCard
                key={`${chat.id}-${chat.session_id}`}
                id={chat.id}
                name={`${chat.first_name} ${chat.last_name}`}
                session_id={chat.session_id}
                last_message_time={chat.last_message_time}
              />
            ))
          )}
        </div>
      </section>

      <section>
        <div className="flex gap-4 items-center mb-8">
          <h2 className="text-[24px] leading-[32px] tracking-[0%] font-[400] font-['Fira_Sans'] text-[#5E6C84]">
            List of students
          </h2>
          <div className="w-[1px] h-[24px] bg-[#E4E4E4]" />
          <div className="flex gap-2">
            <button
              onClick={openRegisterModal}
              className="flex items-center justify-center text-[14px] text-[#1C1C1C] gap-2 py-2 rounded-lg transition-colors cursor-pointer"
            >
              <img className="h-6 w-6" src={plusIcon} alt="" />
              Add student
            </button>
          </div>
        </div>

        {error && <div className="text-red-500 text-sm text-center mb-4">{error}</div>}

        {isLoading ? (
          <div className="text-center text-gray-500">Loading students...</div>
        ) : students.length === 0 ? (
          <div className="text-center text-gray-500">No students found</div>
        ) : (
          <StudentsList
            students={students.map(student => ({
              id: student.id.toString(),
              name: `${student.first_name} ${student.last_name}`,
              yearsActive: student.age,
              avatar: avatarFull,
              additionalInfo: student.additional_info,
              gender: student.gender,
            }))}
            onDelete={id => {
              openDeleteModal(id);
              return Promise.resolve();
            }}
            onEdit={(id, _data) => {
              const student = students.find(s => s.id === parseInt(id.toString()));
              if (student) {
                openEditModal(student);
              }
              return Promise.resolve();
            }}
          />
        )}
      </section>
    </div>
  );
};
