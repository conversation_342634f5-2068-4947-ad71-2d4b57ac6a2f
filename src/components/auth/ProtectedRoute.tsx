/* eslint-disable no-console */
import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { authService } from '../../services/authService';
import { useUserStore } from '../../store/userStore';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, allowedRoles }) => {
  const location = useLocation();
  const { user, setUser, loadAvatar } = useUserStore();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isValid = authService.checkToken();
        if (isValid && !user) {
          const userData = await authService.getCurrentUser();
          setUser(userData);

          if (userData && !userData.avatar_url) {
            try {
              await loadAvatar();
            } catch (avatarError) {
              console.error('Error loading avatar:', avatarError);
              window.location.href = '/dashboard/settings/avatar-creator';
            }
          }
        }
      } catch (error) {
        console.error('Authorization error:', error);
        setUser(null);
      }
    };

    checkAuth();
  }, [user, setUser, loadAvatar]);

  if (!authService.checkToken()) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (!user) {
    return <div>Ładowanie...</div>;
  }

  // Sprawdzanie, czy użytkownik ma odpowiednią rolę
  if (allowedRoles && allowedRoles.length > 0) {
    if (!allowedRoles.includes(user.role)) {
      // Przekierowanie do głównej strony dashboardu, jeśli użytkownik nie ma odpowiedniej roli
      return <Navigate to="/dashboard" replace />;
    }
  }

  return <>{children}</>;
};
