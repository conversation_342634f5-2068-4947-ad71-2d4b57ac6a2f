import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { chatService } from '../chatService';

// Pobieranie listy sesji czatu
export const useChatSessions = () => {
  return useQuery({
    queryKey: ['chatSessions'],
    queryFn: () => chatService.getSessions(),
    staleTime: 5 * 60 * 1000,
  });
};

// Rozpoczynanie nowej sesji czatu
export const useStartChatSession = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (studentId: string | number) => chatService.startSession(studentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chatSessions'] });
    },
  });
};

// Pobieranie historii czatu
export const useChatHistory = (sessionId?: string) => {
  return useQuery({
    queryKey: ['chatHistory', sessionId],
    queryFn: () => (sessionId ? chatService.getChatHistory(sessionId) : []),
    enabled: !!sessionId,
    staleTime: 2 * 60 * 1000,
  });
};
