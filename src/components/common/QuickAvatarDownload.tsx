import React, { useState } from 'react';
import { downloadAvatar, saveAvatarLocally, generateAvatarFilename } from '../../utils/avatarUtils';

interface QuickAvatarDownloadProps {
  avatarUrl: string;
  className?: string;
  children?: React.ReactNode;
}

export const QuickAvatarDownload: React.FC<QuickAvatarDownloadProps> = ({
  avatarUrl,
  className = '',
  children,
}) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const handleQuickDownload = async () => {
    setIsDownloading(true);

    try {
      const blob = await downloadAvatar(avatarUrl, {
        format: 'glb',
        quality: 'medium',
        pose: 'A',
        textureAtlas: '512',
      });
      const filename = generateAvatarFilename(avatarUrl, 'glb');
      saveAvatarLocally(blob, filename);
    } catch (error) {
      console.error('Błąd podczas szybkiego pobierania:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <button
      onClick={handleQuickDownload}
      disabled={isDownloading}
      className={`inline-flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
      title="Szybkie pobieranie (GLB, średnia jakość)"
    >
      {isDownloading ? (
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
      ) : (
        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 10v6m0 0l-3-3m3 3l3-3"
          />
        </svg>
      )}
      {children || (isDownloading ? 'Pobieranie...' : 'Pobierz')}
    </button>
  );
};
