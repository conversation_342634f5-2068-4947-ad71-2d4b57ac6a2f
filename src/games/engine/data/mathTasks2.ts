import { MathTask2 } from '../types';

export const mathTasks2: MathTask2[] = [
  {
    type: 'math-game2',
    id: 1,
    visualObjects: {
      total: 2,
      subtract: 0,
      objectType: 'donut',
    },
    equation: {
      num1: 1,
      num2: 2,
      result: 2,
    },
  },
  {
    type: 'math-game2',
    id: 2,
    visualObjects: {
      total: 6,
      subtract: 0,
      objectType: 'flower',
    },
    equation: {
      num1: 2,
      num2: 3,
      result: 6,
    },
  },
  {
    type: 'math-game2',
    id: 3,
    visualObjects: {
      total: 12,
      subtract: 0,
      objectType: 'pencil',
    },
    equation: {
      num1: 3,
      num2: 4,
      result: 12,
    },
  },
  {
    type: 'math-game2',
    id: 4,
    visualObjects: {
      total: 8,
      subtract: 0,
      objectType: 'backpack',
    },
    equation: {
      num1: 2,
      num2: 4,
      result: 8,
    },
  },
  {
    type: 'math-game2',
    id: 5,
    visualObjects: {
      total: 20,
      subtract: 0,
      objectType: 'calculator',
    },
    equation: {
      num1: 4,
      num2: 5,
      result: 20,
    },
  },
  {
    type: 'math-game2',
    id: 6,
    visualObjects: {
      total: 25,
      subtract: 0,
      objectType: 'mushroom',
    },
    equation: {
      num1: 5,
      num2: 5,
      result: 25,
    },
  },
];
