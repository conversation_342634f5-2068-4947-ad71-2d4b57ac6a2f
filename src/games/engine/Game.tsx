import React, { useState, useEffect } from 'react';
import { GameProps, GameState } from './types';
import { GameStats } from '../common/types';
import logger from '../../utils/logger';
import GameRenderer from './GameRenderer';
import { gameWebSocket } from '../common/websocket';
import GameResults from '../common/GameResults';

const Game: React.FC<GameProps> = ({
  tasks,
  onComplete,
  studentId = '',
  gameId,
  sessionId,
  onFinish,
}) => {
  const [state, setState] = useState<GameState>({
    currentTaskIndex: 0,
    completedTasks: Array(tasks.length).fill(false),
    isGameComplete: false,
    stats: {
      totalQuestions: tasks.length,
      correctAnswers: 0,
      incorrectAnswers: 0,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: gameId,
      studentId: studentId,
    },
    feedback: {
      show: false,
      isCorrect: false,
      userAnswer: null,
    },
  });

  const [animateOut, setAnimateOut] = useState(false);

  const currentTask = tasks[state.currentTaskIndex];

  useEffect(() => {
    gameWebSocket.connect();
  }, []);

  useEffect(() => {
    return () => {
      if (!state.isGameComplete && state.completedTasks.some(Boolean) && tasks.length > 0) {
        gameWebSocket.sendGameStats({
          ...state.stats,
          completionPercentage: 100,
          endTime: Date.now(),
          timeInSeconds: Math.round((Date.now() - state.stats.startTime) / 1000),
        });
      }
    };
  }, [state.isGameComplete, state.completedTasks, state.stats, tasks.length]);

  const handleAnswer = (isCorrect: boolean, userAnswer: string | number | null = null) => {
    if (state.feedback.show) return;

    const updatedCompletedTasks = [...state.completedTasks];
    updatedCompletedTasks[state.currentTaskIndex] = true;

    const correctAnswers = state.stats.correctAnswers + (isCorrect ? 1 : 0);
    const incorrectAnswers = state.stats.incorrectAnswers + (isCorrect ? 0 : 1);
    const completedTasksCount = updatedCompletedTasks.filter(Boolean).length;
    const completionPercentage = Math.round((completedTasksCount / tasks.length) * 100);
    const currentTime = Date.now();

    logger.log(
      `[Game] Answer: ${isCorrect ? 'correct' : 'incorrect'}, Completed: ${completedTasksCount}/${tasks.length}`
    );

    if (!state.stats.progress) {
      state.stats.progress = [];
    }

    const currentStep = state.currentTaskIndex + 1;
    const stepExists = state.stats.progress.some(p => p.step === currentStep);
    if (!stepExists) {
      state.stats.progress.push({
        step: currentStep,
        correct: isCorrect,
      });
    }

    const newStats: GameStats = {
      ...state.stats,
      sessionId: sessionId,
      studentId: String(studentId || 'guest-user'),
      gameId: gameId,
      correctAnswers: correctAnswers,
      incorrectAnswers: incorrectAnswers,
      completionPercentage: completionPercentage,
      endTime: currentTime,
      progress: [...state.stats.progress],
    };

    try {
      gameWebSocket.sendGameStats(newStats);
      // Stats sent via WebSocket
    } catch (error) {
      logger.error('[Game] Failed to send stats via WebSocket:', error);
    }

    setState({
      ...state,
      completedTasks: updatedCompletedTasks,
      stats: newStats,
      feedback: {
        show: true,
        isCorrect,
        userAnswer,
      },
    });

    setTimeout(() => {
      setAnimateOut(true);

      setTimeout(() => {
        const nextIndex = state.currentTaskIndex + 1;

        if (nextIndex >= tasks.length) {
          const endTime = Date.now();
          const calculatedTimeInSeconds = Math.round((endTime - state.stats.startTime) / 1000);

          const finalStats: GameStats = {
            ...newStats,
            completionPercentage: 100,
            endTime,
            timeInSeconds: calculatedTimeInSeconds,
          };

          // Game completed with stats

          // Zapisujemy statystyki w store i wysyłamy przez WebSocket
          try {
            gameWebSocket.sendGameStats(finalStats);
            // Final stats sent via WebSocket
          } catch (error) {
            logger.error('[Game] Failed to send final stats via WebSocket:', error);
          }

          if (onComplete) {
            // Calling onComplete callback
            onComplete(finalStats);
          }

          if (onFinish) {
            // Calling onFinish callback
            onFinish(finalStats);
          }

          setState({
            ...state,
            isGameComplete: true,
            stats: finalStats,
            feedback: {
              show: false,
              isCorrect: false,
              userAnswer: null,
            },
          });
        } else {
          setState({
            ...state,
            currentTaskIndex: nextIndex,
            completedTasks: updatedCompletedTasks,
            stats: newStats,
            feedback: {
              show: false,
              isCorrect: false,
              userAnswer: null,
            },
          });
        }

        setAnimateOut(false);
      }, 500);
    }, 1500);
  };

  const handleRestart = () => {
    setState({
      currentTaskIndex: 0,
      completedTasks: Array(tasks.length).fill(false),
      isGameComplete: false,
      stats: {
        totalQuestions: tasks.length,
        correctAnswers: 0,
        incorrectAnswers: 0,
        completionPercentage: 0,
        startTime: Date.now(),
        endTime: null,
        gameId: gameId,
        studentId: studentId,
      },
      feedback: {
        show: false,
        isCorrect: false,
        userAnswer: null,
      },
    });
  };

  const renderGameComplete = () => {
    // Calculate time in seconds if not already available
    const timeInSeconds =
      state.stats.timeInSeconds ||
      (state.stats.endTime ? Math.round((state.stats.endTime - state.stats.startTime) / 1000) : 0);

    // Update statistics with time in seconds if not previously set
    const updatedStats = {
      ...state.stats,
      timeInSeconds: timeInSeconds,
    };

    return <GameResults stats={updatedStats} onRestart={handleRestart} />;
  };

  return (
    <div className="min-h-screen w-full py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {!state.isGameComplete ? (
          <div
            className={`transition-all duration-500 transform ${animateOut ? 'opacity-0 scale-95' : 'opacity-100 scale-100'}`}
          >
            <GameRenderer
              task={currentTask}
              onAnswer={handleAnswer}
              showFeedback={state.feedback.show}
              userAnswer={state.feedback.userAnswer}
              isCorrect={state.feedback.isCorrect}
              currentTaskIndex={state.currentTaskIndex}
              totalTasks={tasks.length}
              stats={state.stats}
              sessionId={sessionId} // Pass sessionId
              onRestart={handleRestart}
            />
          </div>
        ) : (
          renderGameComplete()
        )}
      </div>
    </div>
  );
};

export default Game;
