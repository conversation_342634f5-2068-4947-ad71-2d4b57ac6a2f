import React from 'react';
import { InterventionItem } from '../../pages/chat/utils/chatUtils';
import GameInterventionsComponent from '../games/GameInterventionsComponent';

// Import game images
import game1Image from '../../assets/game1.png';
import game2Image from '../../assets/game2.png';
import game3Image from '../../assets/game3.png';

interface InterventionsFromIdsComponentProps {
  interventions: InterventionItem[];
  studentId?: number;
  gradeLevel?: string;
}

// Mapowanie intervention_id na typ gry i dane
const interventionIdToGameData: Record<
  string,
  {
    type: string;
    title: string;
    description: string;
    exerciseTime: string;
    imageUrl: string;
    areaId: string;
    areaName: string;
  }
> = {
  'math-game': {
    type: 'math',
    title: 'Math Game',
    description: 'Practice math skills with interactive exercises.',
    exerciseTime: '10 min.',
    imageUrl: game2Image,
    areaId: 'math',
    areaName: 'Math',
  },
  'math-game1': {
    type: 'math',
    title: 'Advanced Math Game',
    description: 'Advanced math exercises for skill building.',
    exerciseTime: '15 min.',
    imageUrl: game2Image,
    areaId: 'math',
    areaName: 'Math',
  },
  'memory-game': {
    type: 'memory',
    title: 'Memory Game',
    description: 'Enhance memory skills with fun activities.',
    exerciseTime: '8 min.',
    imageUrl: game3Image,
    areaId: 'memory',
    areaName: 'Memory',
  },
  'memory-game1': {
    type: 'memory',
    title: 'Memory Challenge',
    description: 'Challenge your memory with these exercises.',
    exerciseTime: '12 min.',
    imageUrl: game3Image,
    areaId: 'memory',
    areaName: 'Memory',
  },
  'memory-game2': {
    type: 'memory',
    title: 'Advanced Memory Game',
    description: 'Advanced memory exercises for enhanced cognitive skills.',
    exerciseTime: '15 min.',
    imageUrl: game3Image,
    areaId: 'memory',
    areaName: 'Memory',
  },
  'math-game2': {
    type: 'math',
    title: 'Math Challenge',
    description: 'Challenging math problems for advanced learners.',
    exerciseTime: '20 min.',
    imageUrl: game2Image,
    areaId: 'math',
    areaName: 'Math',
  },
  'blend-game': {
    type: 'blend',
    title: 'Blending Game',
    description: 'Practice blending sounds and letters.',
    exerciseTime: '5 min.',
    imageUrl: game1Image,
    areaId: 'blend',
    areaName: 'Blending',
  },
  'reading-game': {
    type: 'blend',
    title: 'Reading Game',
    description: 'Improve reading skills with interactive activities.',
    exerciseTime: '10 min.',
    imageUrl: game1Image,
    areaId: 'reading',
    areaName: 'Reading',
  },
};

const InterventionsFromIdsComponent: React.FC<InterventionsFromIdsComponentProps> = ({
  interventions,
  studentId,
  gradeLevel = '1st grade',
}) => {
  // Convert all interventions to games without grouping by area
  const allGames = interventions
    .map(intervention => {
      const gameData = interventionIdToGameData[intervention.intervention_id];
      if (gameData) {
        return {
          id: Math.random(), // Generate random ID
          type: gameData.type,
          title: gameData.title,
          description: gameData.description,
          exerciseTime: gameData.exerciseTime,
          imageUrl: gameData.imageUrl,
          game_id: intervention.intervention_id, // Pass intervention_id as game_id
        };
      }
      return null;
    })
    .filter(game => game !== null) as Array<{
    id: number;
    type: string;
    title: string;
    description: string;
    exerciseTime: string;
    imageUrl: string;
    game_id: string;
  }>;

  // If there are no interventions, don't render anything
  if (allGames.length === 0) {
    return null;
  }

  return (
    <div className="mt-4">
      <GameInterventionsComponent
        areaId="interventions"
        areaName="Interventions"
        studentId={studentId}
        gradeLevel={gradeLevel}
        predefinedGames={allGames}
      />
    </div>
  );
};

export default InterventionsFromIdsComponent;
