import React, { useState, useEffect, useRef, useCallback } from 'react';
import { chatService, Message } from '../services/chatService';
import logger from '../utils/logger';

interface ChatComponentProps {
  userId: string;
  chatId: string;
  initialMessage?: string;
}

const ChatComponent: React.FC<ChatComponentProps> = ({ userId, chatId, initialMessage }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [connectionStatus, setConnectionStatus] = useState<
    'connecting' | 'connected' | 'disconnected'
  >('disconnected');

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleWebSocketMessage = useCallback((message: Message) => {
    logger.log('Received message via WebSocket:', message);
    setMessages(prev => [...prev, message]);
    setIsLoading(false);
  }, []);

  useEffect(() => {
    setConnectionStatus('connecting');

    try {
      chatService.connectWebSocket(userId, chatId, handleWebSocketMessage);

      setTimeout(() => {
        setConnectionStatus('connected');
      }, 500);

      if (initialMessage) {
        setTimeout(() => {
          handleSendMessage(initialMessage);
        }, 800);
      }
    } catch (error) {
      logger.error('Error initializing WebSocket:', error);
      setConnectionStatus('disconnected');
    }

    return () => {
      chatService.disconnectWebSocket();
      setConnectionStatus('disconnected');
    };
  }, [userId, chatId]);

  const handleSendMessage = async (text?: string) => {
    const messageToSend = text || inputText;
    if (!messageToSend.trim()) {
      return;
    }

    const userMessage: Message = {
      id: Date.now(),
      text: messageToSend.trim(),
      sender: 'user',
      timestamp: new Date().toISOString(),
      type: 'text',
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      // Send message via WebSocket
      await chatService.sendMessage(userId, chatId, messageToSend.trim());
    } catch (error) {
      logger.error('Error sending message:', error);
      setIsLoading(false);

      const errorMessage: Message = {
        id: Date.now() + 1,
        text: "Sorry, I couldn't send your message. Please try again.",
        sender: 'system',
        timestamp: new Date().toISOString(),
        type: 'text',
      };

      setMessages(prev => [...prev, errorMessage]);
    }
  };

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
        {messages.map(message => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[70%] p-3 rounded-lg ${
                message.sender === 'user'
                  ? 'bg-[#005773] text-white rounded-tr-none'
                  : 'bg-white border border-gray-200 shadow-sm rounded-tl-none'
              }`}
            >
              <p className="whitespace-pre-wrap break-words">{message.text}</p>
              <p
                className={`text-xs mt-1 ${message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'}`}
              >
                {new Date(message.timestamp).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </p>
            </div>
          </div>
        ))}
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-white border border-gray-200 p-3 rounded-lg rounded-tl-none shadow-sm">
              <div className="flex space-x-2">
                <div
                  className="w-2 h-2 rounded-full bg-gray-300 animate-bounce"
                  style={{ animationDelay: '0ms' }}
                ></div>
                <div
                  className="w-2 h-2 rounded-full bg-gray-300 animate-bounce"
                  style={{ animationDelay: '150ms' }}
                ></div>
                <div
                  className="w-2 h-2 rounded-full bg-gray-300 animate-bounce"
                  style={{ animationDelay: '300ms' }}
                ></div>
              </div>
            </div>
          </div>
        )}
        {connectionStatus !== 'connected' && (
          <div className="text-center py-2">
            <span
              className={`text-sm px-3 py-1 rounded-full ${
                connectionStatus === 'connecting'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {connectionStatus === 'connecting' ? 'Connecting to chat...' : 'Disconnected'}
            </span>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input area */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <div className="flex items-center">
          <input
            type="text"
            value={inputText}
            onChange={e => setInputText(e.target.value)}
            onKeyPress={e => e.key === 'Enter' && handleSendMessage()}
            placeholder="Type your message..."
            className="flex-1 p-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-[#005773] focus:border-transparent"
            disabled={isLoading}
          />
          <button
            onClick={() => handleSendMessage()}
            disabled={isLoading || !inputText.trim()}
            className="bg-[#005773] text-white p-3 rounded-r-lg hover:bg-[#00485f] transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatComponent;
