import React, { ReactNode } from 'react';
interface Props {
  children: ReactNode;
  isVisible: boolean;
  direction: 'next' | 'previous' | 'none';
}
export const QuestionTransition: React.FC<Props> = ({ children, isVisible, direction }) => {
  const transform = !isVisible
    ? direction === 'next'
      ? 'translate-x-full opacity-0'
      : direction === 'previous'
        ? '-translate-x-full opacity-0'
        : 'opacity-0'
    : 'translate-x-0 opacity-100';
  return (
    <div className={`transition-all duration-500 ease-in-out transform ${transform}`}>
      {children}
    </div>
  );
};
