import { WordBuildingTask } from '../engine/types';

// Sample tasks for Word-Building Puzzles game
export const wordBuildingTasks: WordBuildingTask[] = [
  {
    type: 'wordbuilding',
    id: 1,
    targetWord: 'CAT',
    image: 'https://d3dttmvg0d6qxx.cloudfront.net/cat.png',
    availableLetters: ['C', 'A', 'T', 'D', 'O', 'G'],
    difficulty: 'easy',
    hint: 'A domestic animal that meows.',
  },
  {
    type: 'wordbuilding',
    id: 2,
    targetWord: 'DOG',
    image: 'https://d3dttmvg0d6qxx.cloudfront.net/dog.png',
    availableLetters: ['D', 'O', 'G', 'C', 'A', 'T'],
    difficulty: 'easy',
    hint: 'A domestic animal that barks.',
  },
  {
    type: 'wordbuilding',
    id: 3,
    targetWord: 'FISH',
    image: 'https://d3dttmvg0d6qxx.cloudfront.net/fish.png',
    availableLetters: ['F', 'I', 'S', 'H', 'B', 'T', 'R'],
    difficulty: 'medium',
    hint: 'An animal that lives in water.',
  },
  {
    type: 'wordbuilding',
    id: 4,
    targetWord: 'BIRD',
    image: 'https://d3dttmvg0d6qxx.cloudfront.net/bird.png',
    availableLetters: ['B', 'I', 'R', 'D', 'C', 'A', 'T'],
    difficulty: 'medium',
    hint: 'An animal that flies and has feathers.',
  },
  {
    type: 'wordbuilding',
    id: 5,
    targetWord: 'APPLE',
    image: 'https://d3dttmvg0d6qxx.cloudfront.net/apple.png',
    availableLetters: ['A', 'P', 'P', 'L', 'E', 'B', 'C', 'D'],
    difficulty: 'medium',
    hint: 'A red or green fruit.',
  },
  {
    type: 'wordbuilding',
    id: 6,
    targetWord: 'PENCIL',
    image: 'https://d3dttmvg0d6qxx.cloudfront.net/pencil.png',
    availableLetters: ['P', 'E', 'N', 'C', 'I', 'L', 'A', 'B', 'D'],
    difficulty: 'hard',
    hint: 'A tool for writing and drawing.',
  },
  {
    type: 'wordbuilding',
    id: 7,
    targetWord: 'SCHOOL',
    image: 'https://d3dttmvg0d6qxx.cloudfront.net/school.png',
    availableLetters: ['S', 'C', 'H', 'O', 'O', 'L', 'B', 'A', 'T'],
    difficulty: 'hard',
    hint: 'A place where children learn.',
  },
  {
    type: 'wordbuilding',
    id: 8,
    targetWord: 'BOOK',
    image: 'https://d3dttmvg0d6qxx.cloudfront.net/book.png',
    availableLetters: ['B', 'O', 'O', 'K', 'C', 'A', 'T'],
    difficulty: 'medium',
    hint: 'A thing with pages for reading.',
  },
  {
    type: 'wordbuilding',
    id: 9,
    targetWord: 'HOUSE',
    image: 'https://d3dttmvg0d6qxx.cloudfront.net/house.png',
    availableLetters: ['H', 'O', 'U', 'S', 'E', 'M', 'N', 'T'],
    difficulty: 'hard',
    hint: 'A building where people live.',
  },
  {
    type: 'wordbuilding',
    id: 10,
    targetWord: 'SUN',
    image: 'https://d3dttmvg0d6qxx.cloudfront.net/sun.png',
    availableLetters: ['S', 'U', 'N', 'M', 'O', 'P'],
    difficulty: 'easy',
    hint: 'Shines in the sky during the day.',
  },
];

export default wordBuildingTasks;
