import React, { useEffect, useState, useRef, Suspense, useMemo } from 'react';
import { Canvas, useFrame, ThreeElements, useThree } from '@react-three/fiber';
import { OrbitControls, useProgress, useGLTF, useAnimations } from '@react-three/drei';
import * as THREE from 'three';
import { motion } from 'framer-motion';

interface Model3DProps {
  showFeedback?: boolean;
  isCorrect?: boolean;
  onFeedback?: (isCorrect: boolean) => void;
  avatarUrl?: string;
  useCustomAvatar?: boolean;
}

const positiveMessages = [
  'Great!',
  'Excellent work!',
  'Well done!',
  'Perfect!',
  'Fantastic!',
  'Amazing!',
  'Brilliant!',
  'Outstanding!',
  'Superb!',
  'Good job!',
];

const negativeMessages = [
  'Not this time...',
  'Try again!',
  'Almost!',
  "Next time you'll get it!",
  "Don't give up!",
  'Keep practicing!',
  'Almost there!',
  'Close!',
  'Just a bit more!',
  'Almost got it!',
];

const SpeechBubble: React.FC<{ isVisible: boolean; message: string }> = ({
  isVisible,
  message,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
      transition={{ duration: 0.3 }}
      className="absolute top-[100px] left-1/2 transform -translate-x-1/2 -translate-y-full bg-white border rounded-lg shadow-lg p-4 text-center min-w-[200px]"
      style={{ zIndex: 1000 }}
    >
      <div className="text-lg font-semibold text-gray-800">{message}</div>
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 rotate-45 w-4 h-4 bg-white"></div>
    </motion.div>
  );
};

const LoadingScreen: React.FC = () => {
  const { progress } = useProgress();
  return (
    <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75">
      <div className="text-center">
        <div className="text-lg font-semibold text-gray-800">Loading...</div>
        <div className="mt-2 text-sm text-gray-600">{Math.round(progress)}%</div>
      </div>
    </div>
  );
};

const Scene: React.FC<Model3DProps> = ({
  showFeedback,
  isCorrect,
  onFeedback,
  avatarUrl,
  useCustomAvatar,
}) => {
  const modelPath = useCustomAvatar && avatarUrl ? avatarUrl : '/yubu_.glb';

  const { scene, animations } = useGLTF(modelPath, true);
  const [_currentAnimation, setCurrentAnimation] = useState<string | null>(null);
  const modelRef = useRef<THREE.Group>(null);
  const [modelError, setModelError] = useState(false);

  const fallbackModel = useGLTF(modelPath, true);
  const finalScene = modelError ? fallbackModel.scene : scene;
  const finalAnimations = modelError ? fallbackModel.animations : animations;

  const { animations: walkAnimation } = useGLTF('/animations/M_Walk_001.glb', true);
  const { animations: danceAnimation1 } = useGLTF('/animations/F_Dances_001.glb', true);
  const { animations: danceAnimation2 } = useGLTF('/animations/F_Dances_004.glb', true);
  const { animations: danceAnimation3 } = useGLTF('/animations/F_Dances_005.glb', true);
  const { animations: danceAnimation4 } = useGLTF('/animations/F_Dances_006.glb', true);
  const { animations: danceAnimation5 } = useGLTF('/animations/M_Dances_001.glb', true);
  const { animations: danceAnimation6 } = useGLTF('/animations/M_Dances_002.glb', true);
  const { animations: danceAnimation7 } = useGLTF('/animations/M_Dances_003.glb', true);

  const { animations: idleAnimation1 } = useGLTF('/animations/M_Talking_Variations_010.glb', true);
  const { animations: idleAnimation2 } = useGLTF(
    '/animations/F_Standing_Idle_Variations_001.glb',
    true
  );

  const { animations: idleAnimation3 } = useGLTF('/animations/M_Talking_Variations_007.glb', true);
  const { animations: idleAnimation4 } = useGLTF('/animations/M_Talking_Variations_008.glb', true);
  const { animations: idleAnimation5 } = useGLTF('/animations/F_Talking_Variations_003.glb', true);
  const { animations: idleAnimation6 } = useGLTF('/animations/M_Talking_Variations_010.glb', true);

  const allAnimations = [
    ...(walkAnimation.length > 0 ? [walkAnimation[0]] : []),
    ...(danceAnimation1.length > 0 ? [danceAnimation1[0]] : []),
    ...(danceAnimation2.length > 0 ? [danceAnimation2[0]] : []),
    ...(danceAnimation3.length > 0 ? [danceAnimation3[0]] : []),
    ...(danceAnimation4.length > 0 ? [danceAnimation4[0]] : []),
    ...(danceAnimation5.length > 0 ? [danceAnimation5[0]] : []),
    ...(danceAnimation6.length > 0 ? [danceAnimation6[0]] : []),
    ...(danceAnimation7.length > 0 ? [danceAnimation7[0]] : []),

    ...(idleAnimation1.length > 0 ? [idleAnimation1[0]] : []),
    ...(idleAnimation2.length > 0 ? [idleAnimation2[0]] : []),
    ...(idleAnimation3.length > 0 ? [idleAnimation3[0]] : []),
    ...(idleAnimation4.length > 0 ? [idleAnimation4[0]] : []),
    ...(idleAnimation5.length > 0 ? [idleAnimation5[0]] : []),
    ...(idleAnimation6.length > 0 ? [idleAnimation6[0]] : []),
  ];

  const { actions } = useAnimations(allAnimations, modelRef);
  const [currentAnimationName, setCurrentAnimationName] = useState<string>('');

  const idleAnimationNames = useMemo(
    () => ['F_Standing_Idle_Variations_001'].filter(name => actions && actions[name]),
    [actions]
  );

  useEffect(() => {
    if (useCustomAvatar && avatarUrl) {
      const handleError = () => {
        setModelError(true);
      };

      if (!scene || !animations.length) {
        handleError();
      }
    }
  }, [scene, animations, useCustomAvatar, avatarUrl]);

  useEffect(() => {
    if (!actions || Object.keys(actions).length === 0) {
      return;
    }

    let animationToPlay: string | null = null;

    if (showFeedback === true && typeof isCorrect === 'boolean') {
      if (useCustomAvatar) {
        if (isCorrect) {
          const danceAnimations = [
            'F_Dances_001',
            'F_Dances_004',
            'F_Dances_005',
            'F_Dances_006',
            'M_Dances_001',
            'M_Dances_002',
            'M_Dances_003',
          ];

          const availableDanceAnimations = danceAnimations.filter(
            name => actions[name] !== undefined
          );

          if (availableDanceAnimations.length > 0) {
            const randomIndex = Math.floor(Math.random() * availableDanceAnimations.length);
            animationToPlay = availableDanceAnimations[randomIndex];
          }
        } else {
          if (actions['F_Talking_Variations_003']) {
            animationToPlay = 'F_Talking_Variations_003';
          } else if (idleAnimationNames.length > 0) {
            const randomIndex = Math.floor(Math.random() * idleAnimationNames.length);
            animationToPlay = idleAnimationNames[randomIndex];
          }
        }

        if (!animationToPlay) {
          const availableAnimations = Object.keys(actions);
          if (availableAnimations.length > 0) {
            animationToPlay = availableAnimations[0];
          }
        }
      } else {
        const idleAnim =
          finalAnimations.find(anim => anim.name === 'YUBU_Talking On Phone') || finalAnimations[0];
        animationToPlay = idleAnim?.name || '';
      }

      if (onFeedback) {
        onFeedback(isCorrect);
      }
    } else {
      if (useCustomAvatar) {
        if (idleAnimationNames.length > 0) {
          animationToPlay = idleAnimationNames[0];
        } else {
          const availableAnimations = Object.keys(actions);
          if (availableAnimations.length > 0) {
            animationToPlay = availableAnimations[0];
          }
        }
      } else {
        const idleAnim =
          finalAnimations.find(anim => anim.name === 'YUBU_Talking On Phone') || finalAnimations[0];
        animationToPlay = idleAnim?.name || '';
      }
    }

    if (animationToPlay && actions[animationToPlay]) {
      Object.values(actions).forEach(action => {
        if (action) {
          action.fadeOut(0.3);
        }
      });

      const newAction = actions[animationToPlay];
      if (newAction) {
        newAction.reset().fadeIn(0.3).play();
        setCurrentAnimationName(animationToPlay);

        if (idleAnimationNames.includes(animationToPlay) && !showFeedback) {
          newAction.setLoop(THREE.LoopRepeat, Infinity);
        } else if (showFeedback) {
          newAction.setLoop(THREE.LoopOnce, 1);
          newAction.clampWhenFinished = true;
        }
      }
    }
  }, [
    showFeedback,
    isCorrect,
    finalAnimations,
    onFeedback,
    useCustomAvatar,
    actions,
    idleAnimationNames,
  ]);

  return (
    <>
      <primitive
        ref={modelRef}
        // eslint-disable-next-line react/no-unknown-property
        object={finalScene as unknown as ThreeElements['primitive']['object']}
        scale={useCustomAvatar ? 1.05 : 1.2}
        // eslint-disable-next-line react/no-unknown-property
        position={useCustomAvatar ? [0, -0.5, 0] : ([0, -1.5, 0] as [number, number, number])}
        // eslint-disable-next-line react/no-unknown-property
        onUpdate={(_self: THREE.Object3D) => {}}
      />
      <OrbitControls enableZoom={false} enablePan={false} />
      <ambientLight
        // eslint-disable-next-line react/no-unknown-property
        intensity={useCustomAvatar ? 0.8 : 0.5}
      />
      <directionalLight
        // eslint-disable-next-line react/no-unknown-property
        position={[10, 10, 5]}
        // eslint-disable-next-line react/no-unknown-property
        intensity={useCustomAvatar ? 1.5 : 1}
      />
      {useCustomAvatar && (
        <directionalLight
          // eslint-disable-next-line react/no-unknown-property
          position={[-5, 5, 3]}
          // eslint-disable-next-line react/no-unknown-property
          intensity={0.8}
        />
      )}
    </>
  );
};

const Model3D: React.FC<Model3DProps> = ({
  showFeedback,
  isCorrect,
  onFeedback,
  avatarUrl,
  useCustomAvatar,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [message, setMessage] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const { progress } = useProgress();
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (showFeedback === true && typeof isCorrect === 'boolean') {
      const messages = isCorrect ? positiveMessages : negativeMessages;
      const randomMessageIndex = Math.floor(Math.random() * messages.length);
      setMessage(messages[randomMessageIndex]);
    } else {
      setMessage('');
    }
  }, [showFeedback, isCorrect]);

  useEffect(() => {
    const handleContextLost = (event: Event) => {
      event.preventDefault();
      setTimeout(() => {
        if (canvasRef.current) {
          const gl = canvasRef.current.getContext('webgl');
          if (gl) {
            setError(null);
          }
        }
      }, 1000);
    };

    const canvas = canvasRef.current;
    if (canvas) {
      canvas.addEventListener('webglcontextlost', handleContextLost);
    }

    return () => {
      if (canvas) {
        canvas.removeEventListener('webglcontextlost', handleContextLost);
      }
    };
  }, []);

  if (error) {
    return (
      <div className="relative rounded-lg overflow-hidden min-w-[250px] bg-red-50 p-4 text-center">
        <div className="text-red-600">{error}</div>
      </div>
    );
  }

  return (
    <div className="relative rounded-lg overflow-hidden min-w-[250px] max-h-[650px]">
      {isLoading && progress < 100 && <LoadingScreen />}

      <SpeechBubble isVisible={showFeedback === true} message={message} />
      <Canvas
        style={{
          opacity: progress < 100 ? '0' : '1',
        }}
        ref={canvasRef}
        camera={{ position: [0, 0, 6], fov: 45 }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: 'high-performance',
          preserveDrawingBuffer: true,
          premultipliedAlpha: false,
          failIfMajorPerformanceCaveat: false,
          stencil: false,
          depth: true,
          logarithmicDepthBuffer: false,
        }}
        dpr={[1, 1.5]}
        frameloop="always"
        onCreated={({ gl, scene: _canvasScene, camera: _camera }) => {
          const renderer = gl as THREE.WebGLRenderer;
          renderer.setClearColor(0x000000, 0);
          renderer.setClearAlpha(0);
          renderer.info.autoReset = false;
          renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5));
          setIsLoading(false);
        }}
        onError={_error => {}}
      >
        <Suspense
          fallback={
            <mesh>
              {/* eslint-disable-next-line react/no-unknown-property */}
              <boxGeometry args={[1, 1, 1] as [number, number, number]} />
              <meshStandardMaterial color="hotpink" />
            </mesh>
          }
        >
          <Scene
            showFeedback={showFeedback}
            isCorrect={isCorrect}
            onFeedback={onFeedback}
            avatarUrl={avatarUrl}
            useCustomAvatar={useCustomAvatar}
          />
        </Suspense>
      </Canvas>
    </div>
  );
};

export default Model3D;
