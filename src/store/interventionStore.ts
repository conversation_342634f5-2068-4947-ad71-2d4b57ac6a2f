import { create } from 'zustand';
import { api } from '../services/api';
import logger from '../utils/logger';
import { GameIntervention, GameActivity } from '../types/gameIntervention';

// Type alias for consistency with existing code
type InterventionActivity = GameActivity;

interface Intervention {
  id: string;
  title: string;
  activities: InterventionActivity[];
}

interface InterventionState {
  interventions: Intervention[];
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;
  lastFetched: number | null;

  fetchInterventions: () => Promise<void>;
  getGameNameById: (gameId: string) => string;
  getGameNameByInterventionId: (interventionId: string, gameId: string) => string;
  clearCache: () => void;
}

// Cache duration: 5 minutes
const CACHE_DURATION = 5 * 60 * 1000;

// Fallback game names mapping
const FALLBACK_GAME_NAMES: Record<string, string> = {
  'blend-game--quiz': 'Blend Activity',
  'blend-game--word-building': 'Word Building Game',
  'blend-game': 'Blend Game',
  'math-game': 'Math Quiz',
  'memory-game': 'Memory Game',
  'reading-game': 'Reading Game',
};

// Helper function to format game ID as title
const formatGameTitle = (gameId: string): string => {
  if (!gameId) return 'Unknown Activity';

  // Remove prefixes like 'game-' if they exist
  let title = gameId.replace(/^game-/, '');

  // Replace hyphens and underscores with spaces
  title = title.replace(/[-_]/g, ' ');

  // Capitalize first letter of each word
  return title.replace(/\b\w/g, l => l.toUpperCase());
};

export const useInterventionStore = create<InterventionState>((set, get) => ({
  interventions: [],
  isLoaded: false,
  isLoading: false,
  error: null,
  lastFetched: null,

  fetchInterventions: async () => {
    const state = get();

    // Check if we have cached data that's still valid
    if (state.isLoaded && state.lastFetched && Date.now() - state.lastFetched < CACHE_DURATION) {
      logger.log('[InterventionStore] Using cached intervention data');
      return;
    }

    if (state.isLoading) {
      logger.log('[InterventionStore] Already loading interventions');
      return;
    }

    set({ isLoading: true, error: null });

    try {
      logger.log('[InterventionStore] Fetching interventions from API');
      const response = await api.get('games/all_interventions');
      const data = response.data;

      if (!Array.isArray(data)) {
        throw new Error('Invalid response format: expected array');
      }

      // Transform the data to our format
      const interventions: Intervention[] = data.map((intervention: GameIntervention) => ({
        id: intervention.id?.toString() || '',
        title: intervention.title || '',
        activities: intervention.activities || [],
      }));

      set({
        interventions,
        isLoaded: true,
        isLoading: false,
        error: null,
        lastFetched: Date.now(),
      });

      logger.log(`[InterventionStore] Successfully loaded ${interventions.length} interventions`);
    } catch (error) {
      logger.error('[InterventionStore] Error fetching interventions:', error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch interventions',
      });
    }
  },

  getGameNameById: (gameId: string): string => {
    const state = get();

    if (!gameId) return 'Unknown Activity';

    // First try to find in loaded interventions
    for (const intervention of state.interventions) {
      for (const activity of intervention.activities) {
        if (activity.game_id === gameId) {
          return activity.title || formatGameTitle(gameId);
        }
      }
    }

    // Fallback to predefined names
    if (FALLBACK_GAME_NAMES[gameId]) {
      return FALLBACK_GAME_NAMES[gameId];
    }

    // Last resort: format the game ID
    return formatGameTitle(gameId);
  },

  getGameNameByInterventionId: (interventionId: string, gameId: string): string => {
    const state = get();

    if (!interventionId || !gameId) {
      return state.getGameNameById(gameId);
    }

    // Find the specific intervention and activity
    const intervention = state.interventions.find(i => i.id === interventionId);
    if (intervention) {
      const activity = intervention.activities.find(a => a.game_id === gameId);
      if (activity && activity.title) {
        return activity.title;
      }
    }

    // Fallback to game ID based lookup
    return state.getGameNameById(gameId);
  },

  clearCache: () => {
    set({
      interventions: [],
      isLoaded: false,
      isLoading: false,
      error: null,
      lastFetched: null,
    });
  },
}));
