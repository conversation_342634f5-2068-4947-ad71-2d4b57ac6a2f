import React, { ReactNode } from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
  title?: string;
  width?: number;
  height?: number;
  radius?: number;
  size?: 'small' | 'medium' | 'large';
  showCloseButton?: boolean;
  hideCloseButton?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  children,
  title,
  width,
  height,
  radius = 20,
  size = 'medium',
  showCloseButton = true,
  hideCloseButton = false,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div
        className="bg-white overflow-hidden shadow-lg relative"
        style={{
          width: width
            ? `${width}px`
            : size === 'small'
              ? '500px'
              : size === 'medium'
                ? '878px'
                : '900px',
          height: height
            ? `${height}px`
            : size === 'medium'
              ? '883px'
              : size === 'small'
                ? '500px'
                : '700px',
          maxHeight: '90vh',
          borderRadius: `${radius}px`,
        }}
      >
        {showCloseButton && !hideCloseButton && (
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 6L6 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        )}
        {title && <h2 className="text-xl font-semibold p-4 border-b border-gray-200">{title}</h2>}
        {children}
      </div>
    </div>
  );
};

export default Modal;
