import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import arrowBottom from '../../../assets/arrowBottom.svg';

interface SubmenuItem {
  label: string;
  path: string;
}

interface DropdownMenuItemProps {
  icon: string | React.ReactNode;
  label: string;
  submenuItems: SubmenuItem[];
}

export const DropdownMenuItem: React.FC<DropdownMenuItemProps> = ({
  icon,
  label,
  submenuItems,
}) => {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <div>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`cursor-pointer w-full flex items-center justify-between p-3 rounded-lg text-[14px] leading-[20px] tracking-[0%] font-[400] font-fira-sans transition-colors
                    ${isOpen ? 'text-[#0D0D0D] hover:bg-[#1C1C1C0D]' : 'text-[#0D0D0D] hover:bg-[#1C1C1C0D]'}`}
      >
        <div className="flex items-center">
          <span className="mr-3">
            {typeof icon === 'string' ? (
              <img
                src={icon}
                alt="Menu icon"
                className="[filter:invert(40%)_sepia(0%)_saturate(0%)_hue-rotate(0deg)_brightness(100%)_contrast(100%)]"
              />
            ) : (
              icon
            )}
          </span>
          <span className="text-[#0D0D0D] text-[14px] leading-[20px] tracking-[0%] font-[400] font-fira-sans">
            {label}
          </span>
        </div>
        <img
          src={arrowBottom}
          alt=""
          className={`w-4 h-4 transition-transform duration-200 [filter:invert(40%)_sepia(0%)_saturate(0%)_hue-rotate(0deg)_brightness(100%)_contrast(100%)] ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>
      {isOpen && (
        <div className="ml-10 mt-1 space-y-1 relative mb-3 mt-2 flex flex-col gap-2">
          <div className="absolute -left-4 bottom-2 top-2 w-[1px] bg-[#00000033]"></div>
          {submenuItems.map(item => (
            <NavLink
              key={item.path}
              to={item.path}
              {...(item.path === '/dashboard' ? { end: true } : {})}
              className={({ isActive }) =>
                `flex items-center p-2 rounded-lg text-[14px] leading-[20px] tracking-[0%] font-[400] font-fira-sans transition-colors ${
                  isActive ? 'bg-[#1C1C1C0D]' : 'text-[#0D0D0D] hover:bg-[#1C1C1C0D]'
                }`
              }
            >
              <span className="text-[#0D0D0D] text-[14px] leading-[20px] tracking-[0%] font-[400] font-fira-sans">
                {item.label}
              </span>
            </NavLink>
          ))}
        </div>
      )}
    </div>
  );
};
