import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import assessmentService, {
  TodaysAssessmentSession,
  SessionAssessmentDetails,
  SubmitAssessmentRequest,
  Assessment,
} from '../assessmentService';
import testAssignmentService, {
  StudentSessionsResponse,
  ScheduledAssessment,
} from '../testAssignmentService';
import logger from '../../utils/logger';

// Query keys for assessments
export const assessmentQueryKeys = {
  all: ['assessments'] as const,
  todaysSessions: (studentId: number) => ['assessments', 'todays', studentId] as const,
  sessionDetails: (sessionId: string) => ['assessments', 'session', sessionId] as const,
  studentSessions: (studentId: number) => ['assessments', 'student', studentId] as const,
  scheduledAssessments: (studentId: number) => ['assessments', 'scheduled', studentId] as const,
  allAssessments: () => ['assessments', 'catalog'] as const,
};

// Hook for fetching today's assessment sessions for a student
export const useTodaysAssessments = (studentId: number) => {
  return useQuery<TodaysAssessmentSession>({
    queryKey: assessmentQueryKeys.todaysSessions(studentId),
    queryFn: () => assessmentService.getTodaysAssessments(studentId),
    enabled: !!studentId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: true,
    retry: 2,
  });
};

// Hook for fetching session assessment details
export const useSessionDetails = (sessionId: string) => {
  return useQuery<SessionAssessmentDetails>({
    queryKey: assessmentQueryKeys.sessionDetails(sessionId),
    queryFn: () => assessmentService.getSessionDetails(sessionId),
    enabled: !!sessionId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Hook for fetching all student sessions (existing from testAssignmentService)
export const useStudentSessions = (studentId: number) => {
  return useQuery<StudentSessionsResponse>({
    queryKey: assessmentQueryKeys.studentSessions(studentId),
    queryFn: () => testAssignmentService.getStudentSessions(studentId),
    enabled: !!studentId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: true,
    retry: 2,
  });
};

// Hook for fetching scheduled assessments for a student
export const useScheduledAssessments = (studentId: number) => {
  return useQuery<ScheduledAssessment[]>({
    queryKey: assessmentQueryKeys.scheduledAssessments(studentId),
    queryFn: () => testAssignmentService.getScheduledAssessments(studentId),
    enabled: !!studentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Hook for fetching all assessments catalog
export const useAllAssessments = () => {
  return useQuery<Assessment[]>({
    queryKey: assessmentQueryKeys.allAssessments(),
    queryFn: () => assessmentService.getAllAssessments(),
    staleTime: 30 * 60 * 1000, // 30 minutes - catalog doesn't change often
    gcTime: 60 * 60 * 1000, // 1 hour
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Mutation for submitting assessment answers
export const useSubmitAssessment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: SubmitAssessmentRequest) => assessmentService.submitAnswers(request),
    onSuccess: (data, variables) => {
      logger.log('Assessment submitted successfully:', data);

      // Invalidate relevant queries to refresh data
      queryClient.invalidateQueries({
        queryKey: assessmentQueryKeys.sessionDetails(variables.session_id),
      });

      // Invalidate student sessions to update status
      // We need to extract student_id from somewhere - this might need to be passed as part of the request
      queryClient.invalidateQueries({
        queryKey: ['assessments', 'student'],
      });

      // Invalidate today's sessions
      queryClient.invalidateQueries({
        queryKey: ['assessments', 'todays'],
      });
    },
    onError: error => {
      logger.error('Error submitting assessment:', error);
    },
  });
};

// Enhanced mutation that includes student_id for better cache invalidation
export const useSubmitAssessmentWithStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      request,
      studentId: _studentId,
    }: {
      request: SubmitAssessmentRequest;
      studentId: number;
    }) => assessmentService.submitAnswers(request),
    onSuccess: (data, variables) => {
      logger.log('Assessment submitted successfully:', data);

      const { request, studentId } = variables;

      // Invalidate specific queries
      queryClient.invalidateQueries({
        queryKey: assessmentQueryKeys.sessionDetails(request.session_id),
      });

      queryClient.invalidateQueries({
        queryKey: assessmentQueryKeys.studentSessions(studentId),
      });

      queryClient.invalidateQueries({
        queryKey: assessmentQueryKeys.todaysSessions(studentId),
      });
    },
    onError: error => {
      logger.error('Error submitting assessment:', error);
    },
  });
};
