import React from 'react';
import { ReadingArea } from '../types';
import stars from '../../../assets/stars.svg';

interface ReadingAreaSelectorProps {
  readingAreas: ReadingArea[];
  selectedReadingAreas: ReadingArea[];
  displayAreaText: string;
  areaSelectorText: string;
  loadingReadingAreas: boolean;
  handleAreaSelect: (area: ReadingArea) => void;
  handleExercisesSubmit: (payload: {
    age: number;
    grade: string;
    domains: string[];
    selectedArea: string;
  }) => void;
  selectedGrade: string;
}

export const ReadingAreaSelector: React.FC<ReadingAreaSelectorProps> = ({
  readingAreas,
  selectedReadingAreas,
  displayAreaText,
  areaSelectorText,
  loadingReadingAreas,
  handleAreaSelect,
  handleExercisesSubmit,
  selectedGrade,
}) => {
  return (
    <>
      {displayAreaText.length > 0 && (
        <div className="flex items-center mb-6 h-8">
          <img src={stars} alt="" className="w-6 h-6 mr-2 ml-[13px]" />
          <p className="text-xl font-semibold text-gray-700">{displayAreaText}</p>
        </div>
      )}

      {!loadingReadingAreas &&
        readingAreas.length > 0 &&
        displayAreaText.length === areaSelectorText.length &&
        areaSelectorText.length > 0 && (
          <div className="w-[1024px] h-[191px] overflow-y-auto flex justify-center items-center mx-auto">
            <div className="grid grid-cols-4 gap-4">
              {readingAreas.map(area => (
                <button
                  key={area.id}
                  className={`
                    w-[231px] h-[84px]
                    px-4 py-3
                    rounded-xl
                    border
                    shadow-sm
                    transition-all
                    text-base
                    text-left
                    text-black
                    focus:outline-none
                    hover:border-2 hover:border-[#005773]
                    ${
                      selectedReadingAreas.some(selected => selected.id === area.id)
                        ? 'border-2 border-[#005773] bg-white font-semibold shadow-lg'
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  onClick={() => {
                    if (area.name === 'Math Problem') {
                      // For Math Problem use handleExercisesSubmit instead of handleAreaSelect
                      handleExercisesSubmit({
                        age: 8, // Default age
                        grade: selectedGrade || '2nd Grade', // Use selected grade or default
                        domains: ['math-problem'],
                        selectedArea: 'Math Problem',
                      });
                    } else {
                      // For other areas use standard handleAreaSelect function
                      handleAreaSelect(area);
                    }
                  }}
                >
                  <div className="flex flex-col h-full">
                    <div className="flex items-center mb-1">
                      <div className="w-6 h-6 mr-2 flex items-center justify-center rounded-full bg-[#005773] text-white">
                        {area.name === 'Math Problem' ? (
                          <span className="text-xs font-bold">M</span>
                        ) : (
                          <span className="text-xs font-bold">{area.name.charAt(0)}</span>
                        )}
                      </div>
                      <div className="font-medium">{area.name}</div>
                    </div>
                    <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                      {area.description ||
                        (area.name === 'Math Problem'
                          ? 'Exercises for practicing mathematical concepts and problem solving.'
                          : 'Select this area to start exercises.')}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}
    </>
  );
};

export default ReadingAreaSelector;
