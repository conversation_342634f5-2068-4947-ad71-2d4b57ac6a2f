import React, { useState, useEffect } from 'react';
import { QuizQuestion as QuizQuestionType } from '../common/types';
import VisualObjects from './VisualObjects';

interface QuizQuestionProps {
  question: QuizQuestionType;
  onAnswer: (answer: number) => void;
  showFeedback: boolean;
  userAnswer: number | null;
}

const QuizQuestion: React.FC<QuizQuestionProps> = ({
  question,
  onAnswer,
  showFeedback,
  userAnswer,
}) => {
  const [inputValue, setInputValue] = useState<string>('');
  const [crossedCount, setCrossedCount] = useState(0);
  const isCorrect = userAnswer === question.equation.result;

  useEffect(() => {
    if (showFeedback) {
      const timer = setTimeout(() => {
        setCrossedCount(question.visualObjects.subtract);
      }, 500);
      return () => clearTimeout(timer);
    } else {
      setCrossedCount(0);
      setInputValue('');
    }
  }, [showFeedback, question.visualObjects.subtract]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const numValue = parseInt(inputValue);
    if (!isNaN(numValue)) {
      onAnswer(numValue);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 rounded-xl bg-white shadow-lg transition-all duration-500 ease-in-out">
      <div className="mb-8">
        <VisualObjects
          objectType={question.visualObjects.objectType}
          count={question.visualObjects.total}
          crossedOut={crossedCount}
          className="mb-4 py-3"
        />

        <div className="flex items-center justify-center text-3xl font-bold gap-3 py-4">
          <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800">
            {question.equation.num1}
          </div>
          <span>−</span>
          <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800">
            {question.equation.num2}
          </div>
          <span>=</span>
          <div
            className={`flex items-center justify-center w-14 h-14 rounded-full border-2 ${
              showFeedback
                ? isCorrect
                  ? 'border-green-500 bg-green-100'
                  : 'border-red-500 bg-red-100'
                : 'border-gray-800'
            } transition-colors duration-300`}
          >
            {showFeedback ? question.equation.result : '?'}
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="flex flex-col items-center">
        <div className="mb-4 w-full max-w-xs">
          <label htmlFor="answer" className="block text-lg font-medium text-gray-700 mb-2">
            Your answer:
          </label>
          <input
            type="number"
            id="answer"
            value={inputValue}
            onChange={e => setInputValue(e.target.value)}
            disabled={showFeedback}
            className={`w-full px-4 py-3 text-xl text-center rounded-lg border-2 ${
              showFeedback
                ? isCorrect
                  ? 'border-green-500 bg-green-50'
                  : 'border-red-500 bg-red-50'
                : 'border-purple-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-200'
            } outline-none transition-all duration-300`}
            placeholder="?"
            min="0"
            max="20"
          />
        </div>

        {showFeedback ? (
          <div
            className={`text-lg font-medium ${isCorrect ? 'text-green-600' : 'text-red-600'} mb-2 animate-fadeIn`}
          >
            {isCorrect
              ? 'Correct! Great job! 🎉'
              : `Not quite. The answer is ${question.equation.result}`}
          </div>
        ) : (
          <button
            type="submit"
            disabled={inputValue === ''}
            className="px-6 py-3 bg-purple-600 text-white rounded-lg shadow-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Check Answer
          </button>
        )}
      </form>
    </div>
  );
};

export default QuizQuestion;
