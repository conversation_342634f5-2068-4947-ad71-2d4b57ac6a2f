import React, { useState, useRef } from 'react';
import { GameStats, Task, FeedbackState } from '../common/types';
import { gameWebSocket } from '../common/websocket';
import Question from '@games/blend-game/Question';
import OptionButton from '@games/blend-game/OptionButton';
import Feedback from '@games/blend-game/Feedback';
import ProgressBar from '@games/blend-game/ProgressBar';
import GameResults from '../common/GameResults';

export interface BlendActivityProps {
  tasks: Task[];
  onComplete?: (correct: number, total: number, time: number) => void;
  studentId?: string;
  gameId?: string;
  sessionId?: string;
  onFinish?: (result: GameStats) => void;
  onFeedback?: (isCorrect: boolean) => void;
}

const BlendActivity: React.FC<BlendActivityProps> = ({
  tasks,
  onComplete,
  gameId,
  studentId,
  sessionId,
  onFinish,
  onFeedback,
}) => {
  const [currentTaskIndex, setCurrentTaskIndex] = useState(0);
  const [feedback, setFeedback] = useState<FeedbackState>({
    show: false,
    isCorrect: false,
    selectedOption: null,
  });
  const [completedTasks, setCompletedTasks] = useState<boolean[]>(() =>
    Array(tasks.length).fill(false)
  );
  const [isGameComplete, setIsGameComplete] = useState(false);
  const [stats, setStats] = useState<GameStats>({
    sessionId: sessionId,
    totalQuestions: tasks.length,
    correctAnswers: 0,
    incorrectAnswers: 0,
    completionPercentage: 0,
    startTime: Date.now(),
    endTime: null,
    gameId: gameId || '',
    studentId: studentId || '',
  });

  const currentTask = tasks[currentTaskIndex];

  const handleOptionClick = (option: string) => {
    if (feedback.show) return;

    const isCorrect = option === currentTask.answer;
    setFeedback({
      show: true,
      isCorrect,
      selectedOption: option,
    });

    if (onFeedback) {
      onFeedback(isCorrect);
    }

    const updatedCompletedTasks = [...completedTasks];
    updatedCompletedTasks[currentTaskIndex] = true;

    setCompletedTasks(updatedCompletedTasks);

    setStats(prevStats => {
      const newCorrectAnswers = prevStats.correctAnswers + (isCorrect ? 1 : 0);
      const newIncorrectAnswers = prevStats.incorrectAnswers + (isCorrect ? 0 : 1);
      const newCompletionPercentage = Math.round(
        (updatedCompletedTasks.filter(Boolean).length / tasks.length) * 100
      );

      const newStats: GameStats = {
        ...prevStats,
        sessionId: sessionId, // Zachowaj sessionId
        correctAnswers: newCorrectAnswers,
        incorrectAnswers: newIncorrectAnswers,
        completionPercentage: newCompletionPercentage,
        gameId: gameId || '',
        studentId: studentId || '',
        endTime: Date.now(),
        progress: [
          ...(prevStats.progress || []),
          {
            step: currentTaskIndex + 1,
            correct: isCorrect,
          },
        ],
      };

      return newStats;
    });
  };

  const handleFeedbackComplete = () => {
    setFeedback({
      show: false,
      isCorrect: false,
      selectedOption: null,
    });

    if (currentTaskIndex >= tasks.length - 1) {
      setIsGameComplete(true);
      const finalEndTime = Date.now();

      setStats(prevStats => {
        const finalStats = {
          ...prevStats,
          sessionId: sessionId,
          endTime: finalEndTime,
          completionPercentage: 100,
          timeInSeconds: Math.round((finalEndTime - prevStats.startTime) / 1000),
        };

        if (onComplete) {
          const timeTaken = Math.round((finalEndTime - prevStats.startTime) / 1000);
          onComplete(finalStats.correctAnswers, finalStats.totalQuestions, timeTaken);
        }

        setTimeout(() => {
          if (onFinish) {
            onFinish(finalStats);
          }
        }, 500);
        return finalStats;
      });
    } else {
      setCurrentTaskIndex(prevIndex => prevIndex + 1);
    }
  };

  const handleRestart = () => {
    setCurrentTaskIndex(0);
    setCompletedTasks(Array(tasks.length).fill(false));
    setIsGameComplete(false);
    setFeedback({
      show: false,
      isCorrect: false,
      selectedOption: null,
    });
    setStats({
      sessionId: sessionId,
      totalQuestions: tasks.length,
      correctAnswers: 0,
      incorrectAnswers: 0,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: gameId || '',
      studentId: studentId || '',
    });
  };

  if (isGameComplete) {
    return <GameResults stats={stats} onRestart={handleRestart} />;
  }

  return (
    <div className="bg-gradient-to-b from-purple-50 to-blue-50 w-full">
      <div className="max-w-4xl mx-auto p-6 relative">
        <div className="mb-6">
          <ProgressBar currentQuestion={currentTaskIndex + 1} totalQuestions={tasks.length} />
        </div>

        {feedback.show && (
          <Feedback isCorrect={feedback.isCorrect} onComplete={handleFeedbackComplete} />
        )}

        {currentTask && (
          <>
            <Question task={currentTask} />

            <div className="grid grid-cols-2 gap-4 mt-8">
              {currentTask.options.map((option: string, index: number) => (
                <OptionButton
                  key={index}
                  option={option}
                  onClick={() => handleOptionClick(option)}
                  disabled={feedback.show}
                  isSelected={option === feedback.selectedOption}
                  isCorrect={feedback.selectedOption === option ? feedback.isCorrect : undefined}
                />
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default BlendActivity;
