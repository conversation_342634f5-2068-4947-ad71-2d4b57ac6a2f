import { GameTask, GameProps, BaseTask } from './types';
import { GameStats } from '../common/types';
import Game from './Game';

class GameFactory {
  /**
   * Create a new game instance with the given tasks and callbacks
   *
   * @param tasks Array of game tasks
   * @param gameId Unique identifier for the game
   * @param studentId Student identifier (optional)
   * @param onComplete Callback when game is completed (optional)
   * @param onFinish Callback when game is finished (optional)
   * @returns Game component props
   */
  createGame(
    tasks: GameTask[],
    gameId: string,
    studentId: string = '',
    onComplete?: (stats: GameStats) => void,
    onFinish?: (result: GameStats) => void
  ): GameProps {
    return {
      tasks,
      gameId,
      studentId,
      onComplete,
      onFinish,
    };
  }

  /**
   * @param type
   * @param tasks
   * @param gameId
   * @param studentId
   * @param onComplete
   * @param onFinish
   * @returns
   */
  createGameOfType<T extends BaseTask>(
    type: string,
    tasks: Omit<T, 'type'>[],
    gameId: string,
    studentId: string = '',
    onComplete?: (stats: GameStats) => void,
    onFinish?: (result: GameStats) => void
  ): GameProps {
    const typedTasks = tasks.map(task => ({
      ...task,
      type,
    })) as unknown as GameTask[];

    return this.createGame(typedTasks, gameId, studentId, onComplete, onFinish);
  }

  getGameComponent() {
    return Game;
  }
}

const gameFactory = new GameFactory();
export default gameFactory;
