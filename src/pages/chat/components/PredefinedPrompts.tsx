import React from 'react';

interface PredefinedPromptsProps {
  onPromptClick: (promptText: string) => void;
}

export const PredefinedPrompts: React.FC<PredefinedPromptsProps> = ({ onPromptClick }) => {
  const prompts = [
    {
      id: 'phonemic-awareness',
      title: 'Phonemic Awareness',
      description: 'Develop sound recognition and manipulation skills',
      icon: 'P',
    },
    {
      id: 'phonics-decoding',
      title: 'Phonics & Decoding',
      description: 'Master letter-sound relationships and word recognition',
      icon: 'P',
    },
    {
      id: 'reading-fluency',
      title: 'Reading Fluency',
      description: 'Improve reading speed, accuracy, and expression',
      icon: 'R',
    },
    {
      id: 'vocabulary',
      title: 'Vocabulary Development',
      description: 'Expand your word knowledge and usage',
      icon: 'V',
    },
    {
      id: 'reading-comprehension',
      title: 'Reading Comprehension',
      description: 'Enhance understanding of written texts',
      icon: 'R',
    },
    {
      id: 'orthographic',
      title: 'Orthographic Processing',
      description: 'Improve visual word recognition and spelling',
      icon: 'O',
    },
    {
      id: 'written-expression',
      title: 'Written Expression',
      description: 'Develop writing skills and composition abilities',
      icon: 'W',
    },
    {
      id: 'executive-function',
      title: 'Executive Function & Study Skills',
      description: 'Build organization and learning strategies',
      icon: 'E',
    },
    {
      id: 'math',
      title: 'Math',
      description: 'Math exercises and problem solving',
      icon: 'M',
    },
  ];

  return (
    <div className="w-full max-w-3xl mx-auto my-4">
      <div className="flex flex-wrap justify-center gap-4">
        {prompts.map(prompt => (
          <button
            key={prompt.id}
            className="w-[231px] h-[120px] px-4 py-3 mt-4 rounded-xl border border-gray-200 shadow-sm
                     transition-all text-base text-left text-black focus:outline-none border-2
                     hover:border-2 hover:border-[#005773] bg-gray-50 cursor-pointer"
            onClick={() => onPromptClick(prompt.title)}
          >
            <div className="flex flex-col h-full">
              <div className="flex mb-1">
                <div className="min-w-6 h-6 mr-2 flex items-center justify-center rounded-full bg-[#005773] text-white">
                  <span className="text-xs font-bold">{prompt.icon}</span>
                </div>
                <div className="font-medium">{prompt.title}</div>
              </div>
              <div className="text-xs text-gray-500 mt-1 line-clamp-2">{prompt.description}</div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default PredefinedPrompts;
