import React from 'react';

interface ProgressBarProps {
  current?: number;
  total?: number;
  currentQuestion?: number;
  totalQuestions?: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  current,
  total,
  currentQuestion,
  totalQuestions,
}) => {
  const currentValue = currentQuestion || current || 0;
  const totalValue = totalQuestions || total || 1;
  const progress = (currentValue / totalValue) * 100;

  // Określamy kolor paska postępu w zależ<PERSON>ści od procentu ukończenia
  const getProgressColor = () => {
    if (progress < 30) return 'from-indigo-500 to-blue-500';
    if (progress < 70) return 'from-blue-500 to-cyan-500';
    return 'from-cyan-500 to-green-500';
  };

  return (
    <div className="w-full p-4 mb-4">
      {/* Informacja o postępie */}
      <div className="flex justify-between items-center mb-3 px-1">
        <div className="flex items-center">
          <span className="text-gray-700 font-medium">
            {currentValue} of {totalValue} tasks
          </span>
        </div>
        <div className="bg-indigo-50 px-4 py-1 rounded-full shadow-sm">
          <span className="text-indigo-700 font-semibold">{Math.round(progress)}%</span>
        </div>
      </div>

      {/* Pasek postępu */}
      <div className="w-full h-4 bg-gray-100 rounded-full overflow-hidden shadow-inner">
        <div
          className={`h-full bg-gradient-to-r ${getProgressColor()} transition-all duration-700 ease-out`}
          style={{
            width: `${progress}%`,
            boxShadow: '0 0 8px rgba(99, 102, 241, 0.6)',
          }}
        ></div>
      </div>
    </div>
  );
};

export default ProgressBar;
