import React, { useEffect } from 'react';
import { useUserStore } from '../../store/userStore';
import { USER_ROLES } from '../../contants';
import { StudentDashboard } from '../../components/dashboard/StudentDashboard';
import { TeacherDashboard } from '../../components/dashboard/TeacherDashboard';

export const DashboardPage: React.FC = () => {
  const { user, loadAvatar } = useUserStore();

  useEffect(() => {
    const checkAvatar = async () => {
      try {
        await loadAvatar();
      } catch (error) {
        console.error('Error checking avatar:', error);
        window.location.href = '/dashboard/settings/avatar-creator';
      }
    };
    checkAvatar();
  }, [loadAvatar]);

  if (user?.role === USER_ROLES.TEACHER) {
    return <TeacherDashboard />;
  }

  return <StudentDashboard />;
};
