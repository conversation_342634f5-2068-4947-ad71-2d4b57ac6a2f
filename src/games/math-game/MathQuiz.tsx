import React, { useState, useEffect } from 'react';
import { quizQuestions } from './quizData';
import { QuizState, GameStats } from '../common/types';
import QuizQuestion from './QuizQuestion';
import QuizHeader from './QuizHeader';
import { gameWebSocket } from '../common/websocket';
import GameResults from '../common/GameResults';

interface MathQuizProps {
  studentId?: string;
  gameId?: string;
  sessionId?: string;
  onComplete?: (correct: number, total: number, time: number) => void;
  onFinish?: (result: GameStats) => void;
  onFeedback?: (isCorrect: boolean) => void;
}

const MathQuiz: React.FC<MathQuizProps> = ({
  studentId,
  gameId,
  sessionId,
  onComplete,
  onFinish,
  onFeedback,
}) => {
  const initialState: QuizState = {
    currentQuestionIndex: 0,
    questions: [...quizQuestions].sort(() => Math.random() - 0.5),
    score: 0,
    userAnswers: Array(quizQuestions.length).fill(null),
    quizCompleted: false,
  };

  const [quizState, setQuizState] = useState<QuizState>(initialState);
  const [showFeedback, setShowFeedback] = useState(false);
  const [animateOut, setAnimateOut] = useState(false);
  const [_startTime] = useState(Date.now());
  const [stats, setStats] = useState<GameStats>({
    sessionId: sessionId,
    totalQuestions: quizQuestions.length,
    correctAnswers: 0,
    incorrectAnswers: 0,
    completionPercentage: 0,
    startTime: Date.now(),
    endTime: null,
    gameId: gameId || 'math-game',
    studentId: studentId || '',
  });

  const currentQuestion = quizState.questions[quizState.currentQuestionIndex];

  useEffect(() => {
    gameWebSocket.connect();

    return () => {
      if (!quizState.quizCompleted && quizState.currentQuestionIndex > 0) {
        const partialStats = {
          ...stats,
          endTime: Date.now(),
          completionPercentage: Math.round(
            (quizState.currentQuestionIndex / quizQuestions.length) * 100
          ),
        };

        gameWebSocket.sendGameStats(partialStats);
      }
    };
  }, [quizState.quizCompleted, quizState.currentQuestionIndex, stats, gameId]);

  const handleAnswer = (answer: number) => {
    const isCorrect = answer === currentQuestion.equation.result;

    if (onFeedback) {
      onFeedback(isCorrect);
    }

    const newUserAnswers = [...quizState.userAnswers];
    newUserAnswers[quizState.currentQuestionIndex] = answer;

    setQuizState(prev => ({
      ...prev,
      userAnswers: newUserAnswers,
      score: isCorrect ? prev.score + 1 : prev.score,
    }));

    setStats(prevStats => {
      const completedQuestions = quizState.currentQuestionIndex + 1;
      const newStats: GameStats = {
        ...prevStats,
        sessionId: sessionId,
        correctAnswers: prevStats.correctAnswers + (isCorrect ? 1 : 0),
        incorrectAnswers: prevStats.incorrectAnswers + (isCorrect ? 0 : 1),
        completionPercentage: Math.round((completedQuestions / quizQuestions.length) * 100),
        gameId: gameId || 'math-game',
        studentId: studentId || '',
        endTime: Date.now(),
        progress: [
          ...(prevStats.progress || []),
          {
            step: quizState.currentQuestionIndex + 1,
            correct: isCorrect,
          },
        ],
      };

      gameWebSocket.sendGameStats(newStats);
      return newStats;
    });

    setShowFeedback(true);

    setTimeout(() => {
      setAnimateOut(true);

      setTimeout(() => {
        const nextIndex = quizState.currentQuestionIndex + 1;

        if (nextIndex >= quizState.questions.length) {
          setQuizState(prev => ({
            ...prev,
            quizCompleted: true,
          }));

          const endTime = Date.now();
          setStats(prevStats => {
            const updatedStats: GameStats = {
              ...prevStats,
              sessionId: sessionId,
              endTime,
              completionPercentage: 100,
              gameId: gameId || 'math-game',
              studentId: studentId || '',
              progress: prevStats.progress || [],
            };

            gameWebSocket.sendGameStats(updatedStats);

            if (onComplete) {
              const time = Math.round((endTime - prevStats.startTime) / 1000);
              onComplete(updatedStats.correctAnswers, updatedStats.totalQuestions, time);
            }

            if (onFinish) {
              const timeInSeconds = Math.round((endTime - prevStats.startTime) / 1000);
              const finalStats: GameStats = {
                ...updatedStats,
                timeInSeconds,
                progress: updatedStats.progress || [],
              };

              onFinish(finalStats);
            }

            return updatedStats;
          });
        } else {
          setQuizState(prev => ({
            ...prev,
            currentQuestionIndex: nextIndex,
          }));
        }

        setShowFeedback(false);
        setAnimateOut(false);
      }, 500);
    }, 1500);
  };

  const restartQuiz = () => {
    setQuizState({
      ...initialState,
      questions: [...quizQuestions].sort(() => Math.random() - 0.5),
    });

    setStats({
      sessionId: sessionId,
      totalQuestions: quizQuestions.length,
      correctAnswers: 0,
      incorrectAnswers: 0,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: gameId || 'math-game',
      studentId: studentId || '',
    });

    setShowFeedback(false);
    setAnimateOut(false);
  };

  return (
    <div className="w-full bg-gradient-to-b from-purple-50 to-blue-50 py-8 px-4">
      <div className="max-w-4xl mx-auto overflow-x-auto">
        {!quizState.quizCompleted ? (
          <>
            <QuizHeader
              currentQuestion={quizState.currentQuestionIndex + 1}
              totalQuestions={quizState.questions.length}
              score={quizState.score}
            />

            <div className="w-full flex flex-col md:flex-row gap-8 items-stretch min-h-[500px]">
              <div className="w-full flex flex-col justify-center">
                <QuizQuestion
                  question={currentQuestion}
                  onAnswer={handleAnswer}
                  showFeedback={showFeedback}
                  userAnswer={quizState.userAnswers[quizState.currentQuestionIndex]}
                />
              </div>
            </div>
          </>
        ) : (
          <GameResults
            stats={{
              ...stats,
              timeInSeconds: stats.endTime
                ? Math.round((stats.endTime - stats.startTime) / 1000)
                : 0,
            }}
            onRestart={restartQuiz}
          />
        )}
      </div>
    </div>
  );
};

export default MathQuiz;
