import * as React from 'react';
import * as RadixRadioGroup from '@radix-ui/react-radio-group';

export interface RadioGroupProps extends Omit<RadixRadioGroup.RadioGroupProps, 'onValueChange'> {
  options: { label: string; value: string }[];
  onValueChange?: (value: string) => void;
  selected?: string;
}

export const RadioGroup: React.FC<RadioGroupProps> = ({
  options,
  onValueChange,
  selected,
  className,
  ...props
}) => {
  return (
    <RadixRadioGroup.Root
      value={selected ?? ''}
      onValueChange={val => onValueChange?.(val)}
      className={`flex gap-4 ${className}`}
      {...props}
    >
      {options.map(opt => (
        <RadixRadioGroup.Item
          key={opt.value}
          value={opt.value}
          className="h-5 w-5 rounded-full border-2 border-gray-400 data-[state=checked]:border-primaryBtn flex items-center justify-center cursor-pointer focus:outline-none"
        >
          <RadixRadioGroup.Indicator className="h-3 w-3 bg-primaryBtn rounded-full" />
        </RadixRadioGroup.Item>
      ))}
    </RadixRadioGroup.Root>
  );
};
