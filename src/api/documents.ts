import { authService } from '../services/authService';

const API_URL = 'https://api.dev.yubu.ai';

interface Document {
  document_id: number;
  student_id: number;
  title: string | null;
  created_at: string;
}

interface DocumentDetails {
  document_id: number;
  student_id: number;
  created_at: string;
  summary: string;
  title: string;
  age: number;
  grade: number;
  reason_for_referral: string;
  assessment: {
    psychological: string;
    speech_language: string;
    neurological: string;
    mathematics: string;
    literacy: string;
    academic: string;
    behavioral: string;
    emotional: string;
    motor: string;
    other: string;
  };
  diagnosis: string[];
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  followup_needed: boolean;
  keywords: string[];
}

interface DocumentContent {
  document_id: number;
  student_id: number;
  report_text: string;
  created_at: string;
}

export const documentsApi = {
  async addDocument(file: File, studentId: number): Promise<Document> {
    const token = authService.getAccessToken();

    if (!token) {
      throw new Error('No authorization token');
    }

    const formData = new FormData();
    formData.append('student_id', studentId.toString());
    formData.append('file', file);

    const response = await fetch(`${API_URL}/documents/documents_add`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || 'Error adding document');
    }

    return response.json();
  },

  async getDocumentsList(): Promise<Document[]> {
    const token = authService.getAccessToken();
    const headers = {
      Authorization: `Bearer ${token}`,
    };

    const response = await fetch(`${API_URL}/documents/list`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      throw new Error(errorData.detail || 'Failed to load documents list');
    }

    const data = await response.json();
    return data.documents;
  },

  async getDocument(documentId: number): Promise<DocumentContent> {
    const token = authService.getAccessToken();
    const headers = {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
    const body = { document_id: documentId };

    const response = await fetch(`${API_URL}/documents/get_document`, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      throw new Error(errorData.detail || 'Failed to load document');
    }

    return response.json();
  },

  async getDocumentDetails(documentId: number): Promise<DocumentDetails> {
    const token = authService.getAccessToken();

    const headers = {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
    const body = { document_id: documentId };

    const response = await fetch(`${API_URL}/documents/get_details`, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      throw new Error(errorData.detail || 'Failed to load document details');
    }

    return response.json();
  },
};
