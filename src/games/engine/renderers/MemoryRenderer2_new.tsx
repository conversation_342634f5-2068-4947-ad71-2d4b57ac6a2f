import React, { useState, useEffect } from 'react';
import { GameRendererProps } from '../types';
// import { MemoryTask2 } from '../types';
import { KeySquare } from 'lucide-react';
import { gameWebSocket } from '../../common/websocket';
import logger from '../../../utils/logger';

interface GuessHistoryItem {
  guess: string;
  feedback: string;
  correctPlace: number;
  correctDigit: number;
  wrong: number;
}

const MemoryRenderer2: React.FC<GameRendererProps> = props => {
  const {
    task: _task,
    onAnswer,
    showFeedback = false,
    currentTaskIndex = 0,
    totalTasks = 1,
    sessionId,
    stats = {
      correctAnswers: 0,
      incorrectAnswers: 0,
      totalQuestions: 1,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: '',
      studentId: '',
    },
  } = props;

  const [secret, setSecret] = useState<string>('');
  const [guess, setGuess] = useState('');
  const [history, setHistory] = useState<GuessHistoryItem[]>([]);
  const [attempts, setAttempts] = useState(0);
  const [won, setWon] = useState(false);
  const [difficulty, setDifficulty] = useState(3); // Default 3-digit number

  useEffect(() => {
    // Generate a number without repeating digits
    const generateSecret = () => {
      const digits: string[] = [];
      while (digits.length < difficulty) {
        const d = Math.floor(Math.random() * 10).toString();
        if (digits.length === 0 && d === '0') continue; // do not start with 0
        if (!digits.includes(d)) digits.push(d);
      }
      return digits.join('');
    };

    const newSecret = generateSecret();
    logger.log('[MemoryRenderer2] Generated secret number:', newSecret);

    setSecret(newSecret);
    setHistory([]);
    setAttempts(0);
    setWon(false);
    setGuess('');

    // Reset stats
    if (stats) {
      stats.correctAnswers = 0;
      stats.incorrectAnswers = 0;
      stats.startTime = Date.now();
      stats.endTime = null;
      stats.progress = [];
    }
  }, [difficulty, stats]);

  const checkGuess = (g: string, s: string) => {
    let correctPlace = 0;
    let correctDigit = 0;

    // Check digits in correct positions
    for (let i = 0; i < s.length; i++) {
      if (g[i] === s[i]) correctPlace++;
    }

    // Check digits in wrong positions
    // First count occurrences of each digit in the secret number
    const secretDigits: { [key: string]: number } = {};
    for (const digit of s) {
      secretDigits[digit] = (secretDigits[digit] || 0) + 1;
    }

    // Subtract digits that are already in correct positions
    for (let i = 0; i < s.length; i++) {
      if (g[i] === s[i] && secretDigits[g[i]] > 0) {
        secretDigits[g[i]]--;
      }
    }

    // Count digits in wrong positions
    for (let i = 0; i < s.length; i++) {
      if (g[i] !== s[i] && secretDigits[g[i]] > 0) {
        correctDigit++;
        secretDigits[g[i]]--;
      }
    }

    const wrong = s.length - correctPlace - correctDigit;
    return { correctPlace, correctDigit, wrong };
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (guess.length !== difficulty || /[^0-9]/.test(guess)) return;

    setAttempts(a => a + 1);
    const res = checkGuess(guess, secret);

    let feedback = '';
    if (res.correctPlace === difficulty) {
      feedback = ' Congratulations! Code cracked!';
      setWon(true);

      // Update statistics
      if (stats) {
        const currentTime = Date.now();

        if (!stats.progress) {
          stats.progress = [];
        }

        stats.progress.push({
          step: attempts + 1,
          correct: true,
        });

        const updatedStats = {
          ...stats,
          sessionId: sessionId,
          correctAnswers: 1,
          totalQuestions: 1,
          completionPercentage: 100,
          endTime: currentTime,
          timeInSeconds: Math.round((currentTime - stats.startTime) / 1000),
          progress: [...stats.progress],
        };

        try {
          gameWebSocket.sendGameStats(updatedStats);
          logger.log('[MemoryRenderer2] Stats sent after winning:', updatedStats);
        } catch (error) {
          logger.error('[MemoryRenderer2] Failed to send stats:', error);
        }

        Object.assign(stats, updatedStats);
      }

      if (onAnswer) onAnswer(true, attempts + 1);
    } else {
      feedback = `${res.correctPlace} in place, ${res.correctDigit} not in place, ${res.wrong} missing`;

      // Update statistics for incorrect attempt
      if (stats) {
        const currentTime = Date.now();

        if (!stats.progress) {
          stats.progress = [];
        }

        stats.progress.push({
          step: attempts + 1,
          correct: false,
        });

        const updatedStats = {
          ...stats,
          sessionId: sessionId,
          incorrectAnswers: (stats.incorrectAnswers || 0) + 1,
          endTime: currentTime,
          timeInSeconds: Math.round((currentTime - stats.startTime) / 1000),
          progress: [...stats.progress],
        };

        try {
          gameWebSocket.sendGameStats(updatedStats);
          logger.log('[MemoryRenderer2] Stats sent after incorrect attempt:', updatedStats);
        } catch (error) {
          logger.error('[MemoryRenderer2] Failed to send stats:', error);
        }

        Object.assign(stats, updatedStats);
      }
    }

    setHistory(h => [...h, { guess, feedback, ...res }]);
    setGuess('');
  };

  const handleReset = () => {
    // Generate a new secret number without repeating digits
    const generateSecret = () => {
      const digits: string[] = [];
      while (digits.length < difficulty) {
        const d = Math.floor(Math.random() * 10).toString();
        if (digits.length === 0 && d === '0') continue; // do not start with 0
        if (!digits.includes(d)) digits.push(d);
      }
      return digits.join('');
    };

    const newSecret = generateSecret();
    logger.log('[MemoryRenderer2] Generated new secret number:', newSecret);

    setSecret(newSecret);
    setHistory([]);
    setAttempts(0);
    setWon(false);
    setGuess('');

    // Reset statistics
    if (stats) {
      stats.correctAnswers = 0;
      stats.incorrectAnswers = 0;
      stats.startTime = Date.now();
      stats.endTime = null;
      stats.progress = [];
    }
  };

  const changeDifficulty = (newDifficulty: number) => {
    if (newDifficulty >= 3 && newDifficulty <= 5) {
      setDifficulty(newDifficulty);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto p-6 bg-white rounded-xl shadow-lg mt-8">
      <div className="flex items-center justify-center mb-4">
        <KeySquare className="w-8 h-8 text-indigo-600 mr-2" />
        <h2 className="text-2xl font-bold text-center text-indigo-700">Number Code Breaker</h2>
      </div>

      <div className="flex justify-between items-center px-2 mb-4">
        <div className="text-sm md:text-base text-gray-700 font-medium">
          Level: <span className="text-indigo-700">{currentTaskIndex + 1}</span> / {totalTasks}
        </div>

        <div className="bg-gradient-to-r from-indigo-500 to-indigo-700 rounded-full h-2 w-1/2 mx-2">
          <div
            className="bg-gradient-to-r from-indigo-300 to-indigo-500 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${((currentTaskIndex + 1) / totalTasks) * 100}%` }}
          ></div>
        </div>

        <div className="text-sm md:text-base text-gray-700 font-medium">
          Attempts: <span className="text-indigo-700">{attempts}</span>
        </div>
      </div>

      <p className="text-center mb-6 text-gray-700">
        The computer has selected a {difficulty}-digit number without repeating digits. Guess it in
        as few attempts as possible!
        <br />
        After each attempt, you&apos;ll receive hints:
      </p>

      <ul className="mb-4 text-gray-600 text-sm">
        <li>✔️ – digit in the correct position</li>
        <li>🔄 – digit present but in wrong position</li>
        <li>❌ – digit not present</li>
      </ul>

      <div className="flex justify-center gap-2 mb-4">
        <button
          onClick={() => changeDifficulty(3)}
          className={`px-3 py-1 rounded-md ${difficulty === 3 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700'}`}
        >
          3 digits
        </button>
        <button
          onClick={() => changeDifficulty(4)}
          className={`px-3 py-1 rounded-md ${difficulty === 4 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700'}`}
        >
          4 digits
        </button>
        <button
          onClick={() => changeDifficulty(5)}
          className={`px-3 py-1 rounded-md ${difficulty === 5 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700'}`}
        >
          5 digits
        </button>
      </div>

      <form onSubmit={handleSubmit} className="flex gap-2 justify-center mb-6">
        <input
          type="text"
          maxLength={difficulty}
          pattern={`[0-9]{${difficulty}}`}
          value={guess}
          onChange={e => {
            const val = e.target.value.replace(/[^0-9]/g, '').slice(0, difficulty);
            setGuess(val);
          }}
          disabled={won}
          className="w-32 text-center text-2xl border-2 border-indigo-400 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-indigo-300"
          placeholder={'_'.repeat(difficulty)}
        />
        <button
          type="submit"
          disabled={won || guess.length !== difficulty}
          className="px-4 py-2 bg-indigo-600 text-white font-bold rounded-lg shadow hover:bg-indigo-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Check
        </button>
      </form>

      <div className="mb-4 text-center text-gray-700">
        Attempt: <span className="font-bold">{attempts + (won ? 0 : 1)}</span>
      </div>

      <div className="overflow-y-auto max-h-64">
        <table className="w-full text-center">
          <thead>
            <tr className="text-indigo-700">
              <th className="py-1">Attempt</th>
              <th className="py-1">Your Number</th>
              <th className="py-1">Hints</th>
            </tr>
          </thead>
          <tbody>
            {history.map((item, idx) => (
              <tr
                key={idx}
                className={
                  item.feedback.includes('Congratulations') ? 'bg-green-100 font-bold' : ''
                }
              >
                <td>{idx + 1}</td>
                <td>{item.guess}</td>
                <td>
                  {item.correctPlace} ✔️ {item.correctDigit} 🔄 {item.wrong} ❌
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {won && (
        <div className="mt-6 p-4 bg-green-100 rounded-lg text-center text-green-700 font-bold text-xl shadow">
          🎉 Congratulations! You cracked the code in {attempts} attempt{attempts === 1 ? '' : 's'}!
          <div className="mt-2 text-lg">
            Secret number: <span className="text-indigo-700">{secret}</span>
          </div>
        </div>
      )}

      <div className="mt-6 flex justify-center">
        <button
          onClick={handleReset}
          className="px-4 py-2 bg-indigo-600 text-white font-bold rounded-lg shadow hover:bg-indigo-700 transition"
        >
          New game
        </button>
      </div>

      {showFeedback && (
        <div className="mt-6 p-4 rounded-lg bg-green-100 text-center">
          <p className="text-lg font-medium text-green-700">
            Great job! Completed level in {props.userAnswer} attempt
            {props.userAnswer === 1 ? '' : 's'}.
          </p>
        </div>
      )}
    </div>
  );
};

export default MemoryRenderer2;
