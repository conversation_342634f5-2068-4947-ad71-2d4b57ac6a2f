import React from 'react';
import { GameRendererProps, BaseTask } from '../types';
import BaseRenderer from './BaseRenderer';

interface TemplateTask extends BaseTask {
  type: 'template';
  question: string;
  options: string[];
  answer: string;
}

class Template<PERSON>enderer extends BaseRenderer<TemplateTask> {
  isAnswerCorrect(userAnswer: string): boolean {
    const task = this.props.task as unknown as TemplateTask;
    return userAnswer === task.answer;
  }

  renderContent(): React.ReactNode {
    const task = this.props.task as unknown as TemplateTask;
    const { showFeedback, userAnswer } = this.props;

    return (
      <div className="game-template">
        <h3 className="text-xl font-bold mb-4">{task.question}</h3>

        <div className="grid grid-cols-2 gap-4 mt-4">
          {task.options.map((option, index) => (
            <button
              key={index}
              onClick={() => this.handleOptionClick(option)}
              disabled={showFeedback}
              className={`p-4 rounded-lg text-center ${
                userAnswer === option ? 'bg-blue-100 border-2 border-blue-500' : 'bg-gray-50 border'
              }`}
            >
              {option}
            </button>
          ))}
        </div>
      </div>
    );
  }

  private handleOptionClick = (option: string) => {
    const { onAnswer } = this.props;
    const task = this.props.task as unknown as TemplateTask;

    const isCorrect = option === task.answer;
    onAnswer(isCorrect, option);
  };
}

export default TemplateRenderer;
