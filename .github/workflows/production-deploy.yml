name: Production Deploy to S3

on:
  push:
    branches: [production]
jobs:
  production-deploy:
    runs-on: [self-hosted, yubu]

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build
        env:
          NODE_ENV: production

      - name: Deploy to Production S3
        run: aws s3 sync dist/ s3://yubu-prod-frontend --delete

      - name: CloudFront Invalidation
        run: |
          aws cloudfront create-invalidation --distribution-id E29ZT0J0F6NY2B --paths "/*"
