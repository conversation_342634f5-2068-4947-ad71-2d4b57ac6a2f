import React from 'react';
import { AnsweredQuestion, AssessmentAnswer } from '../types';
import stars from '../../../assets/stars.svg';
import keyA from '../../../assets/keyA.svg';
import keyB from '../../../assets/keyB.svg';

interface AssessmentComponentProps {
  answeredQuestions: AnsweredQuestion[];
  assessmentText: string;
  displayAssessmentText: string;
  showAssessmentButtons: boolean;
  handleAssessmentAnswer: (answer: AssessmentAnswer) => void;
  isLoadingAssessment: boolean;
}

export const AssessmentComponent: React.FC<AssessmentComponentProps> = ({
  answeredQuestions,
  assessmentText,
  displayAssessmentText,
  showAssessmentButtons,
  handleAssessmentAnswer,
  isLoadingAssessment,
}) => {
  return (
    <div className="mt-8 animate-fade-in">
      {answeredQuestions.map((item, index) => (
        <div key={index} className="mb-8">
          <div className="flex items-center mb-4">
            <img src={stars} alt="" className="w-6 h-6 mr-2 ml-[76px] opacity-70" />
            <p className="text-xl font-semibold text-gray-700">{item.question}</p>
          </div>

          <div className="flex flex-row items-start gap-4 mb-2 ml-[76px]">
            <button
              disabled
              className={`flex items-center justify-start gap-2 w-[135.65px] h-[64px] rounded-[16px] border ${
                item.answer === 'yes'
                  ? 'border-[#005773] bg-gray-100'
                  : 'border-gray-200 bg-white opacity-70'
              } px-4 ml-[30px]`}
            >
              <img src={keyA} alt="A" className="w-8 h-8" />
              <span className="text-[20px] font-bold text-black">Yes</span>
            </button>

            <button
              disabled
              className={`flex items-center justify-start gap-2 w-[135.65px] h-[64px] rounded-[16px] border ${
                item.answer === 'no'
                  ? 'border-[#005773] bg-gray-100'
                  : 'border-gray-200 bg-white opacity-70'
              } px-4 ml-[30px]`}
            >
              <img src={keyB} alt="B" className="w-8 h-8" />
              <span className="text-[20px] font-bold text-black">No</span>
            </button>
          </div>
        </div>
      ))}

      {assessmentText && (
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <img src={stars} alt="" className="w-6 h-6 mr-2 ml-[76px] opacity-70" />
            <p className="text-xl font-semibold text-gray-700">{displayAssessmentText}</p>
          </div>

          {showAssessmentButtons && (
            <div className="flex flex-row items-start gap-4 mb-2 ml-[76px]">
              <button
                onClick={() => handleAssessmentAnswer('yes')}
                className="flex items-center justify-start gap-2 w-[135.65px] h-[64px] rounded-[16px] border border-gray-200 bg-white hover:border-[5px] hover:border-[#005773] transition-all px-4 ml-[30px] hover:shadow-[0px_2px_4px_0px_#CACACA1A,0px_8px_8px_0px_#CACACA17,0px_17px_10px_0px_#CACACA0D,0px_30px_12px_0px_#CACACA03,0px_47px_13px_0px_#CACACA00]"
              >
                <img src={keyA} alt="A" className="w-8 h-8" />
                <span className="text-[20px] font-bold text-black">Yes</span>
              </button>

              <button
                onClick={() => handleAssessmentAnswer('no')}
                className="flex items-center justify-start gap-2 w-[135.65px] h-[64px] rounded-[16px] border border-gray-200 bg-white hover:border-[5px] hover:border-[#005773] transition-all px-4 ml-[30px] hover:shadow-[0px_2px_4px_0px_#CACACA1A,0px_8px_8px_0px_#CACACA17,0px_17px_10px_0px_#CACACA0D,0px_30px_12px_0px_#CACACA03,0px_47px_13px_0px_#CACACA00]"
              >
                <img src={keyB} alt="B" className="w-8 h-8" />
                <span className="text-[20px] font-bold text-black">No</span>
              </button>
            </div>
          )}
        </div>
      )}

      {isLoadingAssessment && (
        <div className="flex flex-col items-center justify-center py-8 mt-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#005773] mb-4"></div>
          <p className="text-gray-700 text-lg">Processing your assessment results...</p>
        </div>
      )}
    </div>
  );
};

export default AssessmentComponent;
