import React from 'react';
import { Game } from '../engine';
import { GameComponentProps, GameResult, GameStats } from '../common/types';
import * as gameData from '../engine/data';
import { useGameStatsStore } from '../../store/gameStatsStore';
import { useGameWebSocket } from '../../hooks/useGameWebSocket';
import logger from '../../utils/logger';

const MemoryGame1: React.FC<GameComponentProps> = ({
  studentId,
  gameId,
  sessionId,
  sessionDetails,
  onFinish,
}) => {
  // Pobieramy zadania typu memory-game1
  const memoryTasks = gameData.getTasksByType('memory-game1');

  const addGameStats = useGameStatsStore(state => state.addGameStats);
  const { sendGameStats } = useGameWebSocket();

  const handleGameFinish = async (stats: GameStats) => {
    const currentEndTime = Date.now();
    const calculatedTimeInSeconds: number = Math.round((currentEndTime - stats.startTime) / 1000);

    // Upewniamy się, że studentId jest stringiem
    const studentIdStr = String(studentId);

    // Obliczamy poprawny procent ukończenia
    const totalQuestions = stats.totalQuestions || memoryTasks.length;
    const correctAnswers = stats.correctAnswers || 0;
    const completionPercentage = Math.min(100, Math.round((correctAnswers / totalQuestions) * 100));

    const updatedStats: GameStats = {
      ...stats,
      studentId: studentIdStr,
      gameId: gameId,
      totalQuestions: totalQuestions,
      correctAnswers: correctAnswers,
      incorrectAnswers: stats.incorrectAnswers || 0,
      completionPercentage: completionPercentage,
      endTime: currentEndTime,
      timeInSeconds: calculatedTimeInSeconds,
    };

    // Stats saved

    // Dodajemy statystyki do stanu
    addGameStats(updatedStats);

    // Wysyłamy statystyki przez WebSocket używając React Query
    try {
      const sent = await sendGameStats(updatedStats);
      if (sent) {
        // Stats sent successfully
      } else {
        // Stats queued for later sending
      }
    } catch (error) {
      logger.error('Failed to send game stats via WebSocket:', error);
    }

    // Tworzymy wynik gry do przekazania dalej
    const result: GameResult = {
      studentId: studentIdStr,
      gameId: updatedStats.gameId,
      correctAnswers: correctAnswers,
      totalQuestions: totalQuestions,
      timeInSeconds: calculatedTimeInSeconds,
    };

    if (onFinish) {
      onFinish(result);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Game
        tasks={memoryTasks}
        studentId={studentId}
        gameId={gameId}
        sessionId={sessionId}
        sessionDetails={sessionDetails}
        onFinish={handleGameFinish}
      />
    </div>
  );
};

export default MemoryGame1;
