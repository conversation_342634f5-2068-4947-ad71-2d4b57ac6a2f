import { useParams } from 'react-router-dom';
import { useUserStore } from '../store/userStore';

/**
 * Hook do pobierania ID studenta w następującej kolejności priorytetów:
 * 1. ID z parametrów URL (np. /student/:id)
 * 2. ID studenta z danych użytkownika (user.student.id)
 * 3. ID użytkownika jako fallback (user.user_id)
 * 4. null jeśli nic nie zostało znalezione
 */
export const useCurrentStudentId = (): number | null => {
  const params = useParams<{ id?: string }>();
  const { user } = useUserStore();

  // Priorytet 1: ID z URL (dla nauczycieli przeglądających konkretnego studenta)
  if (params.id) {
    const urlStudentId = parseInt(params.id, 10);
    if (!isNaN(urlStudentId)) {
      return urlStudentId;
    }
  }

  // Priorytet 2: ID studenta z danych użytkownika (dla studentów)
  if (user?.student?.id) {
    return user.student.id;
  }

  // Fallback do user_id jeśli nie ma student.id
  if (user?.user_id) {
    return user.user_id;
  }

  // Brak ID studenta
  return null;
};

export default useCurrentStudentId;
