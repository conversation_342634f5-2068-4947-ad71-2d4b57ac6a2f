import { useEffect } from 'react';
import { authService, UserData } from '../services/authService';
import { useUserStore } from '../store/userStore';

export const useAuth = () => {
  const { user, setUser, loadAvatar } = useUserStore();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const userData = await authService.getCurrentUser();
        console.log('userData', userData);
        setUser(userData);

        if (userData && !userData.avatar_url) {
          try {
            await loadAvatar();
          } catch (avatarError) {
            console.error('Error loading avatar:', avatarError);
            window.location.href = '/dashboard/settings/avatar-creator';
          }
        }
      } catch (_error) {
        setUser(null);
      }
    };

    if (!user) {
      fetchUser();
    }
  }, [user, setUser, loadAvatar]);

  return { user, loading: !user };
};
