import React, { useState, useEffect } from 'react';
import { GameRendererProps } from '../types';
import { MemoryTask } from '../types';
import { Shuffle } from 'lucide-react';
import { gameWebSocket } from '../../common/websocket';
import logger from '../../../utils/logger';

interface Card {
  id: number;
  value: string;
  isFlipped: boolean;
  isMatched: boolean;
}

const MemoryRenderer: React.FC<GameRendererProps> = props => {
  const {
    task,
    onAnswer,
    showFeedback = false,
    currentTaskIndex = 0,
    totalTasks = 1,
    sessionId, // Dodano sessionId z props
    stats = {
      correctAnswers: 0,
      incorrectAnswers: 0,
      totalQuestions: 1,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: '',
      studentId: '',
    },
  } = props;

  const memoryTask = task as MemoryTask;

  const [cards, setCards] = useState<Card[]>([]);
  const [flippedCards, setFlippedCards] = useState<number[]>([]);
  const [matchedPairs, setMatchedPairs] = useState<number>(0);
  const [moves, setMoves] = useState<number>(0);

  useEffect(() => {
    initializeCards();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [memoryTask]);

  const initializeCards = () => {
    const initialCards: Card[] = [];

    // Handle both data structures: pairs (string[]) or cards (object[])
    if (memoryTask.pairs && Array.isArray(memoryTask.pairs)) {
      // Use pairs structure
      memoryTask.pairs.forEach((pair, index) => {
        initialCards.push({
          id: index * 2,
          value: pair,
          isFlipped: false,
          isMatched: false,
        });

        initialCards.push({
          id: index * 2 + 1,
          value: pair,
          isFlipped: false,
          isMatched: false,
        });
      });
    } else if (memoryTask.cards && Array.isArray(memoryTask.cards)) {
      // Use cards structure - convert to our internal format
      memoryTask.cards.forEach((card, index) => {
        initialCards.push({
          id: index,
          value: card.value,
          isFlipped: false,
          isMatched: card.matched,
        });
      });
    } else {
      // Fallback: create default cards
      const defaultPairs = ['A', 'B', 'C', 'D', 'E', 'F'];
      defaultPairs.forEach((pair, index) => {
        initialCards.push({
          id: index * 2,
          value: pair,
          isFlipped: false,
          isMatched: false,
        });

        initialCards.push({
          id: index * 2 + 1,
          value: pair,
          isFlipped: false,
          isMatched: false,
        });
      });
    }

    const shuffledCards = shuffleArray([...initialCards]);
    setCards(shuffledCards);
    setFlippedCards([]);
    setMatchedPairs(0);
    setMoves(0);
  };

  const shuffleArray = (array: Card[]): Card[] => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  };

  const handleCardClick = (id: number) => {
    if (showFeedback) return;

    if (
      cards.find(card => card.id === id)?.isFlipped ||
      cards.find(card => card.id === id)?.isMatched
    ) {
      return;
    }

    if (flippedCards.length === 2) return;

    const updatedCards = cards.map(card => (card.id === id ? { ...card, isFlipped: true } : card));

    setCards(updatedCards);

    const newFlippedCards = [...flippedCards, id];
    setFlippedCards(newFlippedCards);

    if (newFlippedCards.length === 2) {
      setMoves(moves + 1);

      const firstCardId = newFlippedCards[0];
      const secondCardId = newFlippedCards[1];

      const firstCard = updatedCards.find(card => card.id === firstCardId);
      const secondCard = updatedCards.find(card => card.id === secondCardId);

      if (firstCard?.value === secondCard?.value) {
        setTimeout(() => {
          const matchedCards = updatedCards.map(card =>
            card.id === firstCardId || card.id === secondCardId
              ? { ...card, isMatched: true }
              : card
          );

          setCards(matchedCards);
          setFlippedCards([]);
          const newMatchedPairs = matchedPairs + 1;
          setMatchedPairs(newMatchedPairs);

          // Calculate total pairs based on available data structure
          const totalPairs =
            memoryTask.pairs?.length || (memoryTask.cards ? memoryTask.cards.length / 2 : 6);

          // Update stats after each successful pair
          if (stats) {
            const currentTime = Date.now();

            // Initialize progress if it doesn't exist
            if (!stats.progress) {
              stats.progress = [];
            }

            // Add new step only if it doesn't exist yet
            const stepExists = stats.progress.some(
              p => p.step === newMatchedPairs && p.correct === true
            );
            if (!stepExists) {
              stats.progress.push({
                step: newMatchedPairs,
                correct: true,
              });
            }

            const updatedStats = {
              ...stats,
              sessionId: sessionId, // Używaj prawdziwego sessionId
              correctAnswers: newMatchedPairs,
              totalQuestions: totalPairs,
              completionPercentage: Math.round((newMatchedPairs / totalPairs) * 100),
              endTime: currentTime,
              timeInSeconds: Math.round((currentTime - stats.startTime) / 1000),
              progress: [...stats.progress], // Kopiuj istniejący progress
            };

            // Send stats via WebSocket after each pair
            try {
              gameWebSocket.sendGameStats(updatedStats);
              // Stats sent after pair match
            } catch (error) {
              logger.error('[MemoryRenderer] Failed to send stats:', error);
            }

            // Update the stats object
            Object.assign(stats, updatedStats);
          }

          // Check if game is complete
          if (newMatchedPairs === totalPairs) {
            // Game completed
            onAnswer(true, totalPairs);
          }
        }, 1000);
      } else {
        // Send stats for incorrect attempt
        if (stats) {
          const currentTime = Date.now();

          // Inicjalizuj progress jeśli nie istnieje
          if (!stats.progress) {
            stats.progress = [];
          }

          // Add incorrect move only if it doesn't exist for this move
          const moveStep = moves + 1;
          const stepExists = stats.progress.some(p => p.step === moveStep && p.correct === false);
          if (!stepExists) {
            stats.progress.push({
              step: moveStep,
              correct: false,
            });
          }

          const updatedStats = {
            ...stats,
            sessionId: sessionId, // Używaj prawdziwego sessionId
            incorrectAnswers: (stats.incorrectAnswers || 0) + 1,
            endTime: currentTime,
            timeInSeconds: Math.round((currentTime - stats.startTime) / 1000),
            progress: [...stats.progress], // Kopiuj istniejący progress
          };

          try {
            gameWebSocket.sendGameStats(updatedStats);
            // Stats sent after incorrect attempt
          } catch (error) {
            logger.error('[MemoryRenderer] Failed to send stats:', error);
          }

          Object.assign(stats, updatedStats);
        }

        setTimeout(() => {
          const resetCards = updatedCards.map(card =>
            card.id === firstCardId || card.id === secondCardId
              ? { ...card, isFlipped: false }
              : card
          );

          setCards(resetCards);
          setFlippedCards([]);
        }, 1000);
      }
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="w-full mb-8">
        <div className="flex items-center justify-center mb-4">
          <Shuffle className="w-8 h-8 text-indigo-600 mr-2" />
          <h1 className="text-3xl md:text-4xl font-bold text-indigo-700">Memory Game</h1>
        </div>

        <div className="flex justify-between items-center px-2">
          <div className="text-sm md:text-base text-gray-700 font-medium">
            Level: <span className="text-indigo-700">{currentTaskIndex + 1}</span> / {totalTasks}
          </div>

          <div className="bg-gradient-to-r from-indigo-500 to-indigo-700 rounded-full h-2 w-1/2 mx-2">
            <div
              className="bg-gradient-to-r from-indigo-300 to-indigo-500 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${((currentTaskIndex + 1) / totalTasks) * 100}%` }}
            ></div>
          </div>

          <div className="text-sm md:text-base text-gray-700 font-medium">
            Score: <span className="text-indigo-700">{stats.correctAnswers}</span>
          </div>
        </div>
      </div>

      <div className="w-full p-6 rounded-xl bg-white shadow-lg transition-all duration-500 ease-in-out">
        <div className="mb-4 text-center">
          <div className="text-lg font-medium text-gray-700">
            Moves: <span className="text-indigo-700">{moves}</span>
          </div>
          <div className="text-lg font-medium text-gray-700">
            Pairs Found: <span className="text-indigo-700">{matchedPairs}</span> /{' '}
            {memoryTask.pairs?.length || (memoryTask.cards ? memoryTask.cards.length / 2 : 6)}
          </div>
        </div>

        <div className="grid grid-cols-4 gap-4">
          {cards.map(card => (
            <div
              key={card.id}
              onClick={() => handleCardClick(card.id)}
              className={`aspect-square rounded-lg cursor-pointer transition-all duration-300 relative ${
                card.isMatched ? 'bg-green-100 cursor-default' : ''
              } shadow-md hover:shadow-lg`}
            >
              {/* Tył karty (widoczny, gdy karta nie jest odwrócona) */}
              <div
                className={`absolute inset-0 w-full h-full bg-indigo-600 rounded-lg flex items-center justify-center transition-all duration-300 backface-visibility-hidden ${
                  card.isFlipped ? 'rotate-y-180 opacity-0' : 'opacity-100'
                }`}
              ></div>

              {/* Przód karty (widoczny, gdy karta jest odwrócona lub dopasowana) */}
              <div
                className={`absolute inset-0 w-full h-full rounded-lg flex items-center justify-center transition-all duration-300 backface-visibility-hidden ${
                  card.isMatched ? 'bg-green-200 border-2 border-green-500' : 'bg-indigo-100'
                } ${card.isFlipped || card.isMatched ? 'opacity-100' : 'rotate-y-180 opacity-0'}`}
              >
                {(card.isFlipped || card.isMatched) && (
                  <span
                    className={`text-4xl font-bold ${card.isMatched ? 'text-green-700' : 'text-indigo-700'}`}
                  >
                    {card.value}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>

        {showFeedback && (
          <div className="mt-6 p-4 rounded-lg bg-green-100 text-center">
            <p className="text-lg font-medium text-green-700">
              Great job! You completed the level in {props.userAnswer} moves.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MemoryRenderer;
