import React, { useState } from 'react';
import { X } from 'lucide-react';
import { TestAssignmentSettings } from '../../services/testAssignmentService';

interface AssignTestModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (settings: TestAssignmentSettings) => void;
  testId: string;
  testTitle: string;
  studentId?: number;
}

const AssignTestModal: React.FC<AssignTestModalProps> = ({
  isOpen,
  onClose,
  onSave,
  testId,
  testTitle,
  studentId,
}) => {
  const [settings, setSettings] = useState<TestAssignmentSettings>({
    testId,
    testTitle,
    frequency: {
      startDate: new Date().toISOString().split('T')[0],
      repeatEveryUnit: 'week',
      repeatEveryValue: 1,
      endType: 'never',
    },
    studentId,
  });

  const handleSave = () => {
    onSave(settings);
    onClose();
  };

  const handleFrequencyChange = (field: string, value: string | number) => {
    setSettings(prev => ({
      ...prev,
      frequency: {
        ...prev.frequency,
        [field]: value,
      },
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Assign Test</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X size={24} />
          </button>
        </div>

        <div className="space-y-4">
          {/* Test Info */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Test</label>
            <div className="p-3 bg-gray-50 rounded-md">
              <p className="font-medium">{testTitle}</p>
              <p className="text-sm text-gray-600">ID: {testId}</p>
            </div>
          </div>

          {/* Start Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
            <input
              type="date"
              value={settings.frequency.startDate || ''}
              onChange={e => handleFrequencyChange('startDate', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Repeat Every */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Repeat Every</label>
            <select
              value={settings.frequency.repeatEveryUnit || 'week'}
              onChange={e => handleFrequencyChange('repeatEveryUnit', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="day">Day</option>
              <option value="week">Week</option>
              <option value="month">Month</option>
            </select>
          </div>

          {/* End Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">End Type</label>
            <select
              value={settings.frequency.endType || 'never'}
              onChange={e =>
                handleFrequencyChange('endType', e.target.value as 'never' | 'on' | 'after')
              }
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="never">Never</option>
              <option value="on">On specific date</option>
              <option value="after">After number of times</option>
            </select>
          </div>

          {/* End Date (if end type is 'on') */}
          {settings.frequency.endType === 'on' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
              <input
                type="date"
                value={settings.frequency.endDate || ''}
                onChange={e => handleFrequencyChange('endDate', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          )}

          {/* End Times (if end type is 'after') */}
          {settings.frequency.endType === 'after' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Number of Times
              </label>
              <input
                type="number"
                min="1"
                value={settings.frequency.endTimes || ''}
                onChange={e => handleFrequencyChange('endTimes', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter number of times"
              />
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Assign Test
          </button>
        </div>
      </div>
    </div>
  );
};

export default AssignTestModal;
