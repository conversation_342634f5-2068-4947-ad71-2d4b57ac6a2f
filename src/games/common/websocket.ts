import { GameStats } from './types';
import { v4 as uuidv4 } from 'uuid';
import { authService } from '../../services/authService';
import logger from '../../utils/logger';
import { useWebSocketStore } from '../../store/websocketStore';
import { setWebSocketInstance } from '../../services/queries/websocketQueries';
import { clearTodaysSessionsCache } from '../../services/activityService';
import { queryClient } from '../../lib/queryClient';

export class GameWebSocket {
  private ws: WebSocket | null = null;
  private readonly baseUrl: string;
  private maxReconnectAttempts: number = 5;
  private reconnectInterval: number = 5000;

  // Get store instance (non-reactive, for class usage)
  private getStore = () => useWebSocketStore.getState();

  constructor() {
    this.baseUrl = 'wss://api.dev.yubu.ai/ws/game';

    // Reset connection state on initialization
    this.getStore().setConnectionState({
      connectionFailed: false,
      isConnected: false,
      isConnecting: false,
    });

    // Nie łączymy automatycznie - połączenie będzie inicjowane na żądanie
  }

  private generateSessionId(): string {
    return uuidv4();
  }

  private getToken(): string | null {
    // Pobierz token z serwisu uwierzytelniania
    const token = authService.getAccessToken();

    if (!token) {
      logger.warn('Nie znaleziono tokenu uwierzytelniającego dla WebSocket');
    }

    return token;
  }

  private getWebSocketUrl(): string {
    const token = this.getToken();
    if (!token) {
      return this.baseUrl;
    }

    return `${this.baseUrl}?token=${token}`;
  }

  connect() {
    const store = this.getStore();

    // Sprawdzamy, czy już jesteśmy połączeni lub w trakcie łączenia
    if (store.isConnecting || (this.ws && this.ws.readyState === WebSocket.CONNECTING)) {
      // Already connecting, skipping
      return;
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      // Already connected, skipping
      return;
    }

    // Ustawiamy flagę, że próbowaliśmy się połączyć
    store.setAttemptedConnection(true);

    // Resetujemy flagę connectionFailed przy próbie połączenia
    store.setConnectionState({ connectionFailed: false });

    // Zamknij istniejące połączenie, jeśli istnieje
    if (this.ws) {
      try {
        this.ws.close();
      } catch (_error) {
        // Ignorujemy błąd
      }
      this.ws = null;
    }

    try {
      store.setConnectionState({ isConnecting: true });

      const wsUrl = this.getWebSocketUrl();
      // Attempting to connect to WebSocket
      this.ws = new WebSocket(wsUrl);

      // Set WebSocket instance for React Query mutations
      setWebSocketInstance(this.ws);

      this.ws.onopen = () => {
        // Connection established successfully

        const currentStore = this.getStore();
        currentStore.resetReconnectAttempts();
        currentStore.setConnectionState({
          isConnecting: false,
          connectionFailed: false,
          isConnected: true,
        });

        // Wyślij zaległe wiadomości po połączeniu
        const queuedMessages = currentStore.messageQueue;
        if (queuedMessages.length > 0) {
          // Sending queued messages
          queuedMessages.forEach((stats: GameStats) => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
              this.ws.send(JSON.stringify(stats));
            }
          });
          currentStore.clearQueue();
        }
      };

      this.ws.onmessage = _event => {
        // Tylko logujemy, że otrzymaliśmy wiadomość
      };

      this.ws.onclose = event => {
        logger.log(
          `[GameWebSocket] Połączenie zamknięte z kodem: ${event.code}, powód: ${event.reason}`
        );

        const currentStore = this.getStore();
        currentStore.setConnectionState({
          isConnected: false,
          isConnecting: false,
        });

        // Clear WebSocket instance
        setWebSocketInstance(null);

        // Nie ustawiamy connectionFailed = true przy normalnym zamknięciu
        if (event.code !== 1000 && event.code !== 1001) {
          this.handleClose();
        }
      };

      this.ws.onerror = error => {
        logger.error('[GameWebSocket] Błąd WebSocket:', error);

        const currentStore = this.getStore();
        currentStore.setConnectionState({ isConnecting: false });
        // Nie ustawiamy connectionFailed = true od razu, poczekajmy na zdarzenie onclose
      };
    } catch (error) {
      logger.error('[GameWebSocket] Błąd podczas tworzenia połączenia:', error);
      const currentStore = this.getStore();
      currentStore.setConnectionState({ isConnecting: false });
      this.handleClose();
    }
  }

  private handleClose = () => {
    const store = this.getStore();
    store.setConnectionState({ isConnected: false });
    this.attemptReconnect();
  };

  private attemptReconnect() {
    const store = this.getStore();
    if (store.reconnectAttempts < this.maxReconnectAttempts) {
      store.incrementReconnectAttempts();
      logger.log(
        `[GameWebSocket] Próba ponownego połączenia ${store.reconnectAttempts}/${this.maxReconnectAttempts}`
      );

      // Resetujemy flagę hasAttemptedConnection, aby umożliwić ponowne połączenie
      store.setAttemptedConnection(false);

      setTimeout(() => this.connect(), this.reconnectInterval);
    } else {
      logger.warn('[GameWebSocket] Przekroczono maksymalną liczbę prób ponownego połączenia');
      this.saveQueuedMessagesLocally();
    }
  }

  private saveQueuedMessagesLocally() {
    // Używamy Zustand store zamiast localStorage
    const store = this.getStore();
    const queueLength = store.getQueueLength();
    if (queueLength > 0) {
      // Clearing message queue
      store.clearQueue();
    }
  }

  sendGameStats(stats: GameStats) {
    // Upewnij się, że wszystkie wymagane pola są obecne
    const formattedStats = {
      sessionId: stats.sessionId, // Używaj sessionId z stats, fallback na this.sessionId
      gameId: stats.gameId,
      studentId: stats.studentId,
      totalQuestions: stats.totalQuestions || 0,
      correctAnswers: stats.correctAnswers || 0,
      incorrectAnswers: stats.incorrectAnswers || 0,
      completionPercentage: stats.completionPercentage || 0,
      startTime: stats.startTime,
      endTime: stats.endTime || Date.now(),
      timeInSeconds:
        stats.timeInSeconds || Math.round((stats.endTime || Date.now()) - stats.startTime) / 1000,
      progress: stats.progress || [],
    };

    const store = this.getStore();

    // Sending game stats

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        const message = JSON.stringify(formattedStats);
        this.ws.send(message);
        // Stats sent successfully

        // Invaliduj cache po wysłaniu danych, żeby frontend pobrał świeże dane
        clearTodaysSessionsCache();

        // Invaliduj React Query cache dla sesji
        queryClient.invalidateQueries({ queryKey: ['todaysSessions'] });
        queryClient.invalidateQueries({ queryKey: ['session'] });

        // Cache invalidated after sending stats

        return true;
      } catch (error) {
        logger.error(`[GameWebSocket] Błąd podczas wysyłania statystyk:`, error);
        // Dodajemy do kolejki, aby spróbować ponownie później
        store.addToQueue(formattedStats);
        return false;
      }
    } else {
      logger.warn(
        `[GameWebSocket] WebSocket nie jest otwarty, dodawanie do kolejki i próba połączenia`
      );
      store.addToQueue(formattedStats);

      // Próbujemy połączyć się ponownie
      this.connect();
      return false;
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    // Clear WebSocket instance
    setWebSocketInstance(null);

    const store = this.getStore();

    // Czyścimy kolejkę wiadomości bez zapisywania lokalnie
    const queueLength = store.getQueueLength();
    if (queueLength > 0) {
      logger.log(
        `[GameWebSocket] Rozłączanie: czyszczenie kolejki wiadomości (${queueLength} wiadomości)`
      );
      store.clearQueue();
    }

    // Resetujemy flagę połączenia, aby można było ponownie połączyć się po odświeżeniu strony
    store.setAttemptedConnection(false);
    // Disconnected
  }

  // Metoda do resetowania flagi połączenia - może być wywołana z zewnątrz
  resetConnectionState() {
    const store = this.getStore();
    store.resetState();
    // WebSocket connection state reset
  }
}

export const gameWebSocket = new GameWebSocket();
