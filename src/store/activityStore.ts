// activityStore.ts
import { create } from 'zustand';
import {
  Activity,
  ActivityDetail,
  activityService,
  UpdateProgressPayload,
} from '../services/activityService';

interface ActivityState {
  activities: Activity[];
  currentActivity: ActivityDetail | null;
  isLoading: boolean;
  error: string | null;

  fetchActivities: () => Promise<void>;
  fetchActivityDetails: (activityId: string) => Promise<void>;
  startActivity: (activityId: string) => Promise<void>;
  updateProgress: (payload: UpdateProgressPayload) => Promise<void>;
  completeActivity: (activityId: string) => Promise<void>;
}

export const useActivityStore = create<ActivityState>((set, get) => ({
  activities: [],
  currentActivity: null,
  isLoading: false,
  error: null,

  fetchActivities: async () => {
    set({ isLoading: true, error: null });
    try {
      const activities = await activityService.getStudentActivities();
      set({ activities, isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  fetchActivityDetails: async (activityId: string) => {
    set({ isLoading: true, error: null });
    try {
      const activityDetail = await activityService.getActivityDetails(activityId);
      set({ currentActivity: activityDetail, isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  startActivity: async (activityId: string) => {
    set({ isLoading: true, error: null });
    try {
      await activityService.startActivity(activityId);
      // Aktualizuj listę aktywności po rozpoczęciu
      await get().fetchActivities();
      set({ isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  updateProgress: async (payload: UpdateProgressPayload) => {
    set({ isLoading: true, error: null });
    try {
      await activityService.updateProgress(payload);
      // Aktualizuj szczegóły aktywności po aktualizacji postępu
      if (get().currentActivity?.id === payload.activityId) {
        await get().fetchActivityDetails(payload.activityId);
      }
      set({ isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  completeActivity: async (activityId: string) => {
    set({ isLoading: true, error: null });
    try {
      await activityService.completeActivity(activityId);
      // Aktualizuj listę aktywności i szczegóły po ukończeniu
      await get().fetchActivities();
      if (get().currentActivity?.id === activityId) {
        await get().fetchActivityDetails(activityId);
      }
      set({ isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },
}));
