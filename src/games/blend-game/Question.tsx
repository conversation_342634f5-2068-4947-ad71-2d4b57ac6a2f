import React from 'react';
import { Task } from '@games/common/types';

interface QuestionProps {
  task?: Task;
  word?: string;
  image?: string;
  isCorrect?: boolean;
  showFeedback?: boolean;
}

const Question: React.FC<QuestionProps> = ({ task, word, image, isCorrect, showFeedback }) => {
  const displayWord = task ? task.word : word;
  const displayImage = task ? task.image : image;

  let borderClass = 'border-4 border-blue-400';

  if (showFeedback) {
    borderClass = isCorrect
      ? 'border-4 border-green-500 animate-pulse'
      : 'border-4 border-red-500 animate-pulse';
  }

  return (
    <div className="flex flex-col md:flex-row items-center gap-6 mb-8">
      <div className="w-full md:w-1/2 flex justify-center">
        <div
          className={`relative w-full max-w-sm aspect-square rounded-2xl overflow-hidden ${borderClass} shadow-lg`}
        >
          <img src={displayImage} alt="Word picture" className="w-full h-full object-cover" />
        </div>
      </div>

      <div className="w-full md:w-1/2 flex flex-col items-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-2 text-blue-800">
          What&apos;s the missing blend?
        </h2>
        <div className="text-5xl md:text-6xl font-bold my-6 text-center tracking-wide text-blue-900">
          {displayWord &&
            displayWord.split('__').map((part, index, array) => (
              <React.Fragment key={index}>
                {part}
                {index < array.length - 1 && (
                  <span className="inline-block w-16 h-12 border-b-4 border-dotted border-blue-500 mx-1 align-middle"></span>
                )}
              </React.Fragment>
            ))}
        </div>
      </div>
    </div>
  );
};

export default Question;
