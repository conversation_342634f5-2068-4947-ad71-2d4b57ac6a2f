import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { chatService } from '../services/chatService';

export interface RecentChat {
  id: string | number;
  student_id: string | number;
  first_name: string;
  last_name: string;
  session_id: string;
  last_message_time: string;
}

interface ChatStoreState {
  recentChats: RecentChat[];
  loading: boolean;
  error: string | null;
  fetchRecentChats: () => Promise<void>;
}

export const useChatStore = create<ChatStoreState>()(
  devtools(
    set => ({
      recentChats: [],
      loading: false,
      error: null,
      fetchRecentChats: async () => {
        set({ loading: true, error: null });
        try {
          const data = await chatService.getLastActiveStudents();
          // Ograniczamy do 10 najnowszych czatów
          set({
            recentChats: Array.isArray(data)
              ? data.slice(0, 3).map(chat => ({
                  id: chat.student_id ?? '',
                  student_id: chat.student_id ?? '',
                  first_name: chat.first_name ?? '',
                  last_name: chat.last_name ?? '',
                  session_id: chat.session_id ?? '',
                  last_message_time: chat.last_message_time,
                }))
              : [],
            loading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Error loading chats';
          set({ recentChats: [], loading: false, error: errorMessage });
        }
      },
    }),
    { name: 'chat-store' }
  )
);

export default useChatStore;
