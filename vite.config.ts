/* eslint-disable no-console */
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { ProxyOptions } from 'vite';
import * as path from 'path';

const proxy: ProxyOptions = {
  target: 'http://localhost:3001',
  changeOrigin: true,
  secure: false,
  // rewrite: path => path.replace(/^\/api/, ''),
  configure: (proxy, _options) => {
    proxy.on('error', (err, _req, _res) => {
      console.log('proxy error', err);
    });
    proxy.on('proxyReq', (_proxyReq, req, _res) => {
      console.log('Sending Request to the Target:', req.method, req.url);
    });
    proxy.on('proxyRes', (proxyRes, req, _res) => {
      console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
    });
  },
};

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@games': path.resolve(__dirname, 'src/games'),
      src: path.resolve(__dirname, 'src'),
    },
  },
  server: {
    proxy: {
      '/api': proxy,
    },
  },
});
