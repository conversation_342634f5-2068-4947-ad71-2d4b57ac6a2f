import React, { useState } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import { testService } from '../../services/testService';
import testAssignmentService from '../../services/testAssignmentService';
import { Test, ScaleOption, Question } from '../../types/test';
import { toast } from 'sonner';
import { RadioGroup } from '../../components/ui/RadioGroup';
import { useUserStore } from '../../store/userStore';

const TestDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useUserStore();
  const sessionId = searchParams.get('session_id');

  // If we have a session_id, we're dealing with an assigned test
  const { data: test, isPending } = useQuery<Test>({
    queryKey: sessionId ? ['sessionTest', sessionId] : ['test', id],
    queryFn: async () => {
      if (sessionId) {
        // Get test details from session
        const sessionDetails = await testAssignmentService.getSessionAssessmentDetails(sessionId);
        // Convert session details to Test format
        return {
          id: sessionDetails.assessment.assessment_id,
          title:
            sessionDetails.assessment.title ||
            sessionDetails.assessment.assessment_id
              .replace(/-/g, ' ')
              .replace(/_/g, ' ')
              .split(' ')
              .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(' '),
          questions:
            sessionDetails.assessment.questions?.map((q: string, index: number) => ({
              id: index + 1,
              question: q,
            })) || [],
          scale: [
            { value: 1, label: 'Strongly Disagree' },
            { value: 2, label: 'Disagree' },
            { value: 3, label: 'Neutral' },
            { value: 4, label: 'Agree' },
            { value: 5, label: 'Strongly Agree' },
          ],
        } as Test;
      } else {
        // Regular test preview
        return testService.getTestById(id!);
      }
    },
    enabled: !!(sessionId || id),
  });

  const [answers, setAnswers] = useState<Record<number, number>>({});
  const [currentIndex, setCurrentIndex] = useState(0);

  const mutation = useMutation({
    mutationFn: async (payload: Record<number, number>) => {
      if (sessionId) {
        // Submit through assignment service
        const formattedAnswers = Object.entries(payload).map(([questionId, answer]) => ({
          question_id: parseInt(questionId),
          value: answer,
        }));

        await testAssignmentService.submitAssessmentAnswers({
          session_id: sessionId,
          answers: formattedAnswers,
        });
      } else {
        // Regular test submission (preview mode)
        await testService.submitAnswers();
      }
    },
    onSuccess: () => {
      toast.success('Test submitted successfully!');
      // Navigate back to tests page after successful submission
      setTimeout(() => {
        navigate('/dashboard/tests');
      }, 1500);
    },
    onError: error => {
      console.error('Error submitting test:', error);
      toast.error('Failed to submit test. Please try again.');
    },
  });

  const handleSelect = (questionId: number, value: number) => {
    setAnswers(prev => ({ ...prev, [questionId]: value }));
  };

  const next = () => setCurrentIndex(i => Math.min(i + 1, test!.questions.length - 1));
  const prev = () => setCurrentIndex(i => Math.max(i - 1, 0));

  const handleSubmit = async () => {
    if (Object.keys(answers).length !== test!.questions.length) {
      toast.error('Please answer all questions before submitting.');
      return;
    }

    await mutation.mutateAsync(answers);
  };

  if (isPending || !test)
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );

  const currentQuestion = test.questions[currentIndex];

  if (!currentQuestion) {
    return (
      <div className="max-w-xl mx-auto p-6 bg-white rounded-lg shadow">
        <h1 className="text-2xl font-bold mb-4 text-center">{test.title}</h1>
        <p className="text-center text-gray-600">No questions available for this test.</p>
      </div>
    );
  }

  return (
    <div className="max-w-xl mx-auto p-6 bg-white rounded-lg shadow">
      <div className="mb-4">
        <h1 className="text-2xl font-bold text-center">{test.title}</h1>
        {sessionId && (
          <p className="text-sm text-gray-500 text-center mt-2">
            Session: {sessionId.slice(0, 8)}...
          </p>
        )}
      </div>

      <div className="mb-6">
        <p className="mb-4 font-medium">{currentQuestion.question}</p>

        {test.scale.map((option: ScaleOption) => (
          <label key={option.value} className="flex items-center gap-3 mb-2 cursor-pointer">
            <input
              type="radio"
              name={`q-${currentQuestion.id}`}
              value={option.value}
              checked={answers[currentQuestion.id] === option.value}
              onChange={() => handleSelect(currentQuestion.id, option.value)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500"
            />
            <span>{option.label}</span>
          </label>
        ))}
      </div>

      <div className="flex justify-between items-center">
        <button
          onClick={prev}
          disabled={currentIndex === 0}
          className="bg-gray-200 text-gray-800 px-4 py-2 rounded disabled:opacity-50 hover:bg-gray-300 transition-colors"
        >
          Previous
        </button>

        {currentIndex < test.questions.length - 1 ? (
          <button
            onClick={next}
            disabled={answers[currentQuestion.id] === undefined}
            className="bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50 hover:bg-blue-700 transition-colors"
          >
            Next
          </button>
        ) : (
          <button
            onClick={handleSubmit}
            disabled={Object.keys(answers).length !== test.questions.length || mutation.isPending}
            className="bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50 hover:bg-green-700 transition-colors"
          >
            {mutation.isPending ? 'Submitting...' : 'Submit'}
          </button>
        )}
      </div>

      <p className="text-sm text-gray-500 mt-4 text-center">
        Question {currentIndex + 1} of {test.questions.length}
      </p>

      {!sessionId && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-sm text-yellow-800">
            <strong>Preview Mode:</strong> This is a preview of the test. Submissions in preview
            mode are not saved.
          </p>
        </div>
      )}
    </div>
  );
};

export default TestDetailsPage;
