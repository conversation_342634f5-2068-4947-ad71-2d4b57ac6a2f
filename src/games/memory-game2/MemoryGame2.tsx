import React, { useState } from 'react';
import { Game } from '../engine';
import { GameComponentProps, GameResult, GameStats } from '../common/types';
import * as gameData from '../engine/data';
import { useGameStatsStore } from '../../store/gameStatsStore';
import { useGameWebSocket } from '../../hooks/useGameWebSocket';
import logger from '../../utils/logger';
import GameResults from '../common/GameResults';

const MemoryGame2: React.FC<GameComponentProps> = ({
  studentId,
  gameId,
  sessionId,
  sessionDetails,
  onFinish,
}) => {
  // Debug: check sessionId and sessionDetails
  const memoryTasks = gameData.getTasksByType('memory-game2');

  const addGameStats = useGameStatsStore(state => state.addGameStats);
  const { sendGameStats } = useGameWebSocket();

  const handleGameFinish = async (stats: GameStats) => {
    const currentEndTime = Date.now();
    const calculatedTimeInSeconds: number = Math.round((currentEndTime - stats.startTime) / 1000);

    // Make sure studentId is a string
    const studentIdStr = String(studentId);

    // Calculate the correct completion percentage
    const totalQuestions = stats.totalQuestions || memoryTasks.length;
    const correctAnswers = stats.correctAnswers || 0;
    const completionPercentage = Math.min(100, Math.round((correctAnswers / totalQuestions) * 100));

    const updatedStats: GameStats = {
      ...stats,
      studentId: studentIdStr,
      gameId: gameId,
      totalQuestions: totalQuestions,
      correctAnswers: correctAnswers,
      incorrectAnswers: stats.incorrectAnswers || 0,
      completionPercentage: completionPercentage,
      endTime: currentEndTime,
      timeInSeconds: calculatedTimeInSeconds,
    };

    // Stats saved

    // Add statistics to the state
    addGameStats(updatedStats);

    // Send statistics via WebSocket using React Query
    try {
      const sent = await sendGameStats(updatedStats);
      if (sent) {
        // Stats sent successfully
      } else {
        // Stats queued for later sending
      }
    } catch (error) {
      logger.error('Failed to send game stats via WebSocket:', error);
    }

    // Create game result to pass forward
    const result: GameResult = {
      studentId: studentIdStr,
      gameId: updatedStats.gameId,
      correctAnswers: correctAnswers,
      totalQuestions: totalQuestions,
      timeInSeconds: calculatedTimeInSeconds,
    };

    if (onFinish) {
      onFinish(result);
    }
  };

  // Add state to track if the game has been completed
  const [gameCompleted, setGameCompleted] = useState(false);
  const [finalStats, setFinalStats] = useState<GameStats | null>(null);

  // Modify the game completion handler to save statistics and set completion state
  const handleGameFinishWithUI = async (stats: GameStats) => {
    // First execute standard game completion logic
    await handleGameFinish(stats);

    // Save statistics and set completion state
    setFinalStats(stats);
    setGameCompleted(true);
  };

  const handleRestart = () => {
    setGameCompleted(false);
    setFinalStats(null);
  };

  // If the game is completed, display the GameResults component
  if (gameCompleted && finalStats) {
    return (
      <div className="container mx-auto px-4 py-8">
        <GameResults stats={finalStats} onRestart={handleRestart} />
      </div>
    );
  }

  // Otherwise display the game
  return (
    <div className="container mx-auto px-4 py-8">
      <Game
        tasks={memoryTasks}
        studentId={studentId}
        gameId={gameId}
        sessionId={sessionId}
        sessionDetails={sessionDetails}
        onFinish={handleGameFinishWithUI}
      />
    </div>
  );
};

export default MemoryGame2;
