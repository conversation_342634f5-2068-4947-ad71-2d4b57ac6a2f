## 1. WebSocket `/ws/game`

**Description**:  
This WebSocket endpoint records real-time session data for learning games (interventions). A user (teacher or parent) can send game progress for a selected student. Each message is stored in the `game_sessions` table.

> **Planned**: The term `game` will be renamed to `intervention` across the API and database schema for clarity and consistency with educational terminology.

---

### Authorization:

- Requires a valid JWT token via query parameter:

```
wss://api.dev.yubu.ai/ws/game?token=<JWT>
```

### Authorization:

- The token must be passed as a **query parameter**:  
  `?token=eyJhbGciOi...`

---

### Message format (client → server):

Each message should contain session progress for a given game/intervention step.

#### Example (in-progress step):

```json
{
  "sessionId": "f7d8393e-e603-41b9-bab9-5cd2f9b27b16",
  "gameId": "blend-game",
  "studentId": "3",
  "totalQuestions": 6,
  "correctAnswers": 2,
  "incorrectAnswers": 1,
  "completionPercentage": 50,
  "startTime": 1747091623571,
  "progress": [
    { "step": 1, "correct": true },
    { "step": 2, "correct": true },
    { "step": 3, "correct": false }
  ]
}
```

#### Example (completed session):

```json
{
  "sessionId": "f7d8393e-e603-41b9-bab9-5cd2f9b27b16",
  "gameId": "blend-game",
  "studentId": "3",
  "totalQuestions": 6,
  "correctAnswers": 3,
  "incorrectAnswers": 3,
  "completionPercentage": 100,
  "startTime": 1747091623571,
  "endTime": 1747091633571,
  "progress": [
    { "step": 1, "correct": true },
    { "step": 2, "correct": true },
    { "step": 3, "correct": false },
    { "step": 4, "correct": true },
    { "step": 5, "correct": false },
    { "step": 6, "correct": false }
  ]
}
```

---

### Server behavior:

- Validates and saves each event in the `game_sessions` table.
- If `completionPercentage` is `100` and `endTime` is not provided, the server will auto-generate the `endTime`.
- Sessions are grouped by `sessionId`.
- Each record includes:
  - `user_id` (from token)
  - `student_id`
  - Progress stats

---

### Server responses:

- `"OK"` – successfully stored
- `"Missing 'gameId'"` – field required
- `"Missing 'sessionId'"` – field required
- `"Invalid 'sessionId' format"` – must be UUID
- `"Invalid JSON"` – malformed payload

---

### To be implemented:

- **Role-based validation**: ensure only assigned users (e.g. teacher or parent) can push data for a given `studentId`
- **Rename**: `gameId` → `interventionId` in both API and database schema

---

## 2. POST `/games/schedule_intervention`

**Description**:  
This endpoint allows a teacher or parent to schedule a recurring intervention for a student. The plan includes frequency (e.g. every 1 week), number of sessions per selected day, specific days of the week, a start date, and an optional end condition.

---

### Authorization:

Requires a valid JWT token in the Authorization header:

```
Authorization: Bearer <access_token>
```

#### Request Body:

```json
{
  "student_id": 123,
  "intervention_id": "math-game",
  "repeat_every_value": 1,
  "repeat_every_unit": "week",
  "sessions_per_day": 1,
  "repeat_on": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
  "start_date": "2025-05-16",
  "ends": {
    "mode": "after",
    "after_times": 10
  }
}
```

### `ends` options

| Mode  | Description                        | Required Field |
| ----- | ---------------------------------- | -------------- |
| never | The schedule has no predefined end | –              |
| on    | Ends on a specific date            | `on_date`      |
| after | Ends after X number of sessions    | `after_times`  |

### Field Descriptions

| Field                | Type                       | Required | Description                                                  |
| -------------------- | -------------------------- | -------- | ------------------------------------------------------------ |
| `student_id`         | `int`                      | ✅       | ID of the student (from /students/list                       |
| `intervention_id`    | `str`                      | ✅       | ID of the planned intervention (string, e.g., "math-game")   |
| `repeat_every_value` | `int`                      | ✅       | Number of time units between repeats (e.g. every 1 week)     |
| `repeat_every_unit`  | `"day"` `"week"` `"month"` | ✅       | Time unit for repetition                                     |
| `sessions_per_day`   | `int`                      | ✅       | Number of sessions per day (max 10)                          |
| `repeat_on`          | `List[str]`                | ✅       | Days of the week the intervention should run (Monday–Sunday) |
| `start_date`         | `date`                     | ✅       | First day the intervention starts                            |
| `ends`               | `object`                   | ✅       | End condition of the plan (see table above)                  |

### Responses:

- `200 OK`:

```json
{
  "message": "Intervention scheduled and 10 sessions created.",
  "active_id": "a85a6fc9-13e1-4679-b872-c5d52b2e9c74"
}
```

- `403 Forbidden`: User is not a teacher or parent
- `422 Unprocessable Entity`: Invalid combination of ends.mode and missing fields
- `400 Bad Request`: Invalid request format or token

#### Example Requests:

- `Case 1`: Every day from Monday to Friday, once a day, for 2 weeks (10 sessions in total)

```json
{
  "student_id": 2,
  "intervention_id": "math-game",
  "repeat_every_value": 1,
  "repeat_every_unit": "week",
  "sessions_per_day": 1,
  "repeat_on": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
  "start_date": "2025-05-16",
  "ends": {
    "mode": "after",
    "after_times": 10
  }
}
```

- `Case 2`: Only on Mondays and Wednesdays, twice a day, until a specific date

```json
{
  "student_id": 2,
  "intervention_id": "reading-quiz",
  "repeat_every_value": 1,
  "repeat_every_unit": "week",
  "sessions_per_day": 2,
  "repeat_on": ["Monday", "Wednesday"],
  "start_date": "2025-05-16",
  "ends": {
    "mode": "on",
    "on_date": "2025-06-16"
  }
}
```

- `Case 3`: Every Saturday and Sunday, once a day, with no end date

```json
{
  "student_id": 2,
  "intervention_id": "memory-game",
  "repeat_every_value": 1,
  "repeat_every_unit": "week",
  "sessions_per_day": 1,
  "repeat_on": ["Saturday", "Sunday"],
  "start_date": "2025-05-18",
  "ends": {
    "mode": "never"
  }
}
```

## 3. POST `/games/scheduled_interventions`

**Description:**  
Returns a list of scheduled interventions (recurring assignments/plans) for a given student.
You must provide the student's user_id as student_id in the request body.
The endpoint enforces access control—teachers, parents, and the student themself can only view their own/assigned students' schedules.
**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Returns all planned/scheduled interventions for the selected student, sorted by newest first.

### Authorization:

Requires a valid JWT token in the Authorization header:

```
Authorization: Bearer <access_token>
```

**Access Control:**

- **Teacher (role_id = 1):** Can access students where `students.teacher_id = user_id`.
- **Parent (role_id = 2):** Can access students where `students.parent_id = user_id`.

#### Request Body:

```json
{
  "student_id": 211
}
```

### Responses:

- `200 OK`:

```json
{
  "student_id": 211,
  "interventions": [
    {
      "id": "39b4b78d-295c-4256-be4a-2e7350e6f546",
      "intervention_id": "blend-game--word-building",
      "start_date": "2025-05-25",
      "end_date": "2025-06-04",
      "repeat_on": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
      "repeat_every": 1,
      "sessions_per_day": 1,
      "assigned_by_id": 348,
      "assigned_by_role": "teacher",
      "created_at": "2025-05-26T11:39:30.423367+00:00"
    },
    {
      "id": "65b23c34-017e-455f-97b7-76c11c776645",
      "intervention_id": "blend-game--word-building",
      "start_date": "2025-05-25",
      "end_date": "2025-06-04",
      "repeat_on": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
      "repeat_every": 1,
      "sessions_per_day": 1,
      "assigned_by_id": 348,
      "assigned_by_role": "teacher",
      "created_at": "2025-05-26T01:57:35.266199+00:00"
    },
    {
      "id": "a7b364ac-540e-42d0-94a2-9fe177af086f",
      "intervention_id": "blend-game--word-building",
      "start_date": "2025-05-16",
      "end_date": "2025-05-26",
      "repeat_on": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
      "repeat_every": 1,
      "sessions_per_day": 1,
      "assigned_by_id": 348,
      "assigned_by_role": "teacher",
      "created_at": "2025-05-24T11:36:39.269014+00:00"
    },
    {
      "id": "790b6e77-5269-4520-a044-7b40ac8c9f9c",
      "intervention_id": "blend-game--word-building",
      "start_date": "2025-05-16",
      "end_date": "2025-05-30",
      "repeat_on": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      "repeat_every": 1,
      "sessions_per_day": 1,
      "assigned_by_id": 348,
      "assigned_by_role": "teacher",
      "created_at": "2025-05-24T09:08:55.418475+00:00"
    },
    {
      "id": "be083e73-ec45-452b-9856-6d70be8e6689",
      "intervention_id": "blend-game--quiz",
      "start_date": "2025-05-16",
      "end_date": "2025-05-30",
      "repeat_on": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      "repeat_every": 1,
      "sessions_per_day": 1,
      "assigned_by_id": 348,
      "assigned_by_role": "teacher",
      "created_at": "2025-05-24T09:08:23.589226+00:00"
    },
    {
      "id": "c49694f0-b782-47fa-8f4e-28a7793d45f5",
      "intervention_id": "math-game",
      "start_date": "2025-05-16",
      "end_date": "2025-05-30",
      "repeat_on": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      "repeat_every": 1,
      "sessions_per_day": 1,
      "assigned_by_id": 348,
      "assigned_by_role": "teacher",
      "created_at": "2025-05-24T09:07:17.395418+00:00"
    },
    {
      "id": "d5fd6a92-4103-4149-bbed-1b61a93f96cd",
      "intervention_id": "math-game",
      "start_date": "2025-05-16",
      "end_date": "2025-05-30",
      "repeat_on": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      "repeat_every": 1,
      "sessions_per_day": 1,
      "assigned_by_id": 348,
      "assigned_by_role": "teacher",
      "created_at": "2025-05-23T20:16:20.187291+00:00"
    }
  ]
}
```

- `401 Unauthorized`: Invalid or missing access token.
- `403 Forbidden`: User does not have access to the requested student.

---

## 4. POST `/games/list_sessions_by_active_id`

**Description**:  
Returns a list of all scheduled and completed sessions associated with a specific active_id (shared group ID for a block of scheduled interventions).
Accessible to teachers, parents, and students. Students will only see their own sessions.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Useful for displaying all sessions generated as part of a scheduled intervention (for example, “Show me all upcoming and completed sessions for this plan”).

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "active_id": "a85a6fc9-13e1-4679-b872-c5d52b2e9c74"
}
```

### Responses:

- `200 OK`:

```json
[
  {
    "session_id": "b1b5c159-1a89-4866-99cf-b47b0c687e9c",
    "student_id": 211,
    "game_id": "blend-game--word-building",
    "date": "2025-05-28",
    "status": "planned"
  },
  {
    "session_id": "30c6b9bb-bc4d-497a-b2b6-06385e3b568a",
    "student_id": 211,
    "game_id": "blend-game--word-building",
    "date": "2025-05-29",
    "status": "planned"
  },
  {
    "session_id": "f2d2c91e-39f3-4b3a-8cdb-7a4c8ce99f60",
    "student_id": 211,
    "game_id": "blend-game--word-building",
    "date": "2025-05-27",
    "status": "completed"
  }
]
```

- `403 Forbidden`: User does not have access to the requested sessions.
- `401 Unauthorized`: Invalid or missing access token.
- `500 Internal Server Error`: Unexpected server error (e.g., database failure).

## 4. GET `/games/all_interventions`

**Description**:  
Returns a full list of all available interventions and their metadata.
This is a public endpoint – no authentication is required.
Typically used to allow frontends to list available activities, games, or interventions for assignment and planning.

### Headers:

```
No authentication required.
```

### Responses:

- `200 OK`:

```json
[
    {
        "id": 1,
        "title": "Phonemic Awareness",
        "activities": [
            {
                "title": "Quiz",
                "description": "Say the name of the picture on the left, then circle a picture on the right that starts with the same sound.",
                "images": [
                    "https://d3dttmvg0d6qxx.cloudfront.net/game1.png",
                    "https://d3dttmvg0d6qxx.cloudfront.net/game2.png",
                    "https://d3dttmvg0d6qxx.cloudfront.net/game3.png"
                ],
                "numberOfExercises": 4,
                "game_id": "blend-game--quiz",
                "objective": "Identify words with matching initial sounds.",
                "target_age_group": "5-7",
                "difficulty_level": "easy",
                "categories": [
                    "phonemic awareness",
                    "initial sounds",
                    "matching"
                ]
            },
            {
                "title": "Phonemic Awareness Assessment",
                "description": "Ask the child if the following word pairs rhyme.",
                "images": [
                    "https://d3dttmvg0d6qxx.cloudfront.net/game1.png",
                    "https://d3dttmvg0d6qxx.cloudfront.net/game2.png",
                    "https://d3dttmvg0d6qxx.cloudfront.net/game3.png"
                ],
                "numberOfExercises": 1,
                "game_id": "blend-game--phonemic-awareness-assessment",
                "objective": "Assess the child's ability to recognize rhyming words.",
                "target_age_group": "5-7",
                "difficulty_level": "medium",
                "categories": [
                    "phonemic awareness",
                    "assessment",
                    "rhyming"
                ]
            },
          {
            "title": "Listen to the Beginning",
            "description": "Children name pictures and choose the two words with the same beginning sound.",
            "images": [
              "https://d3dttmvg0d6qxx.cloudfront.net/game1.png",
              "https://d3dttmvg0d6qxx.cloudfront.net/game2.png",
              "https://d3dttmvg0d6qxx.cloudfront.net/game3.png"
            ],
            "numberOfExercises": 1,
            "game_id": "blend-game--listen-to-the-beginning",
            "objective": "Develop ability to detect same initial sounds in words.",
            "target_age_group": "5-8",
            "difficulty_level": "easy",
            "categories": [
              "phonemic awareness",
              "initial sounds"
            ]
          }
  // ... more interventions
]
```

- `404 Not Found`: The interventions file could not be found on the server.
- `500 Internal Server Error`: Unexpected server error (e.g., database failure).
