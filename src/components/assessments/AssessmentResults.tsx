import React, { useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { CheckCircle, ArrowLeft, Home, BarChart3 } from 'lucide-react';
import { useSessionDetails } from '../../services/queries/assessmentQueries';

interface AssessmentResultsProps {
  studentId?: number;
}

const AssessmentResults: React.FC<AssessmentResultsProps> = ({ studentId: _studentId }) => {
  const { assessmentId: _assessmentId, sessionId } = useParams<{
    assessmentId: string;
    sessionId: string;
  }>();
  const navigate = useNavigate();
  const location = useLocation();

  // Check if we came from a completed assessment
  const wasJustCompleted = location.state?.completed;

  const { data: sessionDetails, isLoading, error } = useSessionDetails(sessionId || '');

  // Auto-redirect after 5 seconds if assessment was just completed
  useEffect(() => {
    if (wasJustCompleted) {
      const timer = setTimeout(() => {
        navigate('/dashboard');
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [wasJustCompleted, navigate]);

  const formatTitle = (assessmentId: string) => {
    return assessmentId
      .replace(/-/g, ' ')
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !sessionDetails) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-red-500" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Results</h2>
          <p className="text-gray-600 mb-4">
            {error ? (error as Error).message : 'Assessment results not found'}
          </p>
          <button
            onClick={() => navigate('/dashboard')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4">
        {/* Success Header */}
        {wasJustCompleted && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="flex-1">
                <h2 className="text-lg font-semibold text-green-900">Assessment Completed!</h2>
                <p className="text-green-700">
                  Your responses have been submitted successfully. You&apos;ll be redirected to the
                  dashboard in a few seconds.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Results Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <BarChart3 className="w-10 h-10 text-blue-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Assessment Complete</h1>
            <p className="text-xl text-gray-600">
              {formatTitle(sessionDetails.assessment.assessment_id)}
            </p>
          </div>

          {/* Assessment Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Assessment Details</h3>
              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-gray-500">Assessment:</span>
                  <p className="text-gray-900">{sessionDetails.assessment.title}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Date Completed:</span>
                  <p className="text-gray-900">{formatDate(sessionDetails.session_date)}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Questions:</span>
                  <p className="text-gray-900">
                    {sessionDetails.assessment.questions.length} questions
                  </p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Scale:</span>
                  <p className="text-gray-900">{sessionDetails.assessment.scale}</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Student Information</h3>
              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-gray-500">Student:</span>
                  <p className="text-gray-900">{sessionDetails.student.name}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Student ID:</span>
                  <p className="text-gray-900">{sessionDetails.student.id}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Session ID:</span>
                  <p className="text-gray-900 font-mono text-sm">{sessionId}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Summary Message */}
          <div className="bg-blue-50 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">What&apos;s Next?</h3>
            <p className="text-blue-800">
              Your responses have been recorded and will be reviewed by your teacher. The results
              will help create a personalized learning plan to support your progress.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => navigate('/dashboard')}
              className="flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Home className="w-5 h-5 mr-2" />
              Back to Dashboard
            </button>

            <button
              onClick={() => navigate(-2)} // Go back to the page before assessment execution
              className="flex items-center justify-center px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              View All Assessments
            </button>
          </div>

          {/* Auto-redirect notice */}
          {wasJustCompleted && (
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                You will be automatically redirected to the dashboard in a few seconds...
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssessmentResults;
