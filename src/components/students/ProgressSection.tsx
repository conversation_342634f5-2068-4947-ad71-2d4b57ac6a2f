import React from 'react';
import { Link } from 'react-router-dom';
import progress from '../../assets/u_progress.svg';
import arrow_right from '../../assets/arrow_right.svg';
export const ProgressSection: React.FC = () => {
  return (
    <Link to="#" className="block rounded-xl p-7 hover:shadow-md transition-shadow bg-[#F9F9F9]">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-8 h-8 rounded-full flex items-center justify-center">
            <span>
              <img src={progress} alt="" />
            </span>
          </div>
          <span className="text-base font-medium text-[#424242]">Student progress</span>
        </div>
        <span>
          <img src={arrow_right} alt="" />
        </span>
      </div>
    </Link>
  );
};
