import { api } from './api';

export type VerificationMethod = 'email' | 'credit_card' | 'pdf_upload';

interface ChooseMethodRequest {
  child_email: string;
  parent_email: string;
  verification_method: VerificationMethod;
}

interface ChooseMethodResponse {
  message: string;
  verification_url?: string;
  redirect_to?: string;
}

interface VerifyCreditCardRequest {
  child_email: string;
  payment_token: string;
}

interface UploadPdfRequest {
  child_email: string;
}

export const consentService = {
  async chooseMethod(data: ChooseMethodRequest): Promise<ChooseMethodResponse> {
    const response = await api.post('/consents/choose_method', data);
    return response.data as ChooseMethodResponse;
  },

  async verifyEmail(token: string): Promise<{ message: string }> {
    const response = await api.get(`/consents/verify_email/${token}`);
    return response.data as { message: string };
  },

  async verifyCreditCard(data: VerifyCreditCardRequest): Promise<{ message: string }> {
    const response = await api.post('/consents/verify_credit_card', data);
    return response.data as { message: string };
  },

  async uploadPdfConsent(data: UploadPdfRequest): Promise<{ message: string }> {
    const response = await api.post('/consents/upload_pdf_consent', data);
    return response.data as { message: string };
  },

  async getPdfTemplate(): Promise<{ message: string; sample_text: string }> {
    const response = await api.get('/consents/get_pdf_consent_template');
    return response.data as { message: string; sample_text: string };
  },
};
