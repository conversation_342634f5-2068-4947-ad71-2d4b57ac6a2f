import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTodaysSessions } from '../../services/sessionService';
import { useUserStore } from '../../store/userStore';
import quizImage from '../../assets/quiz-time.webp';

interface AssignedIntervention {
  id: string;
  intervention_id?: string;
  name: string;
  description: string;
  status: 'completed' | 'in-progress' | 'locked';
  progress: {
    current: number;
    total: number;
  };
  time_spent: string;
  display_title: string;
  display_status: string;
  image_type: string;
}

interface InterventionsGridProps {
  interventions: AssignedIntervention[];
}

// Mapowanie typów interwencji do obrazków
const INTERVENTION_IMAGES: Record<string, string> = {
  'blend-game--quiz': quizImage,
  'blend-game--word-building': quizImage,
  'blend-game': quizImage,
  'math-game': quizImage,
  'reading-game': quizImage,
  default: quizImage,
};

export const InterventionsGrid: React.FC<InterventionsGridProps> = ({ interventions }) => {
  const navigate = useNavigate();
  useUserStore();
  const { data: todaysSessions } = useTodaysSessions();

  // Znajdź sesję po game_id
  const findSessionByGameId = (gameId: string) => {
    return (
      todaysSessions?.interventions
        ?.flatMap(intervention => intervention.entries)
        ?.find(session => session.game_id === gameId) || null
    );
  };

  // Funkcja obsługująca kliknięcie na interwencję
  const handleInterventionClick = (intervention: AssignedIntervention) => {
    // Znajdź sesję po game_id
    const gameId = intervention.intervention_id || intervention.id;
    const session = findSessionByGameId(gameId);

    if (session?.game_id) {
      // Przekieruj do gry używając game_id
      navigate(`/dashboard/games/exercise/${session.game_id}`);
    } else {
      // Fallback - użyj intervention_id jako game_id
      navigate(`/dashboard/games/exercise/${gameId}`);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {interventions.map(intervention => {
        // Pobieramy obrazek na podstawie typu interwencji
        const image = INTERVENTION_IMAGES[intervention.image_type] || INTERVENTION_IMAGES.default;

        return (
          <div
            key={intervention.id}
            className="bg-white rounded-xl shadow-sm overflow-hidden border hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => handleInterventionClick(intervention)}
          >
            <div className="h-40 bg-gray-200 relative overflow-hidden">
              <img
                src={image}
                alt={intervention.display_title}
                className="w-full h-full object-cover"
                onError={e => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://via.placeholder.com/300x200?text=Quiz+Image';
                }}
              />
              <div className="absolute top-2 left-2 bg-white px-3 py-1 rounded-full text-sm font-medium">
                {intervention.display_status}
              </div>
            </div>
            <div className="p-4">
              <h4 className="text-lg font-semibold mb-1">{intervention.display_title}</h4>
              <p className="text-gray-600 text-sm mb-3">{intervention.description}</p>
              <div className="flex items-center text-gray-500 text-sm mb-4">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Exercise time: {intervention.time_spent} min.
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-sm text-gray-500">
                    {intervention.progress.current}/{intervention.progress.total}
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  {intervention.status === 'in-progress' ? '∞' : intervention.time_spent} min.
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};
