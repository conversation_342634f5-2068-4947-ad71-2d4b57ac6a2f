import React, { useState, useEffect } from 'react';
import { QuizQuestion as QuizQuestionType } from '@games/common/types';
import VisualObjects from './VisualObjects';

interface QuizQuestionProps {
  question: QuizQuestionType;
  onAnswer: (answer: number | null) => void;
  showFeedback: boolean;
  userAnswer: number | null;
}

const QuizQuestion: React.FC<QuizQuestionProps> = ({
  question,
  onAnswer,
  showFeedback,
  userAnswer,
}) => {
  const [remainingTime, setRemainingTime] = useState(10);
  const [animateIn, setAnimateIn] = useState(false);
  const [options, setOptions] = useState<number[]>([]);

  const isCorrect = userAnswer === question.equation.result;

  useEffect(() => {
    setAnimateIn(true);
    // Generate answer options: correct + 2-3 random wrong
    const correct = question.equation.result;
    const wrongOptions = new Set<number>();
    while (wrongOptions.size < 3) {
      const delta = Math.floor(Math.random() * 10) + 1;
      const sign = Math.random() > 0.5 ? 1 : -1;
      let wrong = correct + sign * delta;
      if (wrong < 0) wrong = correct + delta; // avoid negative
      if (wrong !== correct) wrongOptions.add(wrong);
    }
    const opts = [...wrongOptions].slice(0, 3);
    const all = [correct, ...opts];
    setOptions(all.sort(() => Math.random() - 0.5));
    setRemainingTime(10);
    return () => setAnimateIn(false);
  }, [question.id]);

  useEffect(() => {
    if (showFeedback || userAnswer !== null) return;
    if (remainingTime === 0) {
      onAnswer(null);
      return;
    }
    const timer = setTimeout(() => setRemainingTime(t => t - 1), 1000);
    return () => clearTimeout(timer);
  }, [remainingTime, showFeedback, userAnswer, onAnswer]);

  const handleOptionClick = (value: number) => {
    if (userAnswer !== null || showFeedback) return;
    onAnswer(value);
  };

  return (
    <div
      className={`w-full p-6 rounded-xl bg-white shadow-lg transition-all duration-500 ease-in-out ${
        animateIn ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
      }`}
    >
      <div className="mb-8">
        <VisualObjects
          objectType={question.visualObjects.objectType}
          count={question.visualObjects.total}
          className="mb-4 py-3"
        />

        <div className="flex items-center justify-center text-3xl font-bold gap-3 py-4">
          <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800">
            {question.equation.num1}
          </div>
          <span>+</span>
          <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800">
            {question.equation.num2}
          </div>
          <span>=</span>
          <div
            className={`flex items-center justify-center w-14 h-14 rounded-full border-2 ${
              showFeedback
                ? isCorrect
                  ? 'border-green-500 bg-green-100'
                  : 'border-red-500 bg-red-100'
                : 'border-gray-800'
            } transition-colors duration-300`}
          >
            {showFeedback ? question.equation.result : '?'}
          </div>
        </div>
      </div>

      <div className="flex flex-col items-center">
        {/* Pasek postępu czasu */}
        <div className="w-full max-w-xs mb-4">
          <div className="h-3 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-3 bg-blue-400 transition-all duration-500"
              style={{ width: `${(remainingTime / 10) * 100}%` }}
            ></div>
          </div>
          <div className="text-center text-xs text-gray-600 mt-1">{remainingTime}s</div>
        </div>

        {/* Przyciski odpowiedzi */}
        <div className="grid grid-cols-2 gap-4 w-full max-w-xs mb-2">
          {options.map((opt, idx) => (
            <button
              key={idx}
              type="button"
              className={`py-3 px-1 rounded-lg shadow text-lg font-bold border-2 transition-all duration-200
                ${userAnswer === null && !showFeedback ? 'bg-white hover:bg-blue-100 border-blue-400' : ''}
                ${userAnswer === opt && showFeedback && opt === question.equation.result ? 'bg-green-200 border-green-500' : ''}
                ${userAnswer === opt && showFeedback && opt !== question.equation.result ? 'bg-red-200 border-red-500' : ''}
                ${(userAnswer !== null || showFeedback) && userAnswer !== opt ? 'opacity-60' : ''}
              `}
              disabled={userAnswer !== null || showFeedback}
              onClick={() => handleOptionClick(opt)}
            >
              {String.fromCharCode(65 + idx)}) {opt}
            </button>
          ))}
        </div>
      </div>
      {/* FEEDBACK */}
      {showFeedback && (
        <div
          className={`text-lg font-medium ${isCorrect ? 'text-green-600' : 'text-red-600'} mb-2 animate-fadeIn`}
        >
          {isCorrect
            ? 'Correct! Great job! 🎉'
            : `Not quite. The answer is ${question.equation.result}`}
        </div>
      )}
    </div>
  );
};

export default QuizQuestion;
