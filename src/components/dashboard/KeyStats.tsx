import React from 'react';

export const KeyStats: React.FC = () => {
  const stats = [
    { label: 'Listening', value: 76 },
    { label: 'Reading', value: 67 },
    { label: 'Fluency', value: 70 },
    { label: 'Focusing', value: 24 },
  ];

  return (
    <div className="grid grid-cols-2 gap-6">
      {stats.map(stat => (
        <div key={stat.label} className="flex flex-col items-center">
          <svg width="70" height="70" viewBox="0 0 36 36">
            <path
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
              fill="none"
              stroke="#D1D5DB"
              strokeWidth="3"
            />
            <path
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831"
              fill="none"
              stroke="#40788C"
              strokeWidth="3"
              strokeDasharray={`${stat.value}, 100`}
              strokeLinecap="round"
            />
            <text x="18" y="21" textAnchor="middle" fontSize="9" fill="#17495A" fontWeight="bold">
              {stat.value}%
            </text>
          </svg>
          <span className="mt-2 text-sm font-medium text-gray-700">{stat.label}</span>
        </div>
      ))}
    </div>
  );
};
