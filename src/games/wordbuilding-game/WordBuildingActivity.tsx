import React, { useState, useEffect } from 'react';
import Game from '../engine/Game';
import { WordBuildingTask, GameTask } from '../engine/types';
import wordBuildingTasks from './data';
import { GameStats } from '../common/types';

interface WordBuildingActivityProps {
  gameId?: string;
  studentId?: string;
  sessionId?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  onComplete?: (stats: GameStats) => void;
  onFinish?: (stats: GameStats) => void;
  className?: string;
  style?: React.CSSProperties;
}

const WordBuildingActivity: React.FC<WordBuildingActivityProps> = ({
  gameId = 'support-strategy--word-building-puzzles',
  studentId,
  sessionId,
  difficulty,
  onComplete,
  onFinish,
  className,
  style,
}) => {
  const [tasks, setTasks] = useState<WordBuildingTask[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // Filtrowanie zadań według poziomu trudno<PERSON>, jeśli określono
    let filteredTasks = [...wordBuildingTasks];

    if (difficulty) {
      filteredTasks = filteredTasks.filter(task => task.difficulty === difficulty);
    }

    // Jeśli nie ma zadań po filtrowaniu, użyj wszystkich zadań
    if (filteredTasks.length === 0) {
      filteredTasks = [...wordBuildingTasks];
    }

    // Losowe wybranie maksymalnie 5 zadań
    const shuffledTasks = shuffleArray(filteredTasks);
    const selectedTasks = shuffledTasks.slice(0, 5);

    setTasks(selectedTasks);
    setLoading(false);
  }, [difficulty]);

  // Funkcja do tasowania tablicy
  const shuffleArray = <T,>(array: T[]): T[] => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className={`w-full ${className || ''}`} style={style}>
      <Game
        tasks={tasks as unknown as GameTask[]}
        gameId={gameId}
        studentId={studentId}
        sessionId={sessionId}
        onComplete={onComplete}
        onFinish={onFinish}
      />
    </div>
  );
};

export default WordBuildingActivity;
