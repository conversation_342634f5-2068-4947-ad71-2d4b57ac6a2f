.bg-green-500 {
  background-color: #10b981 !important;
}

.bg-red-500 {
  background-color: #ef4444 !important;
}

.text-white {
  color: white !important;
}

.text-gray-700 {
  color: #374151 !important;
}

.text-gray-600 {
  color: #4b5563 !important;
}

.flex {
  display: flex !important;
}

.items-center {
  align-items: center !important;
}

.justify-center {
  justify-content: center !important;
}

.absolute {
  position: absolute !important;
}

.inset-0 {
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}

.z-10 {
  z-index: 10 !important;
}

.w-12 {
  width: 3rem !important;
}

.h-12 {
  height: 3rem !important;
}

.w-20 {
  width: 5rem !important;
}

.h-20 {
  height: 5rem !important;
}

.rounded-full {
  border-radius: 9999px !important;
}

.animate-bounce {
  animation: bounce 1s infinite !important;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.shadow-lg {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.pointer-events-none {
  pointer-events: none !important;
}
