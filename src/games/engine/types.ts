import { GameStats, Task as BlendTaskOriginal, QuizQuestion } from '../common/types';

export interface BaseTask {
  type: string;
  id?: number | string;
}

export interface BlendTask extends BaseTask {
  type: 'blend';
  image: string;
  word: string;
  options: string[];
  answer: string;
}

export interface MathTask extends BaseTask {
  type: 'math';
  id: number;
  visualObjects: {
    total: number;
    subtract: number;
    objectType: 'mushroom' | 'donut' | 'flower' | 'pencil' | 'backpack' | 'calculator';
  };
  equation: {
    num1: number;
    num2: number;
    result: number;
  };
}

export interface MathTask1 extends BaseTask {
  type: 'math-game1';
  id: number;
  visualObjects: {
    total: number;
    subtract: number;
    objectType: 'mushroom' | 'donut' | 'flower' | 'pencil' | 'backpack' | 'calculator';
  };
  equation: {
    num1: number;
    num2: number;
    result: number;
  };
}

export interface MathTask2 extends BaseTask {
  type: 'math-game2';
  id: number;
  visualObjects: {
    total: number;
    subtract: number;
    objectType: 'mushroom' | 'donut' | 'flower' | 'pencil' | 'backpack' | 'calculator';
  };
  equation: {
    num1: number;
    num2: number;
    result: number;
  };
}

export interface MemoryTask extends BaseTask {
  type: 'memory';
  id?: number;
  pairs?: string[];
  cards?: Array<{ id: number; value: string; matched: boolean }>;
  difficulty?: 'easy' | 'medium' | 'hard';
}

export interface WordBuildingTask extends BaseTask {
  type: 'wordbuilding';
  id?: number | string;
  targetWord: string;
  image: string;
  availableLetters: string[];
  difficulty?: 'easy' | 'medium' | 'hard';
  hint?: string;
}

export interface MemoryTask1 extends BaseTask {
  type: 'memory-game1';
  targetValue?: string;
  operation?: string;
  pairs?: string[];
  cards?: {
    id: number;
    value: string;
    matched: boolean;
    type?: 'operation' | 'result';
  }[];
  matchPairs?: {
    num1: string;
    num2: string;
  }[];
  difficulty?: 'easy' | 'medium' | 'hard';
}

export interface MemoryTask2 extends BaseTask {
  type: 'memory-game2';
  result?: string;
  secondNumber?: string;
  cards?: {
    id: number;
    value: string;
    matched: boolean;
    type: 'number' | 'operator' | 'result';
  }[];
  matchPairs?: {
    number: string;
    operator: string;
    secondNumber: string;
    result: string;
  }[];
  difficulty?: 'easy' | 'medium' | 'hard';
}

export interface SemanticMappingTask extends BaseTask {
  type: 'semantic-mapping';
  id: number;
  targetWord: string;
  definition?: string;
  relatedWords: {
    word: string;
    type: 'related' | 'category' | 'synonym' | 'antonym';
    correct: boolean;
  }[];
  images: {
    url: string;
    alt: string;
    correct: boolean;
  }[];
}

export interface MastermindTask extends BaseTask {
  type: 'mastermind-game';
  id?: number;
  difficulty?: number; // Liczba cyfr (3, 4 lub 5)
  maxAttempts?: number; // Maksymalna liczba prób (opcjonalne)
}

export type GameTask =
  | BlendTask
  | MathTask
  | MathTask1
  | MathTask2
  | MemoryTask
  | MemoryTask1
  | MemoryTask2
  | MastermindTask
  | SemanticMappingTask;

export const convertBlendTask = (originalTask: BlendTaskOriginal): BlendTask => ({
  type: 'blend',
  ...originalTask,
});

export const convertMathTask = (originalTask: QuizQuestion): MathTask => ({
  type: 'math',
  id: originalTask.id,
  visualObjects: originalTask.visualObjects,
  equation: originalTask.equation,
});

export const convertMathTask1 = (originalTask: QuizQuestion): MathTask1 => ({
  type: 'math-game1',
  id: originalTask.id,
  visualObjects: originalTask.visualObjects,
  equation: originalTask.equation,
});

export interface GameRendererProps {
  task: GameTask;
  onAnswer: (isCorrect: boolean, userAnswer?: string | number | null) => void;
  showFeedback?: boolean;
  userAnswer?: string | number | null;
  isCorrect?: boolean;
  currentTaskIndex?: number;
  totalTasks?: number;
  onFeedbackComplete?: () => void;
  stats?: GameStats;
  sessionId?: string; // Dodano sessionId
  onRestart?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

export interface GameProps {
  tasks: GameTask[];
  onComplete?: (stats: GameStats) => void;
  studentId?: string;
  gameId: string;
  sessionId?: string; // Dodano sessionId
  sessionDetails?: string; // Dodano sessionDetails do wznowienia stanu
  onFinish?: (result: GameStats) => void;
  className?: string;
  style?: React.CSSProperties;
}

export interface GameState {
  currentTaskIndex: number;
  completedTasks: boolean[];
  isGameComplete: boolean;
  stats: GameStats;
  feedback: {
    show: boolean;
    isCorrect: boolean;
    userAnswer: string | number | null;
  };
}

export interface GameRendererInfo {
  type: string;
  name: string;
  description?: string;
}
