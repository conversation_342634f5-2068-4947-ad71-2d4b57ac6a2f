import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Game } from '../../services/gameService';
import GameStatsSummary from './GameStatsSummary';
import GameTags from './GameTags';
import progressIcon from '../../assets/progressGames.svg';
import { useGameStatsStore } from '../../store/gameStatsStore';
import imageReading from '../../assets/Image-letters.svg';
import imageMath from '../../assets/Image-math.png';

interface GameCardProps {
  game: Game;
  studentId: string;
  onClick?: () => void;
}

export const GameCard: React.FC<GameCardProps> = ({ game, studentId, onClick }) => {
  const navigate = useNavigate();

  const handleGameSelect = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(`/dashboard/interventions/${game.id}`);
    }
  };

  const latestStats = useGameStatsStore(state => state.getLatestGameStats(game.id, studentId));

  let gameStatus = null;

  if (latestStats) {
    if (
      latestStats.completionPercentage === 100 ||
      (game.id === 'math-game' && latestStats.completionPercentage >= 83)
    ) {
      gameStatus = 'completed';
    } else if (latestStats.completionPercentage < 100) {
      gameStatus = 'in-progress';
    }
  }

  const readingGradients = [
    'linear-gradient(67.84deg, #ED1C24 -1.82%, #FCEC21 106.59%)',
    'linear-gradient(70.91deg, #FFD4A2 0%, #FE0159 100%)',
    'linear-gradient(67.84deg, #2E3393 -1.82%, #1CFAFC 106.59%)',
    'linear-gradient(70.91deg, #00467F 0%, #A5CC82 100%)',
    'linear-gradient(70.91deg, #764BA2 0%, #667EEA 100%)',
    'linear-gradient(71.32deg, #FF00CC -1.7%, #333399 100%)',
    'linear-gradient(67.84deg, #682D8C -1.82%, #EB1E79 106.59%)',
  ];
  const mathGradients = [
    'linear-gradient(135deg, #0C07EF 0%, #00FF75 100%)',
    'linear-gradient(135deg, #7000FF 0%, #FF63F9 100%)',
    'linear-gradient(137.04deg, #F96D7F 3.32%, #626EFF 100%)',
    'linear-gradient(135deg, #6BFFED 0%, #1100CE 100%)',
    'linear-gradient(135deg, #00FF94 0%, #0057FF 100%)',
    'linear-gradient(135deg, #FF7A00 0%, #FF0000 100%)',
    'linear-gradient(135deg, #FF00B8 0%, #B70000 100%)',
  ];

  const mathCategories = ['math'];
  const isMath = game.categories?.some(c => mathCategories.includes(c.toLowerCase()));
  const gradients = isMath ? mathGradients : readingGradients;
  const bgIndex =
    Math.abs(game.id.split('').reduce((acc, c) => acc + c.charCodeAt(0), 0)) % gradients.length;
  const backgroundStyle = {
    background: gradients[bgIndex],
    height: '192px',
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  };
  const displayImage = isMath ? imageMath : imageReading;

  return (
    <div
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer w-full max-w-xs mx-auto flex flex-col"
      onClick={handleGameSelect}
    >
      {gameStatus === 'completed' && (
        <div className="bg-green-500 text-white py-2 px-4 rounded-t-xl flex items-center">
          <svg
            className="w-5 h-5 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9,16.17 L4.83,12 L3.41,13.41 L9,19 L21,7 L19.59,5.59 L9,16.17 Z"
              fill="white"
            />
          </svg>
          <span className="font-medium">Completed</span>
        </div>
      )}
      {gameStatus === 'in-progress' && (
        <div className="bg-blue-500 text-white py-2 px-4 rounded-t-xl flex items-center">
          <img src={progressIcon} alt="In Progress" className="w-5 h-5 mr-2" />
          <span className="font-medium">In Progress</span>
        </div>
      )}

      <div className="w-full relative" style={backgroundStyle}>
        <img src={displayImage} alt={game.name} className="object-contain mx-auto h-48" />
      </div>

      <div className="p-4 flex-1 flex flex-col">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{game.name}</h3>
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{game.description}</p>

        {/* Tagi gry */}
        <GameTags game={game} className="mb-4" maxTags={3} />

        <GameStatsSummary gameId={game.id} studentId={studentId} />
      </div>

      <div className="p-4 border-t border-gray-100 bg-gray-50 rounded-b-xl flex justify-center">
        <button
          onClick={e => {
            e.stopPropagation();
            handleGameSelect();
          }}
          className="w-[214px] h-[56px] rounded-[16px] p-[16px] bg-blue-600 text-white hover:bg-blue-700 transition-colors font-medium cursor-pointer flex items-center justify-center gap-[12px]"
        >
          Settings
        </button>
      </div>
    </div>
  );
};

export default GameCard;
