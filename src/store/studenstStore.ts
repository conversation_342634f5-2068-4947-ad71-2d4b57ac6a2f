import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Student } from '../types/student';
import { studentService, MyStudentsStatsResponse } from '../services/studentService';
import logger from '../utils/logger';
import { AxiosError } from 'axios';

export interface StudentsStats {
  total_students: number;
  average_age: number;
  total_parents: number;
  total_teachers: number;
  total_chat_sessions: number;
  total_assigned_interventions: number;
  total_user_questions: number;
  total_math_interventions: number;
  total_reading_interventions: number;
  total_interventions: number;
  most_popular_intervention: {
    game_id: string;
    title: string;
    count: number;
  };
  best_student: {
    student_id: number;
    first_name: string;
    last_name: string;
    average_score: number;
  };
}

interface StudentsState {
  students: Student[];
  stats: StudentsStats | null;
  myStudentsStats: MyStudentsStatsResponse | null;
  loading: boolean;
  error: string | null;
  fetchStudents: () => Promise<void>;
  fetchStudentsStats: () => Promise<void>;
  fetchMyStudentsStats: () => Promise<void>;
  getStudentById: (id: number) => Student | undefined;
}

export const useStudentsStore = create<StudentsState>()(
  devtools(
    (set, get) => ({
      students: [],
      stats: null,
      myStudentsStats: null,
      loading: false,
      error: null,

      fetchStudents: async () => {
        try {
          set({ loading: true, error: null });
          const students = await studentService.getStudents();
          set({
            students: Array.isArray(students) ? students : [],
            loading: false,
            error: null,
          });
        } catch (error) {
          let errorMessage = 'Failed to load student list';

          if (error instanceof AxiosError) {
            errorMessage = error.response?.data?.message || error.message;
          } else if (error instanceof Error) {
            errorMessage = error.message;
          }

          logger.error('Error while fetching students:', error);
          set({
            students: [],
            error: errorMessage,
            loading: false,
          });
        }
      },

      getStudentById: (id: number) => {
        return get().students.find(student => student.id === id);
      },

      fetchStudentsStats: async () => {
        try {
          set({ loading: true, error: null });
          const stats = await studentService.getStudentsStatistics();
          set({
            stats,
            loading: false,
            error: null,
          });
        } catch (error) {
          let errorMessage = 'Failed to load student statistics';

          if (error instanceof AxiosError) {
            errorMessage = error.response?.data?.message || error.message;
          } else if (error instanceof Error) {
            errorMessage = error.message;
          }

          logger.error('Error while fetching student statistics:', error);
          set({
            stats: null,
            error: errorMessage,
            loading: false,
          });
        }
      },

      fetchMyStudentsStats: async () => {
        try {
          set({ loading: true, error: null });
          const myStudentsStats = await studentService.getMyStudentsStats();
          set({
            myStudentsStats,
            loading: false,
            error: null,
          });
        } catch (error) {
          let errorMessage = 'Failed to load my students statistics';

          if (error instanceof AxiosError) {
            errorMessage = error.response?.data?.message || error.message;
          } else if (error instanceof Error) {
            errorMessage = error.message;
          }

          logger.error('Error fetching my students stats:', error);
          set({
            myStudentsStats: null,
            error: errorMessage,
            loading: false,
          });
        }
      },
    }),
    {
      name: 'students-storage',
    }
  )
);

// Eksportujemy domyślnie hook useStudentsStore
export default useStudentsStore;
