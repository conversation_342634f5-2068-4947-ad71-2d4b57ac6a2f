import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import cookies from '../utils/cookies';
import logger from '../utils/logger';
import * as mockData from '../mocks/mockData';
import { LoginData } from './authService';
import { StudentsListResponse, Student, StudentListItem } from './studentService';
import gameImage from '../assets/game1.png';
import mathGameImage from '../assets/game2.png';
import memoryGameImage from '../assets/game3.png';

/* eslint-disable no-console */ // Allow console logs in debug mode

// Interfejs dla danych studenta w kontekście mockowania
interface MockStudent extends StudentListItem {
  id: number;
  user_id: number;
  created_at: string;
}

// Interfejs dla sesji gry
interface MockGameSession {
  session_id: string;
  game_id: string;
  total_questions: number;
  progress: { step: number; correct: boolean }[];
  status: 'in_progress' | 'completed' | 'not_started';
  duration_seconds: number | null;
  still_in_progress: boolean;
}

// Interfejs dla dnia kalendarza
interface MockCalendarDay {
  date: string;
  weekday: string;
  is_today: boolean;
  in_current_month: boolean;
  sessions: MockGameSession[];
}

// Enable to show detailed logs from mockAPI
const DEBUG_MOCK_API = true;

// Prosty logger dla żądań API mock
const logMockRequest = (method: string, url: string, data?: unknown, response?: unknown): void => {
  if (DEBUG_MOCK_API) {
    console.group(`🔄 Mock API ${method.toUpperCase()} ${url}`);
    if (data) {
      console.log('📤 Request data:', data);
    }
    if (response) {
      console.log('📥 Response:', response);
    }
    console.groupEnd();
  }
};

// Create a mock axios instance
const mockAxios = {
  get: async (url: string, _config?: InternalAxiosRequestConfig): Promise<AxiosResponse> => {
    logMockRequest('get', url, _config);
    const response = await handleMockRequest('get', url, _config);
    logMockRequest('get', url, _config, response);
    return {
      data: response,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as InternalAxiosRequestConfig,
    };
  },
  post: async (url: string, data?: unknown): Promise<AxiosResponse> => {
    logMockRequest('post', url, data);
    const mockResponse = await handleMockRequest('post', url, data);
    logMockRequest('post', url, data, mockResponse);
    return {
      data: mockResponse,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as InternalAxiosRequestConfig,
    };
  },
  put: async (url: string, data?: unknown): Promise<AxiosResponse> => {
    logMockRequest('put', url, data);
    const mockResponse = await handleMockRequest('put', url, data);
    logMockRequest('put', url, data, mockResponse);
    return {
      data: mockResponse,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as InternalAxiosRequestConfig,
    };
  },
  delete: async (url: string, _config?: InternalAxiosRequestConfig): Promise<AxiosResponse> => {
    logMockRequest('delete', url, _config);
    const mockResponse = await handleMockRequest('delete', url);
    logMockRequest('delete', url, _config, mockResponse);
    return {
      data: mockResponse,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as InternalAxiosRequestConfig,
    };
  },
  patch: async (url: string, data?: unknown): Promise<AxiosResponse> => {
    logMockRequest('patch', url, data);
    const mockResponse = await handleMockRequest('patch', url, data);
    logMockRequest('patch', url, data, mockResponse);
    return {
      data: mockResponse,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as InternalAxiosRequestConfig,
    };
  },
};

// Create a real axios instance
const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'https://api.dev.yubu.ai',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

axiosInstance.interceptors.request.use(
  config => {
    const token = cookies.get('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    logger.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Handle mock responses based on URL and method
const handleMockRequest = (method: string, url: string, data?: unknown): unknown => {
  logger.info(`Mock ${method.toUpperCase()} request to: ${url}`);

  // Wait a bit to simulate network latency
  return new Promise(resolve => {
    setTimeout(() => {
      try {
        const response = generateMockResponse(method, url, data);
        if (DEBUG_MOCK_API) {
          console.log(`🛠️ Mock response for ${method.toUpperCase()} ${url}:`, response);
        }
        resolve(response);
      } catch (error) {
        console.error(`❌ Error in mock handler for ${method.toUpperCase()} ${url}:`, error);
        resolve({ error: 'Mock error', message: 'Failed to generate mock response' });
      }
    }, 300);
  });
};

// Get a deep copy of mock students list for manipulation
let mockStudentsList: MockStudent[] = JSON.parse(JSON.stringify(mockData.mockStudents));

// Generate mock response based on URL and method
const generateMockResponse = (method: string, url: string, data?: unknown): unknown => {
  // Normalize URL - add slash at the beginning if missing
  const normalizedUrl = url.startsWith('/') ? url : '/' + url;
  console.log('🔍 Processing request:', { method, originalUrl: url, normalizedUrl });

  // Login endpoint
  if (url === '/users/login' && method === 'post') {
    console.log('🟢 Login attempt:', data);
    const loginData = data as LoginData;

    // Check if login credentials are correct
    if (loginData.email === '<EMAIL>' && loginData.password === 'Test1234!') {
      const loginResponse = {
        access_token: 'mock_access_token_123',
        refresh_token: 'mock_refresh_token_456',
        user_data: mockData.mockTeacher,
      };
      console.log('🟢 Login successful:', loginResponse);
      return loginResponse;
    } else {
      console.error('❌ Invalid login credentials:', loginData);
      throw new Error('Invalid login credentials');
    }
  }

  // Register endpoint
  if (method === 'post' && url === '/users/register') {
    return {
      message: 'User successfully registered',
      user_id: 999,
    };
  }

  // Current user info endpoint
  if (method === 'get' && (normalizedUrl === '/users/me' || url === 'users/me')) {
    return mockData.mockTeacher;
  }

  // Students list endpoint
  if (
    method === 'get' &&
    (normalizedUrl === '/students' ||
      normalizedUrl.startsWith('/students?') ||
      normalizedUrl === '/students/list' ||
      url === 'students' ||
      url.startsWith('students?') ||
      url === 'students/list')
  ) {
    console.log('🟢 Returning students list:', { count: mockStudentsList.length });
    // Return data directly without additional wrapping in data
    return {
      students: mockStudentsList,
      total: mockStudentsList.length,
    };
  }

  // Single student endpoint
  if (method === 'get' && url.match(/\/students\/\d+$/)) {
    const studentId = parseInt(url.split('/').pop() || '0');
    const student = mockStudentsList.find(s => s.id === studentId);

    if (student) {
      return student;
    } else {
      throw new Error('Student not found');
    }
  }

  // Student register endpoint
  if (method === 'post' && url === '/students/register') {
    // Narrow `data` from unknown to expected shape
    const { first_name = 'unknown', last_name = 'user' } =
      (data as Partial<{ first_name: string; last_name: string }>) || {};
    return {
      message: 'Student registered successfully',
      student_user_id: Math.floor(Math.random() * 1000) + 1000,
      temporary_email: `${first_name}.${last_name}@student.local`.toLowerCase(),
      temporary_password: 'Abc12345',
    };
  }

  // Add student endpoint
  if (
    (method === 'post' && normalizedUrl === '/students/add') ||
    (method === 'post' && url === 'students/add')
  ) {
    console.log('🟢 Adding new student:', data);
    const studentData = data as Omit<(typeof mockStudentsList)[0], 'id' | 'created_at' | 'user_id'>;

    // Create a new student with auto-generated id
    const newId = Math.max(...mockStudentsList.map((s: MockStudent) => s.id), 0) + 1;
    const userId = Math.max(...mockStudentsList.map((s: MockStudent) => s.user_id || 0), 200) + 1;

    const newStudent = {
      ...studentData,
      id: newId,
      user_id: userId,
      created_at: new Date().toISOString(),
    };

    // Add to our local copy of students list
    mockStudentsList.push(newStudent);

    return {
      success: true,
      message: 'Student added successfully',
      student: newStudent,
    };
  }

  // Edit student endpoint
  if (
    (method === 'patch' || method === 'put') &&
    (normalizedUrl.match(/\/students\/edit\/\d+/) || url.match(/students\/edit\/\d+/))
  ) {
    const studentId = parseInt(
      normalizedUrl.match(/\/students\/edit\/(\d+)/)
        ? normalizedUrl.match(/\/students\/edit\/(\d+)/)?.[1] || '0'
        : url.match(/students\/edit\/(\d+)/)?.[1] || '0'
    );

    console.log(`🟢 Editing student with ID: ${studentId}`, data);

    const studentIndex = mockStudentsList.findIndex(s => s.id === studentId);
    if (studentIndex === -1) {
      console.error('❌ Student not found:', studentId);
      throw new Error(`Student with ID ${studentId} not found`);
    }

    // Update the student data
    const originalStudent = mockStudentsList[studentIndex];
    mockStudentsList[studentIndex] = {
      ...originalStudent,
      ...(data as object),
      id: studentId, // Preserve original ID
      user_id: originalStudent.user_id, // Preserve user ID
    };

    return {
      success: true,
      message: 'Student updated successfully',
      student: mockStudentsList[studentIndex],
    };
  }

  // Delete student endpoint
  if (
    method === 'delete' &&
    (normalizedUrl.match(/\/students\/delete\/\d+/) || url.match(/students\/delete\/\d+/))
  ) {
    const studentId = parseInt(
      normalizedUrl.match(/\/students\/delete\/(\d+)/)
        ? normalizedUrl.match(/\/students\/delete\/(\d+)/)?.[1] || '0'
        : url.match(/students\/delete\/(\d+)/)?.[1] || '0'
    );

    console.log(`🟢 Deleting student with ID: ${studentId}`);

    const studentIndex = mockStudentsList.findIndex(s => s.id === studentId);
    if (studentIndex === -1) {
      console.error('❌ Student not found:', studentId);
      throw new Error(`Student with ID ${studentId} not found`);
    }

    // Remove the student
    const deletedStudent = mockStudentsList[studentIndex];
    mockStudentsList = mockStudentsList.filter((s: { id: number }) => s.id !== studentId);

    return {
      success: true,
      message: 'Student deleted successfully',
      student: deletedStudent,
    };
  }

  // Students stats endpoint
  if (method === 'get' && (normalizedUrl === '/students/stats' || url === 'students/stats')) {
    return mockData.mockStudentsStats;
  }

  // My students stats endpoint
  if (
    method === 'get' &&
    (normalizedUrl === '/students/stats_my_students' || url === 'students/stats_my_students')
  ) {
    return mockData.mockMyStudentsStats;
  }

  // Get interventions
  if (
    method === 'get' &&
    (normalizedUrl === '/games/interventions' ||
      normalizedUrl === '/games/all_interventions' ||
      url === 'games/interventions' ||
      url === 'games/all_interventions')
  ) {
    console.log('🟢 Returning interventions list for:', url);

    // UWAGA: gameService.getGames() oczekuje TABLICY interwencji z zagnieżdżonymi aktywnościami
    // a nie obiektu { games: [] }, dlatego zmieniamy format zwracanych danych

    // Przygotowujemy mockowe dane w formacie zgodnym z API
    const mockInterventions = [
      {
        id: 'blend-intervention',
        title: 'Blend Intervention',
        description: 'Phonics intervention set',
        activities: [
          {
            game_id: 'blend-game--quiz',
            title: 'Blend Activity',
            description: 'Practice blending sounds with this interactive game',
            images: [gameImage],
            categories: ['Letter Sounds', 'Phonics', 'Long Vowels', 'Multi Syllabic Words'],
            difficulty_level: 'medium',
            target_age_group: '5-8',
          },
        ],
      },
      {
        id: 'math-intervention',
        title: 'Math Intervention',
        description: 'Math skills development',
        activities: [
          {
            game_id: 'math-game',
            title: 'Math Quiz',
            description: 'Practice math skills with this interactive quiz',
            images: [mathGameImage],
            categories: ['Math', 'Arithmetic', 'Phonics Awareness', 'Prefixes'],
            difficulty_level: 'medium',
            target_age_group: '6-10',
          },
        ],
      },
      {
        id: 'memory-intervention',
        title: 'Memory Intervention',
        description: 'Memory development set',
        activities: [
          {
            game_id: 'memory-game',
            title: 'Memory Game',
            description: 'Test your memory by matching pairs in this fun card game',
            images: [memoryGameImage],
            categories: ['Memory', 'Cognition', 'Sight Words', 'Compound Words'],
            difficulty_level: 'easy',
            target_age_group: '4-7',
          },
        ],
      },
      {
        id: 'numbers-intervention',
        title: 'Numbers Intervention',
        description: 'Number skills development',
        activities: [
          {
            game_id: 'mastermind-game',
            title: 'Number Code Breaker',
            description: 'Guess the secret number code based on hints',
            images: [mathGameImage],
            categories: ['Math', 'Logic', 'Problem Solving'],
            difficulty_level: 'medium',
            target_age_group: '7-12',
          },
        ],
      },
    ];

    console.log('🟢 Returning interventions array with nested activities:', mockInterventions);

    // Zwracamy tablicę interwencji, a nie obiekt { games: [...] }
    return mockInterventions;
  }

  // Get assigned interventions
  if (method === 'get' && url.match(/\/games\/assigned_interventions\/\d+$/)) {
    const studentId = parseInt(url.split('/').pop() || '0');
    return {
      assigned_interventions: mockData.mockAssignedInterventions.filter(
        ai => ai.student_id === studentId
      ),
    };
  }

  // Assign intervention endpoint
  if (method === 'post' && url === '/games/assign_intervention') {
    return { message: 'Intervention assigned successfully' };
  }

  // Get dashboard stats
  if (method === 'get' && url === '/dashboard/stats') {
    console.log('🟢 Returning dashboard stats:', mockData.mockTeacherDashboardData);
    // Zwracamy dane bezpośrednio, bez dodatkowego zawijania w data
    return mockData.mockTeacherDashboardData;
  }

  // Student calendar view endpoint
  if (
    method === 'post' &&
    (normalizedUrl === '/students/calendar/view' || url === 'students/calendar/view')
  ) {
    const requestData = data as { student_id: number };
    console.log(`🟢 Getting calendar for student ID: ${requestData.student_id}`);

    // Generate mock calendar data
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    // Simple mock calendar with current month
    return {
      months: [
        {
          name: new Intl.DateTimeFormat('en-US', { month: 'long' }).format(today),
          year: currentYear,
          weeks: generateMockCalendarWeeks(currentYear, currentMonth, requestData.student_id),
        },
      ],
    };
  }

  // Chat endpoints
  if (url.includes('/chat/')) {
    if (url.includes('/start-session')) {
      return { session_id: 'mock_session_1' };
    }

    if (url.includes('/send-message')) {
      return {
        message: 'This is a mock response from the AI assistant.',
        session_id: 'mock_session_1',
      };
    }

    if (url.includes('/my-sessions')) {
      return {
        sessions: [{ id: 'mock_session_1', created_at: new Date().toISOString() }],
      };
    }

    if (url.includes('/history')) {
      return {
        messages: mockData.mockChatMessages,
      };
    }

    if (url.includes('/last_active_students') || url === '/chat/last_active_students') {
      return {
        students: mockData.mockLastActiveStudents,
      };
    }

    if (url === '/chat/educator_survey/options') {
      return mockData.mockEducatorSurveyOptions;
    }

    if (url === '/chat/educator_survey') {
      return { message: 'Survey submitted successfully' };
    }
  }

  // Documents endpoints
  if (url.includes('/documents/')) {
    if (url === '/documents/documents_add' && method === 'post') {
      // Symulujemy dodanie dokumentu
      return {
        document_id: Math.floor(Math.random() * 1000) + 1,
        message: 'Document uploaded and processed successfully',
      };
    }

    if (url.match(/\/documents\/list\/\d+$/) && method === 'get') {
      const studentId = parseInt(url.split('/').pop() || '0');
      return {
        documents: mockData.mockDocuments.filter(doc => doc.student_id === studentId),
      };
    }
  }

  // Games endpoints
  if (url.includes('/games/')) {
    if (url === '/games/list') {
      return { games: mockData.mockGamesData };
    }
  }

  // Default response for unsupported endpoints
  return { message: 'Mock data not implemented for this endpoint' };
};

// Helper function to generate mock calendar data with sessions
function generateMockCalendarWeeks(
  year: number,
  month: number,
  studentId: number
): MockCalendarDay[][] {
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);

  const totalDays = lastDay.getDate();
  const weeks: MockCalendarDay[][] = [];

  let currentWeek: MockCalendarDay[] = [];
  const today = new Date();

  // Add empty days for first week until the first day of the month
  const firstDayOfWeek = firstDay.getDay(); // 0 = Sunday, 1 = Monday, etc.
  for (let i = 0; i < firstDayOfWeek; i++) {
    const prevMonthLastDay = new Date(year, month, 0);
    const day = prevMonthLastDay.getDate() - firstDayOfWeek + i + 1;
    currentWeek.push({
      date: `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
      weekday: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][i],
      is_today: false,
      in_current_month: false,
      sessions: [],
    });
  }

  // Add days of current month
  for (let day = 1; day <= totalDays; day++) {
    const dayDate = new Date(year, month, day);
    const dayOfWeek = dayDate.getDay();

    const isToday =
      today.getFullYear() === year && today.getMonth() === month && today.getDate() === day;

    // Generate some random sessions for this student (more likely on weekdays)
    let sessions: MockGameSession[] = [];
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      // Not weekend
      // 30% chance of having sessions on weekdays
      if (Math.random() < 0.3) {
        sessions = generateMockSessions(1 + Math.floor(Math.random() * 2), studentId);
      }
    } else {
      // 10% chance on weekends
      if (Math.random() < 0.1) {
        sessions = generateMockSessions(1, studentId);
      }
    }

    currentWeek.push({
      date: `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
      weekday: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayOfWeek],
      is_today: isToday,
      in_current_month: true,
      sessions: sessions,
    });

    // Start a new week if we've reached Sunday or the last day
    if (dayOfWeek === 6 || day === totalDays) {
      weeks.push(currentWeek);
      currentWeek = [];
    }
  }

  // Fill in the last week with days from next month if needed
  if (currentWeek.length > 0) {
    const nextMonthDay = 1;
    while (currentWeek.length < 7) {
      const dayIndex = currentWeek.length;
      currentWeek.push({
        date: `${year}-${String(month + 2).padStart(2, '0')}-${String(nextMonthDay + dayIndex - 1).padStart(2, '0')}`,
        weekday: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayIndex],
        is_today: false,
        in_current_month: false,
        sessions: [],
      });
    }
    weeks.push(currentWeek);
  }

  return weeks;
}

// Helper function to generate mock sessions for a student
function generateMockSessions(count: number, studentId: number): MockGameSession[] {
  const sessions: MockGameSession[] = [];
  const gameIds = ['memory-game', 'matching-game', 'sorting-game'];

  for (let i = 0; i < count; i++) {
    const sessionId = `session_${Math.random().toString(36).substring(2, 15)}_${Math.random().toString(36).substring(2, 15)}`;
    const gameId = gameIds[Math.floor(Math.random() * gameIds.length)];
    const totalQuestions = 10 + Math.floor(Math.random() * 10); // 10-20 questions

    // Generate random progress
    const progress = [];
    const stepsCompleted = Math.floor(Math.random() * (totalQuestions + 1));
    for (let step = 0; step < stepsCompleted; step++) {
      progress.push({
        step: step + 1,
        correct: Math.random() > 0.3, // 70% correct rate
      });
    }

    // Determine status
    let status: 'in_progress' | 'completed' | 'not_started' = 'in_progress';
    if (progress.length === 0) {
      status = 'not_started';
    } else if (progress.length >= totalQuestions) {
      status = 'completed';
    }

    sessions.push({
      session_id: sessionId,
      game_id: gameId,
      total_questions: totalQuestions,
      progress: progress,
      status: status,
      duration_seconds: status === 'completed' ? 60 + Math.floor(Math.random() * 300) : null,
      still_in_progress: status === 'in_progress',
    });
  }

  return sessions;
}

// Export the mock API instance
export const mockApi: AxiosInstance = mockAxios as unknown as AxiosInstance;
