// Removed unused imports (useState, Message)
import { useRef, useCallback, useEffect } from 'react';

interface UseTypingEffectProps {
  onMessageUpdate: (messageId: number, text: string) => void;
}

export const useTypingEffect = ({ onMessageUpdate }: UseTypingEffectProps) => {
  const typingQueueRef = useRef<string[]>([]);
  const isTypingRef = useRef<boolean>(false);
  const typingIntervalRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const lastDisplayedTextRef = useRef<string>('');
  const typingSpeedRef = useRef<number>(3);
  const charsPerUpdateRef = useRef<number>(1);
  const streamingMessageIdRef = useRef<number | null>(null);

  const processTypingQueue = useCallback(() => {
    if (typingQueueRef.current.length === 0) {
      isTypingRef.current = false;
      return;
    }

    isTypingRef.current = true;
    const nextText = typingQueueRef.current[0];

    if (streamingMessageIdRef.current === null) {
      // Brak aktywnej wiadomości do aktualizacji
      return;
    }

    const currentText = lastDisplayedTextRef.current;
    const targetText = nextText;

    if (currentText === targetText) {
      typingQueueRef.current.shift();
      typingIntervalRef.current = setTimeout(processTypingQueue, 10);
      return;
    }

    if (targetText.length < 100 && !targetText.includes('.') && !targetText.includes('\n\n')) {
      console.log('Wykryto potencjalnie niekompletną wiadomość, czekam na więcej treści');
      typingIntervalRef.current = setTimeout(processTypingQueue, 500);
      return;
    }

    const charsToAdd = Math.min(charsPerUpdateRef.current, targetText.length - currentText.length);
    const newText = targetText.substring(0, currentText.length + charsToAdd);
    lastDisplayedTextRef.current = newText;

    if (streamingMessageIdRef.current !== null) {
      onMessageUpdate(streamingMessageIdRef.current, newText);
    }

    typingIntervalRef.current = setTimeout(processTypingQueue, typingSpeedRef.current);
  }, [onMessageUpdate]);

  const addToTypingQueue = useCallback(
    (text: string, messageId: number) => {
      typingQueueRef.current = [text];
      streamingMessageIdRef.current = messageId;
      lastDisplayedTextRef.current = '';

      if (!isTypingRef.current) {
        if (typingIntervalRef.current) {
          clearTimeout(typingIntervalRef.current);
        }
        typingIntervalRef.current = setTimeout(processTypingQueue, 10);
      }
    },
    [processTypingQueue]
  );

  useEffect(() => {
    return () => {
      if (typingIntervalRef.current) {
        clearTimeout(typingIntervalRef.current);
      }
    };
  }, []);

  return {
    addToTypingQueue,
  };
};

export default useTypingEffect;
