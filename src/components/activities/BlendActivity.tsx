import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  activityService,
  useActivityDetails,
  useCompleteActivity,
  useSessionDetails,
} from '../../services/activityService';
import { useQuery } from '@tanstack/react-query';
import { Session, SessionProgress, TodaysSessions } from '../../types/session';
import { useInterventionStore } from '../../store/interventionStore';
import logger from '../../utils/logger';

export const BlendActivity: React.FC = () => {
  const { activityId } = useParams<{ activityId: string }>();
  const navigate = useNavigate();

  const {
    data: activityDetails,
    isLoading: isLoadingActivity,
    error: activityError,
  } = useActivityDetails(activityId || '');
  // Pobieramy wszystkie sesje najpierw
  const { data: todaysSessionsForLookup } = useQuery<TodaysSessions>({
    queryKey: ['todaysSessions'],
    queryFn: () => activityService.getTodaysSessions(),
    staleTime: 30000,
  });

  // Znajdź session_id na podstawie activityId (może być game_id lub session_id)
  const actualSessionId = React.useMemo(() => {
    if (!todaysSessionsForLookup || !activityId) return activityId;

    // Sprawdź, czy activityId to już session_id
    const foundBySessionId = activityService.findSessionById(todaysSessionsForLookup, activityId);
    if (foundBySessionId) {
      return activityId;
    }

    // Jeśli nie, spróbuj znaleźć sesję po game_id
    const allSessions = activityService.flattenSessionsList(todaysSessionsForLookup);
    const foundByGameId = allSessions.find(session => session.game_id === activityId);
    if (foundByGameId) {
      return foundByGameId.session_id;
    }
    return activityId;
  }, [todaysSessionsForLookup, activityId]);

  const {
    data: sessionDetails,
    isLoading: isLoadingSession,
    error: sessionError,
    refetch: refetchSessionDetails,
  } = useSessionDetails(actualSessionId || '');
  const completeActivityMutation = useCompleteActivity();
  const interventionStore = useInterventionStore();

  // Auto-fetch interventions if not loaded
  React.useEffect(() => {
    if (!interventionStore.isLoaded && !interventionStore.isLoading) {
      interventionStore.fetchInterventions();
    }
  }, [interventionStore]);

  // Force refetch session details when component mounts to get fresh data
  React.useEffect(() => {
    if (actualSessionId) {
      // Dane są teraz niecache'owane, więc refetch natychmiast
      refetchSessionDetails();
    }
  }, [actualSessionId, refetchSessionDetails]);

  // Hook do pobierania nazwy gry używając intervention_id
  const gameTitle = interventionStore.getGameNameByInterventionId(
    sessionDetails?.intervention_id || '',
    sessionDetails?.game_id || ''
  );

  // Stan do przechowywania lokalnych aktualizacji postępu
  const [localProgress, setLocalProgress] = useState<SessionProgress[]>([]);

  // Filtrujemy sesje, aby znaleźć wszystkie dla tej samej gry co bieżąca sesja
  const sessionsForSameGame = React.useMemo(() => {
    if (!todaysSessionsForLookup || !sessionDetails) return [];

    // Korzystamy z funkcji pomocniczej do spłaszczenia listy sesji i filtrowania po game_id
    const allSessions = activityService.flattenSessionsList(
      todaysSessionsForLookup,
      sessionDetails?.game_id
    );

    return allSessions;
  }, [todaysSessionsForLookup, sessionDetails]);

  useEffect(() => {
    if (!activityId) {
      navigate('/dashboard');
      return;
    }

    // Jeśli mamy dane sesji, inicjalizuj lokalny postęp
    if (
      sessionDetails?.progress &&
      sessionDetails.progress.length > 0 &&
      localProgress.length === 0
    ) {
      setLocalProgress(sessionDetails.progress);
    }

    // Subskrybuj aktualizacje postępu dla tej aktywności
    const unsubscribe = activityService.subscribeToProgressUpdates(activityProgress => {
      // Aktualizuj lokalny postęp
      setLocalProgress(prevProgress => {
        // Konwertuj ActivityProgress na SessionProgress
        const sessionProgress: SessionProgress = {
          step: activityProgress.exerciseNumber,
          correct: activityProgress.iteration1, // Używamy iteration1 jako flagi correct
        };

        // Znajdź indeks kroku, który został zaktualizowany
        const stepIndex = prevProgress.findIndex(p => p.step === sessionProgress.step);

        if (stepIndex !== -1) {
          // Aktualizuj istniejący krok
          const newProgress = [...prevProgress];
          newProgress[stepIndex] = sessionProgress;
          return newProgress;
        } else {
          // Dodaj nowy krok
          return [...prevProgress, sessionProgress];
        }
      });
    });

    return () => {
      // Cancel subscription when component unmounts
      unsubscribe();
    };
  }, [activityId, navigate, sessionDetails, localProgress.length]);

  const handleGoToDashboard = async () => {
    if (activityId) {
      try {
        // Oznacz aktywność jako ukończoną przed powrotem do dashboardu
        await completeActivityMutation.mutateAsync(activityId);
      } catch (error) {
        logger.error('Error while marking activity as completed:', error);
      }
    }
    navigate('/dashboard');
  };

  const isLoading = isLoadingActivity || isLoadingSession;
  const error = activityError || sessionError;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-700"></div>
      </div>
    );
  }

  if (error || !activityDetails || !sessionDetails) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <p className="text-red-500 mb-4">Failed to load activity details.</p>
        <button
          onClick={() => navigate('/dashboard')}
          className="bg-[#005773] cursor-pointer hover:bg-[#00425a] text-white py-3 px-8 rounded-md text-sm font-medium transition-colors duration-200"
        >
          RETURN TO DASHBOARD
        </button>
      </div>
    );
  }

  // Generowanie danych dla tabeli postępu
  const generateExerciseData = () => {
    // Jeśli nie mamy danych sesji, zwracamy puste dane
    if (!sessionDetails) {
      return Array.from({ length: 6 }, (_, index) => ({
        number: index + 1,
        iteration1: false,
      }));
    }

    // Jeśli mamy dane o postępie, używamy ich
    if (sessionDetails?.progress && sessionDetails.progress.length > 0) {
      return sessionDetails.progress.map((p: SessionProgress, index: number) => ({
        number: p.step || index + 1,
        iteration1: p.correct,
      }));
    }

    // Jeśli mamy total_questions, generujemy odpowiednią liczbę ćwiczeń
    if (sessionDetails?.total_questions) {
      return Array.from({ length: sessionDetails.total_questions }, (_, index) => ({
        number: index + 1,
        iteration1: false, // Dla zaplanowanej sesji wszystkie ćwiczenia są niewykonane
      }));
    }

    // W przeciwnym razie generujemy domyślne dane (6 ćwiczeń)
    return Array.from({ length: 6 }, (_, index) => ({
      number: index + 1,
      iteration1: false,
    }));
  };

  const exerciseData = generateExerciseData();

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Nagłówek z przyciskiem zamknięcia */}
      <div className="flex items-center justify-between mb-12">
        <div className="flex items-center">
          <button
            className="w-10 h-10 bg-[#005773] rounded-full flex items-center justify-center mr-4"
            onClick={handleGoToDashboard}
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
          <div>
            <div className="text-gray-600 text-sm">{gameTitle}</div>
            <h1 className="text-2xl font-semibold text-gray-800">{activityDetails.studentName}</h1>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <span className="text-gray-700 mr-2">Sessions</span>
            <div className="flex items-center">
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M13.3334 4L6.00008 11.3333L2.66675 8"
                  stroke="#10B981"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <span className="ml-1 text-gray-600">
                {sessionsForSameGame?.length || 0} sessions
              </span>
            </div>
          </div>

          <div className="flex items-center">
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="10" cy="10" r="9" stroke="#E5E7EB" strokeWidth="2" />
              <path
                d="M10 5V10L13 13"
                stroke="#6B7280"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span className="ml-1 text-gray-600">0:42</span>
          </div>

          <div className="flex items-center">
            <span className="text-gray-700 mr-2">Jump to</span>
            <button className="p-1">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 7.5L10 12.5L15 7.5"
                  stroke="#6B7280"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Główna zawartość */}
      <div className="text-center mb-12">
        <h2 className="text-3xl font-semibold text-gray-700 mb-16">Summary of {gameTitle}</h2>

        {/* Tabela postępu */}
        <div className="bg-gray-800 rounded-lg overflow-hidden max-w-3xl mx-auto">
          {/* Nagłówek tabeli */}
          <div className="bg-gray-800 text-white p-4 border-b border-gray-700">
            <div className="text-center">Exercise progress</div>
          </div>

          {/* Wiersz z numerami ćwiczeń */}
          <div className="grid grid-cols-7 bg-gray-800 text-white">
            <div className="p-4 border-r border-gray-700">Exercise number:</div>
            {exerciseData.map(exercise => (
              <div key={`number-${exercise.number}`} className="p-4 text-center">
                {exercise.number}
              </div>
            ))}
          </div>

          {/* Wiersze dla każdej sesji */}
          {sessionsForSameGame && sessionsForSameGame.length > 0 ? (
            sessionsForSameGame.map((session: Session, sessionIndex: number) => (
              <div
                key={`session-${session.session_id}`}
                className={`grid grid-cols-7 bg-white ${sessionIndex > 0 ? 'border-t border-gray-200' : ''}`}
              >
                <div className="p-4 border-r border-gray-200">Iteration {sessionIndex + 1}:</div>
                {exerciseData.map((exercise: { number: number; iteration1: boolean }) => (
                  <div
                    key={`iter-${sessionIndex}-${exercise.number}`}
                    className="p-4 flex justify-center items-center"
                  >
                    {/* Dla bieżącej sesji używamy danych z exerciseData */}
                    {session.session_id === sessionDetails?.session_id && exercise.iteration1 ? (
                      <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                        <svg
                          key={`check-icon-${exercise.number}-${sessionIndex}`}
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            key={`check-path-${exercise.number}-${sessionIndex}`}
                            d="M13.3334 4L6.00008 11.3333L2.66675 8"
                            stroke="white"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    ) : (
                      <div className="w-8 h-8 rounded-full bg-purple-600 flex items-center justify-center">
                        <svg
                          key={`cross-icon-${exercise.number}-${sessionIndex}`}
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            key={`cross-path-${exercise.number}-${sessionIndex}`}
                            d="M12 4L4 12M4 4L12 12"
                            stroke="white"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ))
          ) : (
            <div className="grid grid-cols-7 bg-white">
              <div className="p-4 border-r border-gray-200">No sessions</div>
              {exerciseData.map((exercise: { number: number; iteration1: boolean }) => (
                <div
                  key={`no-sessions-${exercise.number}`}
                  className="p-4 flex justify-center items-center"
                >
                  <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                    <svg
                      key={`no-session-icon-${exercise.number}`}
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        key={`no-session-path-${exercise.number}`}
                        d="M12 4L4 12M4 4L12 12"
                        stroke="white"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Przycisk powrotu do dashboardu */}
      <div className="text-center mt-16">
        <button
          onClick={handleGoToDashboard}
          className="bg-[#005773] hover:bg-[#00425a] text-white py-3 px-8 rounded-md text-sm font-medium transition-colors duration-200"
        >
          RETURN TO DASHBOARD
        </button>
      </div>
    </div>
  );
};
