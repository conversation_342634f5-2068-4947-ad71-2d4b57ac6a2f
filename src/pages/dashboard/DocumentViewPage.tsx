import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const MOCK_DOCUMENTS = {
  '1': {
    name: 'Example PDF Document',
    type: 'pdf',
    url: '/pdfs/sample.pdf',
  },
  '2': {
    name: 'Sample PDF Document',
    type: 'pdf',
    url: '/pdfs/sample.pdf',
  },
  '3': {
    name: 'Test PDF Document',
    type: 'pdf',
    url: '/pdfs/sample.pdf',
  },
  '4': {
    name: 'Demo PDF Document',
    type: 'pdf',
    url: '/pdfs/sample.pdf',
  },
  '5': {
    name: 'Practice PDF Document',
    type: 'pdf',
    url: '/pdfs/sample.pdf',
  },
};

export const DocumentViewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const document = MOCK_DOCUMENTS[id as keyof typeof MOCK_DOCUMENTS];
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [scale, setScale] = useState(1.0);

  useEffect(() => {
    const checkPdfExists = async () => {
      try {
        const response = await fetch(document?.url || '');
        if (!response.ok) {
          throw new Error('PDF file not found');
        }
      } catch (err) {
        setError('Cannot load PDF document. Make sure the file exists in public/pdfs/.');
        setLoading(false);
      }
    };

    if (document) {
      checkPdfExists();
    }
  }, [document]);

  if (!document) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <h1 className="text-2xl font-semibold text-gray-900 mb-4">Document not found</h1>
        <button
          onClick={() => navigate('/dashboard/documents')}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
        >
          Back to documents
        </button>
      </div>
    );
  }

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setLoading(false);
    setError(null);
  }

  function onDocumentLoadError(error: Error) {
    console.error('Error - loading PDF:', error);
    setError('Error - loading PDF. Try again.');
    setLoading(false);
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between gap-4 mb-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/dashboard/documents')}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">{document.name}</h1>
            <p className="text-sm text-gray-500">.{document.type}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setScale(prev => Math.max(0.5, prev - 0.1))}
            className="p-2 hover:bg-gray-100 rounded-lg"
            title="Zoom out"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>
          <span className="text-sm text-gray-600">{Math.round(scale * 100)}%</span>
          <button
            onClick={() => setScale(prev => Math.min(2, prev + 0.1))}
            className="p-2 hover:bg-gray-100 rounded-lg"
            title="Zoom in"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
          </button>
        </div>
      </div>

      <div className="flex-1 bg-white rounded-lg shadow-sm overflow-auto p-4">
        {error ? (
          <div className="flex flex-col items-center justify-center h-full">
            <p className="text-red-500 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              Try again
            </button>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            {loading && (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            )}
            <Document
              file={document.url}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={onDocumentLoadError}
              className="max-w-full"
              loading={null}
            >
              <Page
                pageNumber={pageNumber}
                renderTextLayer={true}
                renderAnnotationLayer={true}
                className="max-w-full"
                loading={null}
                scale={scale}
              />
            </Document>

            {numPages && (
              <div className="flex items-center gap-4 mt-4">
                <button
                  onClick={() => setPageNumber(prev => Math.max(prev - 1, 1))}
                  disabled={pageNumber <= 1}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg disabled:opacity-50"
                >
                  Previous
                </button>
                <p className="text-sm text-gray-600">
                  Page {pageNumber} of {numPages}
                </p>
                <button
                  onClick={() => setPageNumber(prev => Math.min(prev + 1, numPages))}
                  disabled={pageNumber >= numPages}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg disabled:opacity-50"
                >
                  Next
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
