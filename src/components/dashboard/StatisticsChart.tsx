import React from 'react';

interface StatisticsData {
  label: string;
  value: number;
  color: string;
}

interface StatisticsChartProps {
  data: StatisticsData[];
}

export const StatisticsChart: React.FC<StatisticsChartProps> = ({ data }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);

  return (
    <div className="bg-white p-6 rounded-lg">
      <h2 className="text-xl font-semibold mb-6">Statistics</h2>
      <div className="relative w-64 h-64 mx-auto">
        <div className="absolute inset-0">
          {/* Tu będzie miejsce na implementację wykresu kołowego */}
          <div className="w-full h-full rounded-full border-4 border-gray-100"></div>
        </div>
        <div className="mt-8">
          {data.map((item, index) => (
            <div key={index} className="flex items-center mb-2">
              <div className={`w-3 h-3 rounded-full ${item.color} mr-2`}></div>
              <span className="text-gray-600 uppercase text-sm">{item.label}</span>
              <span className="ml-auto text-gray-900">
                {Math.round((item.value / total) * 100)}%
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
