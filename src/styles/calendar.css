.calendar-container {
  max-width: 1200px;
  margin: 0 auto;
  background-color: white;
  border-radius: 8px;
  /*box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);*/
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  /*border-bottom: 1px solid #E4E4E4;*/
}

.calendar-title {
  font-size: 24px;
  color: #445577;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.calendar-title i {
  margin-right: 10px;
  color: #667799;
}

.month-selector {
  position: relative;
  width: 200px;
}

.month-selector select {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 16px;
  appearance: none;
  cursor: pointer;
}

.month-selector::after {
  content: '▼';
  font-size: 12px;
  color: #777;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.view-toggle {
  display: flex;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #0f4c81;
}

.view-toggle button {
  padding: 10px 20px;
  border: none;
  background-color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #0f4c81;
  transition: all 0.2s;
}

.view-toggle button.active {
  background-color: #0f4c81;
  color: white;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  width: 100%;
}

.week-row {
  display: contents;
}

.week-row .calendar-day:first-child {
  border-left: 20px solid #f1f5f8;
}

.week-row.current-week .calendar-day:first-child {
  /*border-left: 20px solid #0f4c81;*/
}

.week-row.current-week .current-week-day:first-child:before {
  content: '';
  position: absolute;
  left: 0;
  width: 20px;
  height: 100%;
  background: #0f4c81;
  z-index: 1;
}

.calendar-day {
  border: 1px solid #eee;
  min-height: 100px;
  padding: 10px;
  position: relative;
}

.week-header-wrapper {
  display: contents;
}

.day-header {
  text-align: center;
  padding: 12px 15px;
  font-family: Inter;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0px;
  color: #445577;
  border: 1px solid #e4e4e4;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  box-shadow:
    0px 2px 4px 0px #cacaca1a,
    0px 8px 8px 0px #cacaca17,
    0px 17px 10px 0px #cacaca0d,
    0px 30px 12px 0px #cacaca03,
    0px 47px 13px 0px #cacaca00;
  margin-bottom: 10px;
}

.day-header:first-child {
  border-top-left-radius: 8px;
}

.day-header:last-child {
  border-top-right-radius: 8px;
}

.date-number {
  font-size: 18px;
  margin-bottom: 10px;
  color: #555;
}

.today {
  background-color: #f0f7ff;
}

.today .date-number {
  color: #0f4c81;
  font-weight: bold;
}

.weekend {
  background: url('../assets/disable-bg.jpg');
  background-size: cover;
  background-repeat: repeat;
}

.sunday {
  background: none;
}

.progress-dots {
  display: flex;
  margin-bottom: 6px;
  gap: 5px;
  background: #f1f5f8;
  width: auto;
  border-radius: 12px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 2px;
  background-color: #ddd;
}

.dot.completed {
  background-color: #42b883;
}

.dot.partial {
  background-color: #9b59b6;
}

.dot.not-attempted {
  background-color: #ddd;
}

.progress-tooltip {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
  padding: 0;
  min-width: 320px;
  max-width: 400px;
  pointer-events: auto;
  animation: fadeIn 0.15s;
  z-index: 9999;
  position: absolute;
}

.progress-tooltip-inner {
  padding: 15px 8px;
  background: white;
  border-radius: 12px;
}

.progress-tooltip-title {
  font-size: 16px;
  font-weight: 600;
  background: #23272f;
  color: #fff;
  border-radius: 8px 8px 0 0;
  padding: 8px 0 8px 0;
  text-align: center;
  margin-bottom: 10px;
}

.progress-tooltip-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 15px;
}

.progress-tooltip-table th,
.progress-tooltip-table td {
  text-align: center;
  padding: 4px 2px;
}

.progress-tooltip-table th {
  font-weight: 500;
  color: #23272f;
  background: #f5f5f5;
}

.progress-tooltip-table td:first-child {
  text-align: left;
  font-weight: 500;
  color: #23272f;
}

.progress-tooltip-table td {
  min-width: 24px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status.completed {
  background-color: #e3fcef;
  color: #00875a;
}

.status.in_progress {
  background-color: #deebff;
  color: #0747a6;
}

.status.not_started {
  background-color: #f4f5f7;
  color: #6b778c;
}

.progress-tooltip-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 8px;
}

.progress-tooltip-table th,
.progress-tooltip-table td {
  padding: 4px 8px;
  text-align: left;
  font-size: 12px;
}

.progress-tooltip-table th {
  color: #6b778c;
  font-weight: 500;
  /*border-bottom: 1px solid #dfe1e6;*/
}

.progress-tooltip-table td {
  color: #172b4d;
}

.progress-tooltip-title {
  font-size: 14px;
  font-weight: 500;
  color: #172b4d;
  margin-bottom: 8px;
}
