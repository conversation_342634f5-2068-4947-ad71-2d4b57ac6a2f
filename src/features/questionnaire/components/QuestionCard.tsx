import React from 'react';
import { Question, Answer } from '../types';
import { scaleOptions } from '../data/questions';
interface Props {
  question: Question;
  answer: Answer | undefined;
  onAnswerChange: (id: number, val: number) => void;
  questionNumber: number;
}
export const QuestionCard: React.FC<Props> = ({
  question,
  answer,
  onAnswerChange,
  questionNumber,
}) => (
  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
    <div className="mb-6">
      <span className="bg-blue-100 text-blue-700 text-sm font-semibold px-3 py-1 rounded-full">
        Question {questionNumber}
      </span>
      <p className="text-gray-800 text-lg mt-4 font-medium leading-relaxed">{question.text}</p>
    </div>
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-7 gap-3">
      {scaleOptions.map(opt => (
        <label
          key={opt.value}
          className={`relative flex flex-col items-center p-3 rounded-lg border-2 cursor-pointer ${answer?.value === opt.value ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-gray-50'}`}
        >
          <input
            type="radio"
            className="sr-only"
            name={`q-${question.id}`}
            value={opt.value}
            checked={answer?.value === opt.value}
            onChange={() => onAnswerChange(question.id, opt.value)}
          />
          <div
            className={`w-4 h-4 rounded-full border-2 mb-2 ${answer?.value === opt.value ? 'border-blue-500 bg-blue-500' : 'border-gray-300'}`}
          />
          <span
            className={`text-xs text-center ${answer?.value === opt.value ? 'text-blue-700' : 'text-gray-600'}`}
          >
            {opt.label}
          </span>
        </label>
      ))}
    </div>
  </div>
);
