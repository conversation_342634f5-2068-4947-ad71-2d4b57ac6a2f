import React from 'react';
import { Calculator } from 'lucide-react';

interface QuizHeaderProps {
  currentQuestion: number;
  totalQuestions: number;
  score: number;
}

const QuizHeader: React.FC<QuizHeaderProps> = ({ currentQuestion, totalQuestions, score }) => {
  return (
    <div className="w-full mb-8">
      <div className="flex items-center justify-center mb-4">
        <Calculator className="w-8 h-8 text-blue-600 mr-2" />
        <h1 className="text-3xl md:text-4xl font-bold text-blue-700">Math Quiz 1</h1>
      </div>

      <div className="flex justify-between items-center px-2">
        <div className="text-sm md:text-base text-gray-700 font-medium">
          Question: <span className="text-blue-700">{currentQuestion}</span> / {totalQuestions}
        </div>

        <div className="bg-gradient-to-r from-blue-500 to-blue-700 rounded-full h-2 w-1/2 mx-2">
          <div
            className="bg-gradient-to-r from-blue-300 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${(currentQuestion / totalQuestions) * 100}%` }}
          ></div>
        </div>

        <div className="text-sm md:text-base text-gray-700 font-medium">
          Score: <span className="text-blue-700">{score}</span>
        </div>
      </div>
    </div>
  );
};

export default QuizHeader;
