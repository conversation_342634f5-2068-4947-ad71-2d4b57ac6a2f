{"root": ["./src/app.tsx", "./src/config.ts", "./src/contants.ts", "./src/main.tsx", "./src/vite-env.d.ts", "./src/api/documents.ts", "./src/components/assessmentdetailscomponent.tsx", "./src/components/chatcomponent.tsx", "./src/components/3d/model3d.tsx", "./src/components/button/button.tsx", "./src/components/checkbox/checkbox.tsx", "./src/components/form/loginform.tsx", "./src/components/input/input.tsx", "./src/components/logo/logo.tsx", "./src/components/activities/blendactivity.tsx", "./src/components/assessments/assessmentexecution.tsx", "./src/components/assessments/assessmentresults.tsx", "./src/components/assessments/todaysassessments.tsx", "./src/components/auth/protectedroute.tsx", "./src/components/auth/rolebasedredirect.tsx", "./src/components/auth/loginform/loginform.tsx", "./src/components/chat/chatinput.tsx", "./src/components/chat/chatmessage.tsx", "./src/components/common/avatarcreator.tsx", "./src/components/common/avatardisplay.tsx", "./src/components/common/errormessage.tsx", "./src/components/common/loader.tsx", "./src/components/common/quickavatardownload.tsx", "./src/components/common/index.ts", "./src/components/dashboard/dashboardbutton.tsx", "./src/components/dashboard/gamespage.tsx", "./src/components/dashboard/keystats.tsx", "./src/components/dashboard/recenttests.tsx", "./src/components/dashboard/statcard.tsx", "./src/components/dashboard/statisticschart.tsx", "./src/components/dashboard/studentdashboard.tsx", "./src/components/dashboard/studentstable.tsx", "./src/components/dashboard/teacherdashboard.tsx", "./src/components/documents/documentslist.tsx", "./src/components/games/completionbanner.tsx", "./src/components/games/gamecard.tsx", "./src/components/games/gamecontent.tsx", "./src/components/games/gameexerciselayout.tsx", "./src/components/games/gameinterventionscomponent.tsx", "./src/components/games/gameregistry.ts", "./src/components/games/gamesettingsbutton.tsx", "./src/components/games/gamestatssummary.tsx", "./src/components/games/gametags.tsx", "./src/components/games/gamewrapper.tsx", "./src/components/interventions/interventionfilters.tsx", "./src/components/interventions/interventionsfromidscomponent.tsx", "./src/components/layout/dashboardlayout.tsx", "./src/components/layout/navbar.tsx", "./src/components/layout/sidebar.tsx", "./src/components/layout/usermenu.tsx", "./src/components/layout/sidebar/dropdownmenuitem.tsx", "./src/components/layout/sidebar/menuitem.tsx", "./src/components/modals/deleteconfirmationmodal.tsx", "./src/components/modals/gamesettingsmodal.tsx", "./src/components/modals/modal.tsx", "./src/components/modals/successmodal.tsx", "./src/components/students/documentssection.tsx", "./src/components/students/interventioncard.tsx", "./src/components/students/interventionsgrid.tsx", "./src/components/students/progresscalendar.tsx", "./src/components/students/progresssection.tsx", "./src/components/students/recentcard.tsx", "./src/components/students/studentdeleteconfirmationmodal.tsx", "./src/components/students/studentslist.tsx", "./src/components/students/testsgrid.tsx", "./src/components/test/gamenametest.tsx", "./src/components/tests/assigntestmodal.tsx", "./src/components/tests/testsgrid.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/floatinginput.tsx", "./src/components/ui/input.tsx", "./src/components/ui/radiogroup.tsx", "./src/components/ui/select.tsx", "./src/components/ui/spinner.tsx", "./src/constants/auth.ts", "./src/features/questionnaire/questionnairepage.tsx", "./src/features/questionnaire/types.ts", "./src/features/questionnaire/components/progressbar.tsx", "./src/features/questionnaire/components/questioncard.tsx", "./src/features/questionnaire/components/questiontransition.tsx", "./src/features/questionnaire/components/resultssummary.tsx", "./src/features/questionnaire/components/singlequestionview.tsx", "./src/features/questionnaire/components/welcomescreen.tsx", "./src/features/questionnaire/data/questions.ts", "./src/features/questionnaire/utils/calculateresults.ts", "./src/games/blend-game/blendactivity.tsx", "./src/games/blend-game/feedback.tsx", "./src/games/blend-game/optionbutton.tsx", "./src/games/blend-game/progressbar.tsx", "./src/games/blend-game/question.tsx", "./src/games/blend-game/data.ts", "./src/games/common/gameresults.tsx", "./src/games/common/types.ts", "./src/games/common/websocket.ts", "./src/games/components/ui/drawer.tsx", "./src/games/components/ui/sonner.tsx", "./src/games/engine/game.tsx", "./src/games/engine/gamefactory.ts", "./src/games/engine/gameregistry.ts", "./src/games/engine/gamerenderer.tsx", "./src/games/engine/index.ts", "./src/games/engine/types.ts", "./src/games/engine/data/index.ts", "./src/games/engine/data/mastermindtasks.ts", "./src/games/engine/data/mathtasks1.ts", "./src/games/engine/data/mathtasks2.ts", "./src/games/engine/data/semanticmappingtasks.ts", "./src/games/engine/renderers/baserenderer.tsx", "./src/games/engine/renderers/blendrenderer.tsx", "./src/games/engine/renderers/mathrenderer.tsx", "./src/games/engine/renderers/mathrenderer1.tsx", "./src/games/engine/renderers/mathrenderer2.tsx", "./src/games/engine/renderers/memoryrenderer.tsx", "./src/games/engine/renderers/memoryrenderer1.tsx", "./src/games/engine/renderers/memoryrenderer2.tsx", "./src/games/engine/renderers/memoryrenderer2_new.tsx", "./src/games/engine/renderers/renderertemplate.tsx", "./src/games/engine/renderers/semanticmappingrenderer.tsx", "./src/games/engine/renderers/wordbuildingrenderer.tsx", "./src/games/engine/renderers/index.ts", "./src/games/lib/utils.ts", "./src/games/math-game/mathquiz.tsx", "./src/games/math-game/quizheader.tsx", "./src/games/math-game/quizquestion.tsx", "./src/games/math-game/quizresults.tsx", "./src/games/math-game/visualobjects.tsx", "./src/games/math-game/quizdata.ts", "./src/games/math-game1/mathquiz.tsx", "./src/games/math-game1/quizheader.tsx", "./src/games/math-game1/quizquestion.tsx", "./src/games/math-game1/visualobjects.tsx", "./src/games/math-game1/quizdata.ts", "./src/games/math-game2/mathfilloperatorquiz.tsx", "./src/games/math-game2/mathquiz.tsx", "./src/games/math-game2/quizheader.tsx", "./src/games/math-game2/quizquestion.tsx", "./src/games/math-game2/visualobjects.tsx", "./src/games/math-game2/quizdata.ts", "./src/games/memory-game/memorygame.tsx", "./src/games/memory-game1/memorygame1.tsx", "./src/games/memory-game1/index.ts", "./src/games/memory-game2/memorygame2.tsx", "./src/games/memory-game2/index.ts", "./src/games/semantic-mapping/semanticmappingactivity.tsx", "./src/games/semantic-mapping/index.ts", "./src/games/wordbuilding-game/wordbuildingactivity.tsx", "./src/games/wordbuilding-game/data.ts", "./src/games/wordbuilding-game/index.ts", "./src/hooks/useauth.ts", "./src/hooks/usecurrentstudentid.ts", "./src/hooks/usegamename.ts", "./src/hooks/usegamewebsocket.ts", "./src/lib/queryclient.ts", "./src/mocks/mockdata.ts", "./src/mocks/mocktests.ts", "./src/mocks/mockuser.ts", "./src/pages/interventionselectionpage.tsx", "./src/pages/notfoundpage.tsx", "./src/pages/activities/activitypage.tsx", "./src/pages/auth/loginpage.tsx", "./src/pages/auth/registerpage.tsx", "./src/pages/auth/loginpage/loginpage.tsx", "./src/pages/chat/chatpage.tsx", "./src/pages/chat/index.ts", "./src/pages/chat/types.ts", "./src/pages/chat/components/assessmentcomponent.tsx", "./src/pages/chat/components/chatentryselector.tsx", "./src/pages/chat/components/chatinput.tsx", "./src/pages/chat/components/chatmessage.tsx", "./src/pages/chat/components/gradeselector.tsx", "./src/pages/chat/components/predefinedprompts.tsx", "./src/pages/chat/components/readingareadetailscomponent.tsx", "./src/pages/chat/components/readingareaselector.tsx", "./src/pages/chat/hooks/usechatwebsocket.ts", "./src/pages/chat/hooks/usetextanimation.ts", "./src/pages/chat/hooks/usetypingeffect.ts", "./src/pages/chat/utils/chatutils.ts", "./src/pages/dashboard/avatarcreatorpage.tsx", "./src/pages/dashboard/dashboardpage.tsx", "./src/pages/dashboard/documentchatpage.tsx", "./src/pages/dashboard/documentdetailspage.tsx", "./src/pages/dashboard/documentviewpage.tsx", "./src/pages/dashboard/documentspage.tsx", "./src/pages/dashboard/profilepage.tsx", "./src/pages/dashboard/settingspage.tsx", "./src/pages/dashboard/studentdetailspage.tsx", "./src/pages/dashboard/studentprogresspage.tsx", "./src/pages/dashboard/studentspage.tsx", "./src/pages/games/gamedetailspage.tsx", "./src/pages/tests/testdetailspage.tsx", "./src/pages/tests/testresultspage.tsx", "./src/pages/tests/testspage.tsx", "./src/services/activityservice.ts", "./src/services/api.ts", "./src/services/apimock.ts", "./src/services/assessmentservice.ts", "./src/services/authservice.ts", "./src/services/chatservice.ts", "./src/services/consentservice.ts", "./src/services/gameservice.ts", "./src/services/interventionservice.ts", "./src/services/readingareaservice.ts", "./src/services/sessionservice.ts", "./src/services/studentservice.ts", "./src/services/testassignmentservice.ts", "./src/services/testservice.ts", "./src/services/websocketservice.ts", "./src/services/queries/assessmentqueries.ts", "./src/services/queries/chatqueries.ts", "./src/services/queries/interventionqueries.ts", "./src/services/queries/studentqueries.ts", "./src/services/queries/websocketqueries.ts", "./src/services/queries/__tests__/interventionqueries.test.ts", "./src/store/chatstore.ts", "./src/store/activitystore.ts", "./src/store/gamestatsstore.ts", "./src/store/interventionfilterstore.ts", "./src/store/interventionstore.ts", "./src/store/studenststore.ts", "./src/store/studentsstore.ts", "./src/store/userstore.ts", "./src/store/websocketstore.ts", "./src/types/assessment.ts", "./src/types/gameintervention.ts", "./src/types/session.ts", "./src/types/student.ts", "./src/types/svg.d.ts", "./src/types/test.ts", "./src/utils/avatarutils.ts", "./src/utils/cookies.ts", "./src/utils/logger.ts", "./src/utils/rolebasedtext.ts", "./src/utils/storeutils.ts"], "version": "5.7.3"}