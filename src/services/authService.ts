import { api } from './api';
import cookies from '../utils/cookies';

export interface RegisterData {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  role_id: number;
  age?: number;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface ChangePasswordData {
  old_password: string;
  new_password: string;
}

export interface StudentData {
  id: number;
  first_name: string;
  last_name: string;
  age: number;
  gender: string;
  additional_info: string;
  created_at: string;
  user_id: number;
}

export interface UserData {
  user_id: number;
  email: string;
  role: string;
  role_label: string;
  is_logged_in: boolean;
  avatar_url?: string;
  student?: StudentData;
}

interface LoginResponse {
  access_token: string;
  refresh_token?: string;
  user?: unknown;
}

interface RefreshTokenResponse {
  access_token: string;
  refresh_token?: string;
}

const TOKEN_COOKIE_NAME = 'access_token';
const REFRESH_TOKEN_COOKIE_NAME = 'refresh_token';

export const authService = {
  register: async (data: RegisterData) => {
    const response = await api.post<unknown>('/users/register', data);
    return response.data;
  },

  login: async (data: LoginData): Promise<LoginResponse> => {
    const response = await api.post<LoginResponse>('/users/login', data);
    const { access_token, refresh_token } = response.data;

    cookies.set(TOKEN_COOKIE_NAME, access_token);
    if (refresh_token) {
      cookies.set(REFRESH_TOKEN_COOKIE_NAME, refresh_token, 30);
    }

    return response.data;
  },

  logout: async () => {
    const response = await api.post<unknown>('/users/logout');
    cookies.remove(TOKEN_COOKIE_NAME);
    cookies.remove(REFRESH_TOKEN_COOKIE_NAME);

    return response.data;
  },

  changePassword: async (data: ChangePasswordData) => {
    const response = await api.post<unknown>('/users/change_password', data);
    return response.data;
  },

  getCurrentUser: async () => {
    try {
      const response = await api.get<UserData>('/users/me');
      return response.data;
    } catch (error) {
      console.error('Error fetching user data:', error);
      return null;
    }
  },

  checkToken: () => {
    return !!cookies.get(TOKEN_COOKIE_NAME);
  },

  getRoles: async () => {
    const response = await api.get<unknown>('/users/roles');
    return response.data;
  },

  getAccessToken: () => {
    return cookies.get(TOKEN_COOKIE_NAME) || null;
  },

  refreshToken: async (): Promise<RefreshTokenResponse> => {
    const refreshToken = cookies.get(REFRESH_TOKEN_COOKIE_NAME);

    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await api.post<RefreshTokenResponse>('/users/refresh-token', {
        refresh_token: refreshToken,
      });
      const { access_token, refresh_token } = response.data;

      cookies.set(TOKEN_COOKIE_NAME, access_token);
      if (refresh_token) {
        cookies.set(REFRESH_TOKEN_COOKIE_NAME, refresh_token, 30);
      }

      return response.data;
    } catch (error) {
      cookies.remove(TOKEN_COOKIE_NAME);
      cookies.remove(REFRESH_TOKEN_COOKIE_NAME);
      throw error;
    }
  },

  updateAvatar: async (avatarUrl: string) => {
    const response = await api.post('/users/add_avatar', { url: avatarUrl });
    return response.data;
  },

  getAvatar: async () => {
    const response = await api.get('/users/get_avatar');
    return response.data;
  },
};
