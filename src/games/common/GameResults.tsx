import React from 'react';
import { GameStats } from './types';

interface GameResultsProps {
  stats: GameStats;
  onRestart: () => void;
}

const GameResults: React.FC<GameResultsProps> = ({ stats, onRestart }) => {
  const { correctAnswers, totalQuestions, timeInSeconds, progress } = stats;
  const percentage = Math.round((correctAnswers / totalQuestions) * 100);

  const getFeedbackMessage = () => {
    if (percentage === 100) return 'Perfect score! You are a genius! 🏆';
    if (percentage >= 80) return 'Great job! You did really well! 🌟';
    if (percentage >= 60) return 'Good job! Keep practicing to improve! 👍';
    if (percentage >= 40) return 'Nice effort! More practice will help you improve! 📚';
    return 'Keep practicing! It will get better with time! 💪';
  };

  const calculatedTimeInSeconds = timeInSeconds || 0;
  const minutes = Math.floor(calculatedTimeInSeconds / 60);
  const seconds = (calculatedTimeInSeconds % 60).toString().padStart(2, '0');

  return (
    <div className="max-w-2xl mx-auto p-8 bg-white rounded-2xl shadow-xl text-center">
      <div className="mb-8">
        {/* Ikona sukcesu z animacją */}
        <div className="flex justify-center mb-6">
          <div className="text-6xl text-green-500 animate-bounce-slow">✓</div>
        </div>

        {/* Okrągły wskaźnik procentowy */}
        <div className="flex justify-center mb-6">
          <div className="relative w-40 h-40">
            <svg className="w-full h-full" viewBox="0 0 100 100">
              <circle
                className="text-green-100"
                strokeWidth="10"
                stroke="currentColor"
                fill="transparent"
                r="40"
                cx="50"
                cy="50"
              />
              <circle
                className="text-green-500"
                strokeWidth="10"
                stroke="currentColor"
                fill="transparent"
                r="40"
                cx="50"
                cy="50"
                strokeDasharray={`${percentage * 2.51} 251`}
                strokeDashoffset="0"
                strokeLinecap="round"
              />
            </svg>
            <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
              <span className="text-4xl font-bold text-green-600">{percentage}%</span>
            </div>
          </div>
        </div>

        {/* Tytuł */}
        <h2 className="text-3xl font-bold text-gray-800 mb-6">You completed all tasks!</h2>

        {/* Statystyki w karcie */}
        <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-xl p-6 mb-8 shadow-inner">
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-3">
              <p className="text-gray-500 text-sm uppercase font-semibold">Score</p>
              <p className="text-2xl font-bold text-gray-800">
                {correctAnswers}/{totalQuestions}
              </p>
            </div>
            <div className="text-center p-3 border-x border-gray-200">
              <p className="text-gray-500 text-sm uppercase font-semibold">Time</p>
              <p className="text-2xl font-bold text-gray-800">
                {minutes}m {seconds}s
              </p>
            </div>
            <div className="text-center p-3">
              <p className="text-gray-500 text-sm uppercase font-semibold">Accuracy</p>
              <p className="text-2xl font-bold text-gray-800">{percentage}%</p>
            </div>
          </div>
        </div>

        {/* Wiadomość zwrotna */}
        <div className="mb-8 bg-indigo-50 p-4 rounded-lg border-l-4 border-indigo-400">
          <p className="text-xl font-medium text-gray-700">{getFeedbackMessage()}</p>
        </div>

        {/* Podsumowanie zadań */}
        {progress && progress.length > 0 && (
          <div className="mb-8">
            <h3 className="text-xl font-semibold mb-4 text-gray-700">Task Summary:</h3>
            <div className="space-y-3 max-h-60 overflow-auto pr-2">
              {progress.map((progressItem, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg flex items-center ${
                    progressItem.correct
                      ? 'bg-green-50 border-l-4 border-green-400'
                      : 'bg-red-50 border-l-4 border-red-400'
                  }`}
                >
                  <div
                    className={`mr-3 rounded-full w-8 h-8 flex items-center justify-center ${
                      progressItem.correct ? 'bg-green-400 text-white' : 'bg-red-400 text-white'
                    }`}
                  >
                    {progressItem.correct ? '✓' : '✗'}
                  </div>
                  <p className="font-medium">
                    Task {progressItem.step}: {progressItem.correct ? 'Correct' : 'Incorrect'}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Przycisk zagraj ponownie */}
        <button
          onClick={onRestart}
          className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 font-medium shadow-md hover:shadow-lg transform hover:-translate-y-1 active:translate-y-0"
        >
          Play Again
        </button>
      </div>
    </div>
  );
};

export default GameResults;
