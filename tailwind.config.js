/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  safelist: [
    {
      pattern:
        /bg-(green|red|blue|gray|white|black|yellow|purple|indigo|pink|teal)-(50|100|200|300|400|500|600|700|800|900)/,
      variants: ['hover', 'focus', 'active'],
    },
    {
      pattern:
        /text-(green|red|blue|gray|white|black|yellow|purple|indigo|pink|teal)-(50|100|200|300|400|500|600|700|800|900)/,
      variants: ['hover', 'focus', 'active'],
    },

    {
      pattern:
        /border-(green|red|blue|gray|white|black|yellow|purple|indigo|pink|teal)-(50|100|200|300|400|500|600|700|800|900)/,
      variants: ['hover', 'focus', 'active'],
    },

    'animate-bounce',
    'animate-pulse',
    'animate-spin',
    'animate-ping',
    'animate-fade-in',

    'absolute',
    'inset-0',
    'flex',
    'items-center',
    'justify-center',
    'z-10',
    'pointer-events-none',
    'w-20',
    'h-20',
    'rounded-full',
    'shadow-lg',
    'text-white',
    'w-12',
    'h-12',

    'flex',
    'justify-center',
    'items-center',
    'w-full',
    'text-2xl',
    'font-bold',
    'rounded-2xl',
    'border-4',
    'py-3',
    'px-6',
    'transition-all',
    'duration-300',
    'transform',
    'hover:scale-105',
    'active:scale-95',
    'shadow-md',
    'bg-white',
    'border-blue-500',
    'text-blue-700',
    'hover:bg-blue-50',
    'bg-green-100',
    'border-green-500',
    'text-green-700',
    'animate-pulse',
    'bg-red-100',
    'border-red-500',
    'text-red-700',
    'opacity-75',
    'cursor-not-allowed',

    'w-full',
    'bg-gray-200',
    'h-4',
    'rounded-full',
    'overflow-hidden',
    'bg-blue-500',
    'h-full',
    'transition-all',
    'duration-500',
    'ease-out',
    'flex',
    'justify-between',
    'px-2',
    'text-xs',
    'font-medium',
    'text-gray-600',
    '-mt-4',

    'flex',
    'flex-col',
    'md:flex-row',
    'items-center',
    'gap-6',
    'mb-8',
    'w-full',
    'md:w-1/2',
    'flex',
    'justify-center',
    'relative',
    'w-full',
    'max-w-sm',
    'aspect-square',
    'rounded-2xl',
    'overflow-hidden',
    'border-4',
    'border-blue-400',
    'shadow-lg',
    'w-full',
    'h-full',
    'object-cover',
    'text-3xl',
    'md:text-4xl',
    'font-bold',
    'mb-2',
    'text-blue-800',
    'text-5xl',
    'md:text-6xl',
    'font-bold',
    'my-6',
    'text-center',
    'tracking-wide',
    'text-blue-900',
    'inline-block',
    'w-16',
    'h-12',
    'border-b-4',
    'border-dotted',
    'border-blue-500',
    'mx-1',
    'align-middle',

    'max-w-4xl',
    'mx-auto',
    'p-6',
    'bg-white',
    'rounded-3xl',
    'shadow-lg',
    'relative',
    'mb-6',
    'text-center',
    'py-12',
    'text-4xl',
    'font-bold',
    'text-blue-800',
    'mb-6',
    'text-2xl',
    'text-gray-700',
    'mb-4',
    'mb-8',
    'text-lg',
    'text-gray-600',
    'bg-blue-500',
    'hover:bg-blue-600',
    'text-white',
    'text-xl',
    'font-bold',
    'py-4',
    'px-8',
    'rounded-full',
    'shadow-md',
    'transition-all',
    'duration-300',
    'transform',
    'hover:scale-105',
    'grid',
    'grid-cols-2',
    'gap-4',
    'mt-8',
    'backface-visibility-hidden',
    'rotate-y-180',
  ],
  theme: {
    extend: {
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      animation: {
        fadeIn: 'fadeIn 0.2s ease-out',
        scaleIn: 'scaleIn 0.2s ease-out',
        'fade-in': 'fadeIn 0.5s ease-in-out',
      },
      fontFamily: {
        sans: [
          'Inter',
          'system-ui',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Arial',
          'sans-serif',
        ],
        'fira-sans': ['Fira Sans', 'sans-serif'],
      },
      utilities: {
        '.backface-visibility-hidden': {
          'backface-visibility': 'hidden',
          '-webkit-backface-visibility': 'hidden',
        },
        '.rotate-y-180': {
          transform: 'rotateY(180deg)',
        },
      },
      colors: {
        primaryBtn: {
          DEFAULT: '#CFEBDC',
        },
        inputGray: {
          DEFAULT: '#F9F9F9',
        },
        pastelGreen: {
          DEFAULT: '#CFEBDC',
        },
        pastelBlue: {
          DEFAULT: '#D0E4FF',
        },
        pastelRed: {
          DEFAULT: '#FFD0D0',
        },
        pastelLightGreen: {
          DEFAULT: '#D0FFE4',
        },
        'black-5': {
          DEFAULT: '#1C1C1C0D',
        },
      },
    },
  },
  plugins: [],
};
