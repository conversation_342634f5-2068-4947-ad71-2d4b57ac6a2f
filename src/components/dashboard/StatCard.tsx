import React from 'react';

interface StatCardProps {
  title: string;
  value: number;
  subtitle?: string;
  bgColor?: string;
  isPercentage?: boolean;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  bgColor = 'bg-green-50',
  isPercentage = false,
}) => {
  return (
    <div className={`${bgColor} rounded-lg p-6 flex flex-col`}>
      <h3 className="text-gray-700 text-lg mb-4">{title}</h3>
      <div className="flex flex-col flex-grow">
        <span className="text-gray-900 text-6xl font-semibold mb-2">
          {value}
          {isPercentage ? '%' : ''}
        </span>
        {subtitle && <span className="text-gray-600 text-sm">{subtitle}</span>}
      </div>
    </div>
  );
};
