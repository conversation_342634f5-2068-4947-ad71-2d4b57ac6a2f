import React, { useState, useEffect } from 'react';
import GameResults from '../common/GameResults';
import { getTasksByType } from '../engine/data';
import { SemanticMappingTask } from '../engine/types';
import { BookOpen } from 'lucide-react';

interface GameProgress {
  taskId: string;
  isCorrect: boolean;
  timeSpent: number;
  attempts: number;
}

interface GameStats {
  correctAnswers: number;
  incorrectAnswers: number;
  totalQuestions: number;
  completionPercentage: number;
  startTime: number;
  endTime: number;
  gameId: string;
  studentId: string;
  progress: GameProgress[];
}

interface SemanticMappingActivityProps {
  gameId: string;
  studentId?: string;
  onFinish?: (stats: GameStats) => void;
}

const SemanticMappingActivity: React.FC<SemanticMappingActivityProps> = ({
  gameId,
  studentId,
  onFinish,
}) => {
  const [tasks] = useState<SemanticMappingTask[]>(
    getTasksByType('support-strategy--semantic-mapping') as SemanticMappingTask[]
  );
  const [currentTaskIndex, setCurrentTaskIndex] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const [correctAnswers, setCorrectAnswers] = useState(0);
  const [incorrectAnswers, setIncorrectAnswers] = useState(0);
  const [startTime] = useState(Date.now());
  const [endTime, setEndTime] = useState<number | null>(null);
  const [showFeedback, setShowFeedback] = useState(false);

  // State for dragged elements to their respective places
  const [placedWords, setPlacedWords] = useState<string[]>([]);
  const [placedImages, setPlacedImages] = useState<string[]>([]);

  // State for currently dragged item
  const [draggedItem, setDraggedItem] = useState<{ type: 'word' | 'image'; id: string } | null>(
    null
  );

  const currentTask = tasks[currentTaskIndex];

  // Reset state when task changes
  useEffect(() => {
    setPlacedWords([]);
    setPlacedImages([]);
    setShowFeedback(false);
  }, [currentTaskIndex]);

  // Check if all correct items have been placed
  const allCorrectItemsPlaced = () => {
    if (!currentTask) return false;

    const correctWords = currentTask.relatedWords
      .filter(word => word.correct)
      .map(word => word.word);
    const correctImages = currentTask.images.filter(img => img.correct).map(img => img.url);

    const allWordsPlaced = correctWords.every(word => placedWords.includes(word));
    const allImagesPlaced = correctImages.every(img => placedImages.includes(img));

    return allWordsPlaced && allImagesPlaced;
  };

  // Handle drag start
  const handleDragStart = (type: 'word' | 'image', id: string) => {
    setDraggedItem({ type, id });
  };

  // Handle drop event
  const handleDrop = (position: string) => {
    if (!draggedItem || showFeedback) return;

    if (draggedItem.type === 'word') {
      if (!placedWords.includes(draggedItem.id)) {
        setPlacedWords([...placedWords, draggedItem.id]);
      }
    } else {
      if (!placedImages.includes(draggedItem.id)) {
        setPlacedImages([...placedImages, draggedItem.id]);
      }
    }

    setDraggedItem(null);
  };

  // Handle removing item from the map
  const handleRemoveItem = (type: 'word' | 'image', id: string) => {
    if (showFeedback) return;

    if (type === 'word') {
      setPlacedWords(placedWords.filter(word => word !== id));
    } else {
      setPlacedImages(placedImages.filter(img => img !== id));
    }
  };

  // Check if item is correct
  const isItemCorrect = (type: 'word' | 'image', id: string) => {
    if (!currentTask) return false;

    if (type === 'word') {
      const word = currentTask.relatedWords.find(w => w.word === id);
      return word?.correct || false;
    } else {
      const image = currentTask.images.find(img => img.url === id);
      return image?.correct || false;
    }
  };

  // Check answers
  const handleCheckAnswer = () => {
    setShowFeedback(true);

    // Check if all correct items are in place
    const isCorrect = allCorrectItemsPlaced();

    if (isCorrect) {
      setCorrectAnswers(prev => prev + 1);
    } else {
      setIncorrectAnswers(prev => prev + 1);
    }

    // After 2 seconds, move to the next task or finish the game
    setTimeout(() => {
      if (currentTaskIndex < tasks.length - 1) {
        setCurrentTaskIndex(currentTaskIndex + 1);
      } else {
        const timeNow = Date.now();
        setEndTime(timeNow);
        setShowResults(true);

        if (onFinish) {
          onFinish({
            correctAnswers,
            incorrectAnswers: isCorrect ? incorrectAnswers : incorrectAnswers + 1,
            totalQuestions: tasks.length,
            completionPercentage: 100,
            startTime,
            endTime: timeNow,
            gameId,
            studentId: studentId || '',
            progress: [],
          });
        }
      }
    }, 2000);
  };

  // Calculate completion percentage
  const progressPercentage = (currentTaskIndex / tasks.length) * 100;

  // Calculate game time in seconds
  const timeInSeconds = endTime
    ? Math.floor((endTime - startTime) / 1000)
    : Math.floor((Date.now() - startTime) / 1000);

  if (showResults) {
    return (
      <GameResults
        stats={{
          correctAnswers,
          incorrectAnswers,
          totalQuestions: tasks.length,
          completionPercentage: 100,
          startTime,
          endTime: endTime || Date.now(),
          gameId,
          timeInSeconds,
          studentId: studentId || '',
          progress: [],
        }}
        onRestart={() => window.location.reload()}
      />
    );
  }

  if (!currentTask) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex flex-col items-center justify-center p-6 bg-blue-50 rounded-lg shadow-lg max-w-4xl mx-auto">
      {/* Header with title and progress */}
      <div className="w-full flex justify-between items-center mb-6">
        <div className="flex items-center">
          <BookOpen className="w-6 h-6 text-blue-600 mr-2" />
          <h2 className="text-2xl font-bold text-blue-700">Semantic Mapping</h2>
        </div>
        <div className="flex items-center">
          <span className="text-blue-700 font-semibold mr-2">
            Score: {correctAnswers}/{correctAnswers + incorrectAnswers}
          </span>
        </div>
      </div>

      {/* Progress bar */}
      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-6">
        <div
          className="bg-blue-600 h-2.5 rounded-full"
          style={{ width: `${progressPercentage}%` }}
        ></div>
      </div>

      {/* Main game area */}
      <div className="w-full mb-6">
        {/* Central word */}
        <div className="flex justify-center mb-8">
          <div className="bg-blue-600 text-white text-2xl font-bold py-4 px-8 rounded-full shadow-lg">
            {currentTask.targetWord}
          </div>
        </div>

        {/* Word definition (if exists) */}
        {currentTask.definition && (
          <div className="text-center mb-6 italic text-gray-600">
            &ldquo;{currentTask.definition}&rdquo;
          </div>
        )}

        {/* Semantic map area */}
        <div
          className="relative bg-white rounded-xl p-8 min-h-[300px] border-2 border-dashed border-blue-300"
          onDragOver={e => e.preventDefault()}
          onDrop={() => handleDrop('map')}
        >
          {/* Central word in the map */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="bg-blue-600 text-white text-xl font-bold py-3 px-6 rounded-full shadow">
              {currentTask.targetWord}
            </div>
          </div>

          {/* Placed words */}
          <div className="flex flex-wrap justify-center gap-4 mb-6">
            {placedWords.map((wordId, index) => {
              const word = currentTask.relatedWords.find(w => w.word === wordId);
              if (!word) return null;

              // Calculate position for words (arranged in a circle)
              const angle = index * (360 / Math.max(placedWords.length, 1)) * (Math.PI / 180);
              const radius = 120; // Circle radius
              const left = 50 + Math.cos(angle) * radius;
              const top = 50 + Math.sin(angle) * radius;

              return (
                <div
                  key={wordId}
                  className={`absolute cursor-pointer px-4 py-2 rounded-lg shadow ${
                    showFeedback
                      ? word.correct
                        ? 'bg-green-100 text-green-800 border border-green-500'
                        : 'bg-red-100 text-red-800 border border-red-500'
                      : 'bg-blue-100 text-blue-800 border border-blue-300'
                  }`}
                  style={{
                    left: `${left}%`,
                    top: `${top}%`,
                    transform: 'translate(-50%, -50%)',
                  }}
                  onClick={() => !showFeedback && handleRemoveItem('word', wordId)}
                >
                  <div className="flex items-center">
                    <span>{word.word}</span>
                    {showFeedback && <span className="ml-2">{word.correct ? '✓' : '✗'}</span>}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {word.type === 'related'
                      ? 'Related'
                      : word.type === 'category'
                        ? 'Category'
                        : word.type === 'synonym'
                          ? 'Synonym'
                          : 'Antonym'}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Placed images */}
          <div className="flex flex-wrap justify-center gap-4">
            {placedImages.map((imageUrl, index) => {
              const image = currentTask.images.find(img => img.url === imageUrl);
              if (!image) return null;

              // Calculate position for images (arranged in a larger circle)
              const angle =
                (index * (360 / Math.max(placedImages.length, 1)) + 30) * (Math.PI / 180);
              const radius = 180; // Larger radius for images
              const left = 50 + Math.cos(angle) * radius;
              const top = 50 + Math.sin(angle) * radius;

              return (
                <div
                  key={imageUrl}
                  className={`absolute cursor-pointer rounded-lg shadow p-1 ${
                    showFeedback
                      ? image.correct
                        ? 'bg-green-100 border border-green-500'
                        : 'bg-red-100 border border-red-500'
                      : 'bg-white border border-blue-300'
                  }`}
                  style={{
                    left: `${left}%`,
                    top: `${top}%`,
                    transform: 'translate(-50%, -50%)',
                  }}
                  onClick={() => !showFeedback && handleRemoveItem('image', imageUrl)}
                >
                  <img src={imageUrl} alt={image.alt} className="w-16 h-16 object-cover rounded" />
                  {showFeedback && (
                    <div
                      className={`absolute -top-2 -right-2 w-6 h-6 flex items-center justify-center rounded-full ${
                        image.correct ? 'bg-green-500' : 'bg-red-500'
                      } text-white text-sm`}
                    >
                      {image.correct ? '✓' : '✗'}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Available draggable elements */}
      <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Words */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-3 text-blue-700">Words</h3>
          <div className="flex flex-wrap gap-2">
            {currentTask.relatedWords.map(word => {
              const isPlaced = placedWords.includes(word.word);

              return (
                <div
                  key={word.word}
                  className={`px-3 py-1 rounded-md cursor-pointer transition-all ${
                    isPlaced ? 'opacity-50 bg-gray-100' : 'bg-blue-100 hover:bg-blue-200'
                  }`}
                  draggable={!isPlaced && !showFeedback}
                  onDragStart={() =>
                    !isPlaced && !showFeedback && handleDragStart('word', word.word)
                  }
                  onClick={() => {
                    if (!isPlaced && !showFeedback) {
                      handleDragStart('word', word.word);
                      handleDrop('map');
                    }
                  }}
                >
                  {word.word}
                </div>
              );
            })}
          </div>
        </div>

        {/* Images */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-3 text-blue-700">Images</h3>
          <div className="flex flex-wrap gap-2">
            {currentTask.images.map(image => {
              const isPlaced = placedImages.includes(image.url);

              return (
                <div
                  key={image.url}
                  className={`p-1 rounded-md cursor-pointer transition-all ${
                    isPlaced
                      ? 'opacity-50 bg-gray-100'
                      : 'bg-white hover:bg-blue-50 border border-blue-200'
                  }`}
                  draggable={!isPlaced && !showFeedback}
                  onDragStart={() =>
                    !isPlaced && !showFeedback && handleDragStart('image', image.url)
                  }
                  onClick={() => {
                    if (!isPlaced && !showFeedback) {
                      handleDragStart('image', image.url);
                      handleDrop('map');
                    }
                  }}
                >
                  <img src={image.url} alt={image.alt} className="w-12 h-12 object-cover rounded" />
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Check answers button */}
      <button
        className={`mt-6 px-6 py-3 rounded-lg text-white font-bold ${
          showFeedback ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'
        }`}
        onClick={handleCheckAnswer}
        disabled={showFeedback || (placedWords.length === 0 && placedImages.length === 0)}
      >
        {showFeedback ? 'Checking...' : 'Check Answer'}
      </button>

      {/* Information message */}
      <div className="mt-6 text-center text-gray-600">
        {!showFeedback ? (
          <p>
            Drag or click words and images to create a semantic map for the word &ldquo;
            {currentTask.targetWord}&rdquo;
          </p>
        ) : (
          <div
            className={`p-4 rounded-lg w-full ${
              allCorrectItemsPlaced() ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
            }`}
          >
            <p className="text-xl font-bold">
              {allCorrectItemsPlaced()
                ? 'Great! You have created a correct semantic map!'
                : 'Good try! Check which elements are correct.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SemanticMappingActivity;
