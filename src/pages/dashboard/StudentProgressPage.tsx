import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ProgressCalendar } from '../../components/students/ProgressCalendar';
import '../../styles/calendar.css';
import btnBack from '../../assets/btn-back.svg';

export const StudentProgressPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(`/dashboard/student/${id}`);
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <button
          onClick={handleBack}
          className="flex gap-2 items-center text-[32px] leading-[32px] tracking-[0%] font-[400] font-fira-sans text-[#5E6C84] hover:opacity-80 transition-opacity cursor-pointer"
        >
          <img src={btnBack} alt="Powrót" />
          Student Progress
        </button>
      </div>

      <ProgressCalendar studentId={id || ''} />
    </div>
  );
};
