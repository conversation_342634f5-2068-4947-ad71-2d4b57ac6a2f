import React, { useEffect, useState } from 'react';
import { Outlet } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { Navbar } from './Navbar';
import { authService } from '../../services/authService.ts';
import { RoleBasedRedirect } from '../auth/RoleBasedRedirect';
import { useUserStore } from '../../store/userStore';
import logger from 'src/utils/logger';

export const DashboardLayout: React.FC = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const { user, setUser, loadAvatar } = useUserStore();

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        if (!user) {
          const data = await authService.getCurrentUser();
          setUser(data);
          logger.log('userData', data);

          if (data && !data.avatar_url) {
            try {
              await loadAvatar();
            } catch (avatarError) {
              logger.error('Error loading avatar:', avatarError);
              window.location.href = '/dashboard/settings/avatar-creator';
            }
          }
        }
      } catch (error) {
        logger.error('Error while fetching user data:', error);
      }
    };

    fetchUserData();
  }, [user, setUser, loadAvatar]);

  return (
    <RoleBasedRedirect>
      <div className="flex h-screen bg-white">
        <Sidebar isCollapsed={isSidebarCollapsed} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar onToggleSidebar={() => setIsSidebarCollapsed(!isSidebarCollapsed)} />
          <main className="flex-1 overflow-auto px-[28px] py-6">
            <Outlet />
          </main>
        </div>
      </div>
    </RoleBasedRedirect>
  );
};
