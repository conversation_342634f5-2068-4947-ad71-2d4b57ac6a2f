import { useState, useEffect, useCallback } from 'react';

interface UseTextAnimationProps {
  fullText: string;
  speed?: number;
  onComplete?: () => void;
}

export const useTextAnimation = ({ fullText, speed = 20, onComplete }: UseTextAnimationProps) => {
  const [displayText, setDisplayText] = useState('');
  const [charIndex, setCharIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const startAnimation = useCallback(() => {
    setDisplayText('');
    setCharIndex(0);
    setIsAnimating(true);
  }, []);

  const resetAnimation = useCallback(() => {
    setDisplayText('');
    setCharIndex(0);
    setIsAnimating(false);
  }, []);

  const completeAnimation = useCallback(() => {
    setDisplayText(fullText);
    setCharIndex(fullText.length);
    setIsAnimating(false);
    if (onComplete) {
      onComplete();
    }
  }, [fullText, onComplete]);

  useEffect(() => {
    if (isAnimating && charIndex < fullText.length) {
      const timer = setTimeout(() => {
        setDisplayText(prev => prev + fullText[charIndex]);
        setCharIndex(prev => prev + 1);
      }, speed);
      return () => clearTimeout(timer);
    } else if (isAnimating && charIndex === fullText.length) {
      setIsAnimating(false);
      if (onComplete) {
        onComplete();
      }
    }
  }, [fullText, charIndex, speed, isAnimating, onComplete]);

  return {
    displayText,
    isAnimating,
    isComplete: charIndex === fullText.length,
    startAnimation,
    resetAnimation,
    completeAnimation,
  };
};

export default useTextAnimation;
