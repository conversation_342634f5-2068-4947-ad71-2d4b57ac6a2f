import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Clock, CheckCircle, PlayCircle, AlertCircle } from 'lucide-react';
import { useTodaysAssessments } from '../../services/queries/assessmentQueries';
import { AssessmentSession } from '../../services/assessmentService';

interface TodaysAssessmentsProps {
  studentId: number;
}

interface AssessmentCardProps {
  session: AssessmentSession;
  onClick: () => void;
}

const AssessmentCard: React.FC<AssessmentCardProps> = ({ session, onClick }) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
        return <PlayCircle className="w-5 h-5 text-blue-500" />;
      case 'planned':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in_progress':
        return 'In Progress';
      case 'planned':
        return 'Ready to Start';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'planned':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTitle = (assessmentId: string) => {
    return assessmentId
      .replace(/-/g, ' ')
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const canStart = session.status === 'planned' || session.status === 'in_progress';

  return (
    <div
      onClick={canStart ? onClick : undefined}
      className={`bg-white rounded-lg border border-gray-200 shadow-sm p-4 transition-all ${
        canStart
          ? 'hover:shadow-md cursor-pointer hover:border-blue-300'
          : 'opacity-75 cursor-not-allowed'
      }`}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {formatTitle(session.assessment_id)}
          </h3>
          <p className="text-sm text-gray-600">Scheduled for {formatDate(session.date)}</p>
        </div>
        <div className="flex items-center space-x-2">{getStatusIcon(session.status)}</div>
      </div>

      <div className="flex items-center justify-between">
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}
        >
          {getStatusText(session.status)}
        </span>
        {canStart && (
          <span className="text-sm text-blue-600 font-medium">
            {session.status === 'planned' ? 'Start Assessment' : 'Continue'}
          </span>
        )}
      </div>
    </div>
  );
};

const TodaysAssessments: React.FC<TodaysAssessmentsProps> = ({ studentId }) => {
  const navigate = useNavigate();
  const { data: todaysData, isLoading, error } = useTodaysAssessments(studentId);

  const handleAssessmentClick = (session: AssessmentSession) => {
    // Navigate to assessment execution page
    navigate(`/dashboard/assessments/${session.assessment_id}/session/${session.session_id}`);
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Today&apos;s Assessments</h2>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Today&apos;s Assessments</h2>
        <div className="text-center py-8">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">Error loading assessments: {(error as Error).message}</p>
        </div>
      </div>
    );
  }

  const sessions = todaysData?.sessions || [];

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Today&apos;s Assessments</h2>
        {sessions.length > 0 && (
          <span className="text-sm text-gray-500">
            {sessions.filter(s => s.status === 'completed').length} of {sessions.length} completed
          </span>
        )}
      </div>

      {sessions.length === 0 ? (
        <div className="text-center py-8">
          <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No assessments scheduled for today.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {sessions.map(session => (
            <AssessmentCard
              key={session.session_id}
              session={session}
              onClick={() => handleAssessmentClick(session)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default TodaysAssessments;
