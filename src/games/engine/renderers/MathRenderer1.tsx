import React, { useState, useEffect } from 'react';
import { GameRendererProps } from '../types';
import { MathTask1 } from '../types';
import VisualObjects from '../../math-game1/VisualObjects';
import { Calculator } from 'lucide-react';

const MathRenderer1: React.FC<GameRendererProps> = props => {
  const {
    task,
    onAnswer,
    showFeedback = false,
    userAnswer = null,
    currentTaskIndex = 0,
    totalTasks = 1,
    stats = {
      correctAnswers: 0,
      incorrectAnswers: 0,
      totalQuestions: 1,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: '',
      studentId: '',
    },
  } = props;

  const mathTask = task as MathTask1;

  const [inputValue, setInputValue] = useState<string>('');
  const [crossedCount, setCrossedCount] = useState(0);

  const isCorrect = userAnswer === mathTask.equation.result;

  useEffect(() => {
    if (showFeedback) {
      const timer = setTimeout(() => {
        setCrossedCount(mathTask.visualObjects.subtract);
      }, 500);
      return () => clearTimeout(timer);
    } else {
      setCrossedCount(0);
      setInputValue('');
    }
  }, [showFeedback, mathTask.visualObjects.subtract]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const numValue = parseInt(inputValue);
    if (!isNaN(numValue)) {
      onAnswer(numValue === mathTask.equation.result, numValue);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="w-full mb-8">
        <div className="flex items-center justify-center mb-4">
          <Calculator className="w-8 h-8 text-purple-600 mr-2" />
          <h1 className="text-3xl md:text-4xl font-bold text-purple-700">Math Quiz 1</h1>
        </div>

        <div className="flex justify-between items-center px-2">
          <div className="text-sm md:text-base text-gray-700 font-medium">
            Question: <span className="text-purple-700">{currentTaskIndex + 1}</span> / {totalTasks}
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-700 rounded-full h-2 w-1/2 mx-2">
            <div
              className="bg-gradient-to-r from-purple-300 to-purple-500 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${((currentTaskIndex + 1) / totalTasks) * 100}%` }}
            ></div>
          </div>

          <div className="text-sm md:text-base text-gray-700 font-medium">
            Score: <span className="text-purple-700">{stats.correctAnswers}</span>
          </div>
        </div>
      </div>

      <div className="w-full p-6 rounded-xl bg-white shadow-lg transition-all duration-500 ease-in-out">
        <div className="mb-8">
          <VisualObjects
            objectType={mathTask.visualObjects.objectType}
            count={mathTask.visualObjects.total}
            crossedOut={crossedCount}
            className="mb-4 py-3"
          />

          <div className="flex items-center justify-center text-3xl font-bold gap-3 py-4">
            <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800">
              {mathTask.equation.num1}
            </div>
            <span>+</span>
            <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800">
              {mathTask.equation.num2}
            </div>
            <span>=</span>
            <div
              className={`flex items-center justify-center w-14 h-14 rounded-full border-2 ${
                showFeedback
                  ? isCorrect
                    ? 'border-green-500 bg-green-100'
                    : 'border-red-500 bg-red-100'
                  : 'border-gray-800'
              } transition-colors duration-300`}
            >
              {showFeedback ? mathTask.equation.result : '?'}
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="flex flex-col items-center">
          <div className="mb-4 w-full max-w-xs">
            <label htmlFor="answer" className="block text-lg font-medium text-gray-700 mb-2">
              Your answer:
            </label>
            <input
              type="number"
              id="answer"
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
              disabled={showFeedback}
              className={`w-full px-4 py-3 text-xl text-center rounded-lg border-2 ${
                showFeedback
                  ? isCorrect
                    ? 'border-green-500 bg-green-50'
                    : 'border-red-500 bg-red-50'
                  : 'border-purple-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-200'
              } outline-none transition-all duration-300`}
              placeholder="?"
              min="0"
              max="20"
            />
          </div>

          {showFeedback ? (
            <div
              className={`text-lg font-medium ${isCorrect ? 'text-green-600' : 'text-red-600'} mb-2 animate-fadeIn`}
            >
              {isCorrect
                ? 'Correct! Great job! 🎉'
                : `Not quite. The answer is ${mathTask.equation.result}`}
            </div>
          ) : (
            <button
              type="submit"
              disabled={inputValue === ''}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg shadow-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Check Answer
            </button>
          )}
        </form>
      </div>
    </div>
  );
};

export default MathRenderer1;
