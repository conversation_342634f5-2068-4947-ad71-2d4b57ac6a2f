import React from 'react';
import { Button } from '../ui/Button';

interface ErrorMessageProps {
  title: string;
  message: string;
  onRetry?: () => void;
  onBack?: () => void;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({ title, message, onRetry, onBack }) => (
  <div className="text-center py-12">
    <h2 className="text-2xl font-bold text-red-600 mb-4">{title}</h2>
    <p className="text-gray-700 mb-6">{message}</p>
    <div className="flex justify-center gap-4">
      {onRetry && (
        <Button onClick={onRetry} variant="secondary">
          Try again
        </Button>
      )}
      {onBack && <Button onClick={onBack}>Return to games</Button>}
    </div>
  </div>
);
