import React, { useState, useEffect } from 'react';
import QuizHeader from './QuizHeader';
import QuizQuestion from './QuizQuestion';
import VisualObjects from './VisualObjects';
import { quizData } from './quizData';
import { MathTask2 } from '../engine/types';
import { GameStats } from '../common/types';
import GameResults from '../common/GameResults';

interface MathQuizProps {
  tasks?: MathTask2[];
  onComplete?: (correct: number, total: number, time: number) => void;
  studentId?: string;
  gameId?: string;
  onFinish?: (result: GameStats & { startTime: number; endTime: number }) => void;
}

const MathQuiz: React.FC<MathQuizProps> = ({
  tasks = quizData,
  onComplete,
  studentId = '',
  gameId = 'math-game2',
  onFinish,
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswer, setUserAnswer] = useState<number | null>(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [correctAnswers, setCorrectAnswers] = useState(0);
  const [incorrectAnswers, setIncorrectAnswers] = useState(0);
  const [crossedCount, setCrossedCount] = useState(0);
  const [gameCompleted, setGameCompleted] = useState(false);
  const [startTime] = useState(Date.now());
  const [endTime, setEndTime] = useState<number | null>(null);

  const currentQuestion = tasks[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === tasks.length - 1;
  const isCorrect = userAnswer === currentQuestion.equation.result;

  useEffect(() => {
    if (gameCompleted && onComplete) {
      const endTimeValue = endTime || Date.now();
      const totalTime = Math.floor((endTimeValue - startTime) / 1000);
      onComplete(correctAnswers, tasks.length, totalTime);
    }
  }, [gameCompleted, correctAnswers, tasks.length, onComplete, startTime, endTime]);

  const handleAnswerSubmit = (answer: number) => {
    setUserAnswer(answer);
    setShowFeedback(true);

    if (answer === currentQuestion.equation.result) {
      setCorrectAnswers(prev => prev + 1);
    } else {
      setIncorrectAnswers(prev => prev + 1);
    }

    setTimeout(() => {
      if (isLastQuestion) {
        const endTimeValue = Date.now();
        setEndTime(endTimeValue);
        setGameCompleted(true);

        if (onFinish) {
          onFinish({
            correctAnswers:
              answer === currentQuestion.equation.result ? correctAnswers + 1 : correctAnswers,
            incorrectAnswers:
              answer === currentQuestion.equation.result ? incorrectAnswers : incorrectAnswers + 1,
            totalQuestions: tasks.length,
            completionPercentage: 100,
            startTime,
            endTime: endTimeValue,
            gameId,
            studentId,
          });
        }
      } else {
        setCurrentQuestionIndex(prev => prev + 1);
        setUserAnswer(null);
        setShowFeedback(false);
        setCrossedCount(0);
      }
    }, 2000);
  };

  const handleObjectClick = (index: number) => {
    if (crossedCount < currentQuestion.visualObjects.total && !showFeedback) {
      setCrossedCount(prevCount => prevCount + 1);
    }
  };

  const progressPercentage = ((currentQuestionIndex + (showFeedback ? 1 : 0)) / tasks.length) * 100;

  if (gameCompleted) {
    const timeInSeconds = Math.floor(((endTime || Date.now()) - startTime) / 1000);
    return (
      <GameResults
        stats={{
          correctAnswers,
          incorrectAnswers,
          totalQuestions: tasks.length,
          completionPercentage: 100,
          startTime,
          endTime,
          timeInSeconds,
          studentId: studentId || '',
          gameId,
          progress: [],
        }}
        onRestart={() => window.location.reload()}
      />
    );
  }

  return (
    <div className="flex flex-col items-center justify-center p-6 bg-green-50 rounded-lg shadow-lg max-w-2xl mx-auto">
      <QuizHeader
        currentQuestion={currentQuestionIndex + 1}
        totalQuestions={tasks.length}
        score={correctAnswers}
      />

      <QuizQuestion
        num1={currentQuestion.equation.num1}
        num2={currentQuestion.equation.num2}
        onSubmit={handleAnswerSubmit}
        showFeedback={showFeedback}
        isCorrect={isCorrect}
        correctAnswer={currentQuestion.equation.result}
      />

      <VisualObjects
        total={currentQuestion.visualObjects.total}
        crossed={crossedCount}
        objectType={currentQuestion.visualObjects.objectType}
        onObjectClick={handleObjectClick}
        disabled={showFeedback}
      />
    </div>
  );
};

export default MathQuiz;
