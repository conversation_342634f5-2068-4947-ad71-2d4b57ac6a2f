import React from 'react';
import {
  AvatarCreator as RPMAvatarCreator,
  AvatarCreatorConfig,
  AvatarExportedEvent,
} from '@readyplayerme/react-avatar-creator';

interface AvatarCreatorProps {
  onAvatarCreated?: (avatarUrl: string) => void;
  onClose?: () => void;
  avatarId?: string | null;
}

export const AvatarCreator: React.FC<AvatarCreatorProps> = ({
  onAvatarCreated,
  onClose,
  avatarId,
}) => {
  const config: AvatarCreatorConfig = {
    clearCache: !avatarId,
    bodyType: 'fullbody',
    quickStart: false,
    language: 'en',
  };

  const style = {
    width: '100%',
    height: '100vh',
    border: 'none',
    minHeight: '600px',
  };

  const handleOnAvatarExported = (event: AvatarExportedEvent) => {
    if (onAvatarCreated) {
      onAvatarCreated(event.data.url);
    }
  };

  return (
    <div className="relative w-full h-full min-h-[600px] bg-white rounded-lg overflow-hidden">
      <div className="flex justify-between items-center p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Avatar Creator</h3>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}
      </div>

      <div className="w-full h-full">
        <RPMAvatarCreator
          subdomain="yubu-yoo7f2"
          config={config}
          style={style}
          onAvatarExported={handleOnAvatarExported}
        />
      </div>
    </div>
  );
};
