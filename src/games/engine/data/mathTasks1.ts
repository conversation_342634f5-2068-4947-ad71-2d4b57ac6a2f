import { MathTask1 } from '../types';

export const mathTasks1: MathTask1[] = [
  {
    type: 'math-game1',
    id: 1,
    visualObjects: {
      total: 2,
      subtract: 0,
      objectType: 'donut',
    },
    equation: {
      num1: 1,
      num2: 1,
      result: 2,
    },
  },
  {
    type: 'math-game1',
    id: 2,
    visualObjects: {
      total: 5,
      subtract: 0,
      objectType: 'flower',
    },
    equation: {
      num1: 2,
      num2: 3,
      result: 5,
    },
  },
  {
    type: 'math-game1',
    id: 3,
    visualObjects: {
      total: 7,
      subtract: 0,
      objectType: 'pencil',
    },
    equation: {
      num1: 3,
      num2: 4,
      result: 7,
    },
  },
  {
    type: 'math-game1',
    id: 4,
    visualObjects: {
      total: 6,
      subtract: 0,
      objectType: 'backpack',
    },
    equation: {
      num1: 2,
      num2: 4,
      result: 6,
    },
  },
  {
    type: 'math-game1',
    id: 5,
    visualObjects: {
      total: 9,
      subtract: 0,
      objectType: 'calculator',
    },
    equation: {
      num1: 4,
      num2: 5,
      result: 9,
    },
  },
  {
    type: 'math-game1',
    id: 6,
    visualObjects: {
      total: 10,
      subtract: 0,
      objectType: 'mushroom',
    },
    equation: {
      num1: 5,
      num2: 5,
      result: 10,
    },
  },
];
