import React from 'react';
import { Student } from '../../services/studentService';
import { Link } from 'react-router-dom';
import dashboardIcon from '../../assets/speed.svg';
import moreIcon from '../../assets/dots.svg';
import { Popover } from '@headlessui/react';

interface StudentsListProps {
  students: {
    id: string;
    name: string;
    yearsActive: number;
    avatar: string;
    additionalInfo?: string;
    gender: string;
  }[];
  onDelete?: (id: number) => Promise<void>;
  onEdit?: (id: number, data: Partial<Student>) => Promise<void>;
}

export const StudentsList: React.FC<StudentsListProps> = ({ students, onDelete, onEdit }) => {
  const handleOptionClick = async (studentId: string, action: 'edit' | 'delete' | 'view') => {
    if (action === 'delete' && onDelete) {
      await onDelete(Number(studentId));
    } else if (action === 'edit' && onEdit) {
      await onEdit(Number(studentId), {});
    } else if (action === 'view') {
      window.location.href = `/dashboard/student/${studentId}`;
    }
  };

  const handleCardClick = (studentId: string, studentName: string) => {
    window.location.href = `/dashboard/student/${studentId}`;
  };

  return (
    <div className="space-y-4 mt-6">
      <div className="grid grid-cols-1 gap-2">
        {students.map(student => (
          <div
            key={student.id}
            className="bg-[#F9F9F9] rounded-lg px-5 py-4 flex items-center gap-[23px] hover:shadow-md transition-shadow relative cursor-pointer"
            onClick={() => handleCardClick(student.id, student.name)}
          >
            <div className="flex items-center gap-[23px]">
              {student.avatar ? (
                <img
                  src={student.avatar}
                  alt={student.name}
                  className="w-12 h-12 rounded-[9.6px]"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-gray-400 text-xl">
                  {student.name[0].toUpperCase()}
                </div>
              )}
              <div>
                <h3 className="text-base font-medium text-gray-900">{student.name}</h3>
                <p className="text-sm text-gray-500">{student.yearsActive} years</p>
              </div>
            </div>
            <div className="flex items-center gap-2 ml-auto" onClick={e => e.stopPropagation()}>
              <Link
                to={`/dashboard/student/${student.id}`}
                state={{ studentName: student.name }}
                className="px-4 py-2 rounded-full text-gray-700 bg-white hover:bg-gray-100 flex items-center gap-2"
              >
                <img src={dashboardIcon} alt="" className="w-5 h-5" />
                <span>Dashboard</span>
              </Link>
              <Popover className="relative">
                {({ open }) => (
                  <>
                    <Popover.Button className="w-10 h-10 flex items-center justify-center text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 focus:outline-none">
                      <img src={moreIcon} alt="More options" className="w-5 h-5" />
                    </Popover.Button>

                    <Popover.Panel className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-10 focus:outline-none">
                      <button
                        onClick={() => handleOptionClick(student.id, 'edit')}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 border-l-2 border-l-transparent hover:border-l-blue-500 focus:outline-none transition-colors cursor-pointer"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleOptionClick(student.id, 'delete')}
                        className="w-full px-4 py-2 text-left text-sm text-red-600 border-l-2 border-l-transparent hover:border-l-red-500 focus:outline-none transition-colors cursor-pointer"
                      >
                        Delete
                      </button>
                      <button
                        onClick={() => handleOptionClick(student.id, 'view')}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 border-l-2 border-l-transparent hover:border-l-blue-500 focus:outline-none transition-colors cursor-pointer"
                      >
                        View
                      </button>
                    </Popover.Panel>
                  </>
                )}
              </Popover>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
