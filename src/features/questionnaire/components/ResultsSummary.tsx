import React from 'react';
import { QuestionnaireResults, Answer } from '../types';
import { questions, scaleOptions } from '../data/questions';
import { RotateCcw, CheckCircle, BarChart2, FileText } from 'lucide-react';

interface Props {
  results: QuestionnaireResults;
  answers: Answer[];
  onReset: () => void;
  onRestart: () => void;
}

export const ResultsSummary: React.FC<Props> = ({ results, answers, onReset, onRestart }) => {
  const scoreColor = (pct: number) =>
    pct <= 30
      ? 'text-green-600 bg-green-50 border-green-200'
      : pct <= 60
        ? 'text-yellow-600 bg-yellow-50 border-yellow-200'
        : 'text-red-600 bg-red-50 border-red-200';

  const label = (val: number) => scaleOptions.find(o => o.value === val)?.label ?? 'N/A';

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6 animate-bounce">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-4">Assessment Complete!</h1>
          <p className="text-xl text-gray-600">
            Thank you for completing the behavioral questionnaire
          </p>
        </div>

        {/* Results Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {/* Score Card */}
          <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
            <div className="flex items-center mb-6">
              <BarChart2 className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-xl font-bold text-gray-800">Results Summary</h2>
            </div>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total Score:</span>
                <span className="text-2xl font-bold text-gray-800">
                  {results.totalScore} / {results.maxScore}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Percentage:</span>
                <span
                  className={`px-4 py-2 rounded-full border font-bold ${scoreColor(results.percentage)}`}
                >
                  {results.percentage}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3 mt-4">
                <div
                  className={`h-3 rounded-full transition-all duration-1000 ease-out ${
                    results.percentage <= 30
                      ? 'bg-green-500'
                      : results.percentage <= 60
                        ? 'bg-yellow-500'
                        : 'bg-red-500'
                  }`}
                  style={{ width: `${results.percentage}%` }}
                />
              </div>
            </div>
          </div>

          {/* Interpretation Card */}
          <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
            <div className="flex items-center mb-6">
              <FileText className="w-6 h-6 text-purple-600 mr-3" />
              <h2 className="text-xl font-bold text-gray-800">Interpretation</h2>
            </div>
            <p className="text-gray-700 text-lg leading-relaxed mb-4">{results.interpretation}</p>
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <h4 className="font-semibold text-blue-800 mb-2">Recommendations</h4>
              <p className="text-blue-700 text-sm leading-relaxed">
                Results provide insight into behavioral patterns. Consider consulting with a
                specialist for detailed interpretation and appropriate interventions.
              </p>
            </div>
          </div>
        </div>

        {/* Detailed Answers */}
        <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100 mb-12">
          <h2 className="text-xl font-bold text-gray-800 mb-6">Detailed Answers</h2>
          <div className="space-y-6">
            {questions.map((q, idx) => {
              const ans = answers.find(a => a.questionId === q.id);
              return (
                <div key={q.id} className="border-b border-gray-100 pb-6 last:border-0">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-semibold text-sm">{idx + 1}</span>
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-800 font-medium mb-2">{q.text}</p>
                      <div className="flex items-center space-x-4">
                        <span className="text-sm text-gray-600">Answer:</span>
                        <span className="px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm font-medium">
                          {ans ? label(ans.value) : 'No answer'}
                        </span>
                        <span className="text-sm text-gray-500">({ans?.value ?? 0}/6)</span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={onRestart}
            className="px-6 py-3 bg-blue-600 text-white font-medium rounded-xl hover:bg-blue-700 transition-all duration-200 transform hover:scale-105"
          >
            Restart
          </button>
          <button
            onClick={onReset}
            className="flex items-center justify-center px-6 py-3 bg-gray-100 text-gray-700 font-medium rounded-xl hover:bg-gray-200 transition-all duration-200 transform hover:scale-105"
          >
            <RotateCcw className="w-5 h-5 mr-2" /> Back to start
          </button>
        </div>
      </div>
    </div>
  );
};
