import React from 'react';
import { GameRendererProps, BaseTask } from '../types';

abstract class BaseRenderer<
  T extends BaseTask = BaseTask,
> extends React.Component<GameRendererProps> {
  abstract renderContent(): React.ReactNode;

  renderFeedback(): React.ReactNode {
    const { showFeedback, userAnswer, task } = this.props;

    if (!showFeedback) return null;

    const isCorrect = this.isAnswerCorrect(userAnswer || null);

    return (
      <div
        className={`mt-4 p-3 rounded-lg ${
          isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}
      >
        {isCorrect ? (
          <p className="font-semibold">Correct! Well done!</p>
        ) : (
          <p className="font-semibold">Incorrect. Try again!</p>
        )}
      </div>
    );
  }

  isAnswerCorrect(userAnswer: string | number | null): boolean {
    return false;
  }

  render() {
    const { className, style } = this.props;

    return (
      <div className={`bg-white p-6 rounded-lg shadow-lg ${className || ''}`} style={style}>
        {this.renderContent()}
        {this.renderFeedback()}
      </div>
    );
  }
}

export default BaseRenderer;
