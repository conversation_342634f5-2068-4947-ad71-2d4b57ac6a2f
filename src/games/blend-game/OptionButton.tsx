import React from 'react';

interface OptionButtonProps {
  option: string;
  onClick: () => void;
  disabled: boolean;
  isSelected?: boolean;
  isCorrect?: boolean;
}

const OptionButton: React.FC<OptionButtonProps> = ({
  option,
  onClick,
  disabled,
  isSelected,
  isCorrect,
}) => {
  let buttonClasses =
    'flex justify-center items-center  text-2xl font-bold rounded-2xl border-4 py-3 px-6 transition-all duration-300 transform hover:scale-105 active:scale-95 shadow-md';

  if (!isSelected) {
    buttonClasses += ' bg-white border-blue-500 text-blue-700 hover:bg-blue-50';
  } else if (isCorrect === true) {
    buttonClasses += ' bg-green-100 border-green-500 text-green-700 animate-pulse';
  } else if (isCorrect === false) {
    buttonClasses += ' bg-red-100 border-red-500 text-red-700';
  } else {
    buttonClasses += ' bg-blue-100 border-blue-600 text-blue-800';
  }

  if (disabled) {
    buttonClasses += ' opacity-70 cursor-default hover:scale-100 active:scale-100';
  }

  return (
    <button className={buttonClasses} onClick={onClick} disabled={disabled}>
      {option}
    </button>
  );
};

export default OptionButton;
