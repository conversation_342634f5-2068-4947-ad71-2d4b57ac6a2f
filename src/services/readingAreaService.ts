import { api } from './api';

// Define API URL - don't include /api since it's already in the baseURL of the axios instance
const _API_URL = '/chat';

// Reading area interface
export interface ReadingArea {
  id: number;
  name: string;
  description: string;
  grade_levels?: string[];
}

/**
 * Extracts the numeric grade from a grade string
 * e.g., "2nd grade" -> "2"
 */
export const extractNumericGrade = (grade: string): string | undefined => {
  // Extract the number from the grade string
  const match = grade.match(/^(\d+)/);
  if (match && match[1]) {
    return match[1];
  } else if (grade.toLowerCase().startsWith('k')) {
    // Handle kindergarten

    return 'K';
  }
  return undefined;
};

/**
 * Fetches reading areas and filters them by grade level
 */
export const getReadingAreas = async (
  grade?: string
): Promise<{ reading_areas: ReadingArea[] }> => {
  try {
    // Extract numeric grade if a grade is provided
    const numericGrade = grade ? extractNumericGrade(grade) : undefined;

    // Fetch from the backend API
    const response = await api.get('/chat/reading_areas');

    interface ReadingAreasResponse {
      reading_areas: Array<{
        id: number;
        name: string;
        description: string;
        grade_levels?: string[];
      }>;
    }

    const data = response.data as ReadingAreasResponse;
    if (data && data.reading_areas) {
      // Ensure we're working with an array
      let areas: ReadingArea[] = Array.isArray(data.reading_areas)
        ? (data.reading_areas as ReadingArea[])
        : Object.values(data.reading_areas).map((area: unknown) => area as ReadingArea);

      // Ensure each area has the required properties
      areas = areas.map((area: ReadingArea) => ({
        id: area.id,
        name: area.name,
        description: area.description,
        // If grade_levels is missing, add default values
        grade_levels: area.grade_levels || ['K', '1', '2', '3', '4', '5', '6', '7', '8'],
      }));

      // Filter by grade if provided
      if (numericGrade) {
        const filteredAreas = areas.filter(
          (area: ReadingArea) => area.grade_levels && area.grade_levels.includes(numericGrade)
        );

        return { reading_areas: filteredAreas };
      }

      return { reading_areas: areas };
    }

    // Return empty array if no data
    return { reading_areas: [] };
  } catch (error) {
    console.error('Error fetching reading areas:', error);
    // Return empty array if API fails
    return { reading_areas: [] };
  }
};

/**
 * Fetches reading area details for a specific area and grade
 */
export const getReadingAreaDetails = async (area: string, grade: string) => {
  try {
    // Extract numeric grade
    const numericGrade = extractNumericGrade(grade);

    // Fetch from the backend API
    const response = await api.get('/chat/reading_area_details');

    interface ReadingAreaDetailsResponse {
      title: string;
      grade_level: string;
      content: {
        learningObjective: string;
        exercises: Array<{ title: string; description: string; instructions: string }>;
      };
    }

    const data = response.data as ReadingAreaDetailsResponse[];
    if (data) {
      const allDetails = data;

      // Find the matching area and grade level
      const areaDetail = allDetails.find((detail: ReadingAreaDetailsResponse) => {
        return detail.title === area && detail.grade_level === numericGrade;
      });

      if (areaDetail) {
        return {
          title: areaDetail.title,
          content: areaDetail.content,
        };
      }
    }

    // Return empty object if no matching data found
    return {};
  } catch (error) {
    console.error('Error fetching reading area details:', error);
    // Return empty object if API fails
    return {};
  }
};

// Export the reading area service
export const readingAreaService = {
  getReadingAreas,
  getReadingAreaDetails,
};
