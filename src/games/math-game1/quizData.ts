import { QuizQuestion } from '@games/common/types';

export const quizQuestions: QuizQuestion[] = [
  {
    id: 1,
    type: 'subtraction',
    visualObjects: {
      total: 2,
      subtract: 0,
      objectType: 'donut',
    },
    equation: {
      num1: 1,
      num2: 1,
      result: 2,
    },
  },
  {
    id: 2,
    type: 'subtraction',
    visualObjects: {
      total: 5,
      subtract: 0,
      objectType: 'flower',
    },
    equation: {
      num1: 2,
      num2: 3,
      result: 5,
    },
  },
  {
    id: 3,
    type: 'subtraction',
    visualObjects: {
      total: 7,
      subtract: 0,
      objectType: 'pencil',
    },
    equation: {
      num1: 3,
      num2: 4,
      result: 7,
    },
  },
  {
    id: 4,
    type: 'subtraction',
    visualObjects: {
      total: 6,
      subtract: 0,
      objectType: 'backpack',
    },
    equation: {
      num1: 2,
      num2: 4,
      result: 6,
    },
  },
  {
    id: 5,
    type: 'subtraction',
    visualObjects: {
      total: 9,
      subtract: 0,
      objectType: 'calculator',
    },
    equation: {
      num1: 4,
      num2: 5,
      result: 9,
    },
  },
  {
    id: 6,
    type: 'subtraction',
    visualObjects: {
      total: 10,
      subtract: 0,
      objectType: 'mushroom',
    },
    equation: {
      num1: 5,
      num2: 5,
      result: 10,
    },
  },
];
