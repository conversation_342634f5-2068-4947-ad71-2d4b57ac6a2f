import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useSessionDetails, activityService } from '../../services/activityService';
import { useTodaysSessions } from '../../services/sessionService';
import { useInterventionStore } from '../../store/interventionStore';
import { useUserStore } from '../../store/userStore';
import GameContent from './GameContent';
import { GameStatsSummary } from './GameStatsSummary';
import { GameStats } from '@games/common/types';

interface GameExerciseLayoutProps {
  gameId?: string;
  studentId?: string;
}

export const GameExerciseLayout: React.FC<GameExerciseLayoutProps> = ({
  gameId: propGameId,
  studentId: propStudentId,
}) => {
  const { activityId } = useParams<{ activityId: string }>();
  const navigate = useNavigate();
  const { user } = useUserStore();
  const interventionStore = useInterventionStore();
  const { data: todaysSessions } = useTodaysSessions();

  // State for timer
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [isGameStarted, setIsGameStarted] = useState(false);

  // State for game completion
  const [showGameSummary, setShowGameSummary] = useState(false);
  const [gameResult, setGameResult] = useState<
    (GameStats & { startTime: number; endTime: number }) | null
  >(null);

  // Znajdź prawdziwy session_id na podstawie game_id z URL
  const actualSessionId = React.useMemo(() => {
    if (!activityId || !todaysSessions) return activityId;

    // Sprawdź czy activityId to już session_id
    const foundBySessionId = activityService.findSessionById(todaysSessions, activityId);
    if (foundBySessionId) {
      return activityId;
    }

    // Jeśli nie, spróbuj znaleźć sesję po game_id
    const allSessions = activityService.flattenSessionsList(todaysSessions);
    const foundByGameId = allSessions.find(session => session.game_id === activityId);
    if (foundByGameId) {
      return foundByGameId.session_id;
    }

    return activityId;
  }, [activityId, todaysSessions]);

  // Get session details using actual session_id
  const { data: sessionDetails, isLoading } = useSessionDetails(actualSessionId || '');

  // Determine gameId and studentId
  const gameId = propGameId || sessionDetails?.game_id || activityId || 'blend-game--quiz';
  // NAPRAWKA: Używaj student.id zamiast user_id
  const studentId =
    propStudentId || user?.student?.id?.toString() || user?.user_id?.toString() || 'test-student';

  // Get game title
  const gameTitle =
    interventionStore.getGameNameByInterventionId(sessionDetails?.intervention_id || '', gameId) ||
    interventionStore.getGameNameById(gameId) ||
    'Blend Activity';

  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleFinishExercise = () => {
    navigate('/dashboard');
  };

  const handleGameFinish = (result: GameStats & { startTime: number; endTime: number }) => {
    // Zapisz wynik gry i pokaż podsumowanie
    setGameResult(result);
    setShowGameSummary(true);
    setIsGameStarted(false);

    // Przekieruj na stronę podsumowania aktywności po 5 sekundach
    setTimeout(() => {
      navigate(`/activities/${actualSessionId}`);
    }, 5000);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-700"></div>
      </div>
    );
  }

  if (!gameId) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-bold text-gray-600 mb-4">Game not found</h2>
          <button
            onClick={() => navigate('/dashboard')}
            className="bg-[#005773] cursor-pointer hover:bg-[#00425a] text-white py-2 px-4 rounded-md"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left side - Game info */}
            <div className="flex items-center">
              <button
                onClick={handleFinishExercise}
                className="w-10 h-10 bg-[#005773] rounded-full flex items-center justify-center mr-4 hover:bg-[#00425a] transition-colors"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 6L6 18M6 6L18 18"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
              <div>
                <div className="text-sm text-gray-600">{gameTitle}</div>
                <div className="text-lg font-semibold text-gray-900">
                  {user?.email?.split('@')[0] || 'Student'}
                </div>
              </div>
            </div>

            {/* Center - Finish button */}
            <button
              onClick={handleFinishExercise}
              className="bg-[#005773] hover:bg-[#00425a] text-white px-6 py-2 rounded-md font-medium transition-colors"
            >
              FINISH THE EXERCISE
            </button>

            {/* Right side - Progress info */}
            <div className="flex items-center space-x-6">
              <div className="text-sm text-gray-600">
                <span className="font-medium">Iteration 1</span>
                <span className="mx-2">-</span>
                <span>1/6</span>
              </div>

              <div className="flex items-center text-sm text-gray-600">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="mr-1"
                >
                  <circle cx="10" cy="10" r="9" stroke="currentColor" strokeWidth="2" />
                  <path
                    d="M10 5V10L13 13"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                {formatTime(timeElapsed)}
              </div>

              <div className="text-sm text-gray-600">
                <span className="mr-2">Jump to</span>
                <button className="p-1 hover:bg-gray-100 rounded">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5 7.5L10 12.5L15 7.5"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex">
            {/* Left sidebar - Instructions */}
            <div className="w-80 mr-8">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">{gameTitle}</h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  Say the name of the picture on the left, then circle a picture on the right that
                  starts with the same sound.
                </p>
              </div>
            </div>

            {/* Main game area */}
            <div className="flex-1">
              <div className="bg-gray-100 rounded-lg shadow-sm min-h-[600px] flex items-center justify-center">
                {showGameSummary && gameResult ? (
                  <div className="w-full h-full p-8">
                    <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
                      <div className="text-center mb-6">
                        <h2 className="text-2xl font-bold text-gray-900 mb-2">Game Completed!</h2>
                        <p className="text-gray-600">Great job! Here are your results:</p>
                      </div>

                      <GameStatsSummary gameId={gameId} studentId={studentId} />

                      <div className="mt-6 text-center">
                        <p className="text-sm text-gray-500 mb-4">
                          Redirecting to activity summary in 5 seconds...
                        </p>
                        <button
                          onClick={() => navigate(`/activities/${actualSessionId}`)}
                          className="bg-[#005773] hover:bg-[#00425a] text-white px-6 py-2 rounded-md font-medium transition-colors"
                        >
                          View Activity Summary
                        </button>
                      </div>
                    </div>
                  </div>
                ) : gameId && studentId ? (
                  <div className="w-full h-full">
                    <GameContent
                      gameId={gameId}
                      studentId={studentId}
                      sessionId={actualSessionId} // Przekaż prawdziwy sessionId
                      sessionDetails={sessionDetails} // Przekaż sessionDetails do wznowienia stanu
                      onFinish={handleGameFinish}
                    />
                  </div>
                ) : (
                  <div className="text-center text-gray-500">
                    <p>Loading game...</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameExerciseLayout;
