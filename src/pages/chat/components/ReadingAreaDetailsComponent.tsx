import React, { useState, useEffect } from 'react';
import { ReadingAreaDetails } from '../types';
import { authService } from '../../../services/authService';
import editIcon from '../../../assets/editPen1.svg';
import deleteIcon from '../../../assets/deleteIcon.svg';
import GameInterventionsComponent from '../../../components/games/GameInterventionsComponent';

interface ReadingAreaDetailsComponentProps {
  details: ReadingAreaDetails;
  onClose: () => void;
  onEdit: () => void;
  selectedGrade: string;
}

export const ReadingAreaDetailsComponent: React.FC<ReadingAreaDetailsComponentProps> = ({
  details,
  onClose,
  onEdit,
  selectedGrade,
}) => {
  const [studentId, setStudentId] = useState<number | null>(null);

  const getAreaId = (areaName: string): string => {
    const areaMap: Record<string, string> = {
      'Phonemic Awareness': 'phonemic-awareness',
      'Phonics & Decoding': 'phonics-decoding',
      'Reading Fluency': 'reading-fluency',
      'Vocabulary Development': 'vocabulary-development',
      'Reading Comprehension': 'reading-comprehension',
      'Orthographic Processing': 'orthographic-processing',
      'Written Expression': 'written-expression',
      'Executive Function & Study Skills': 'executive-function',
    };

    const fallback = areaName
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[&]/g, 'and')
      .replace(/[^a-z0-9-]/g, '');
    return areaMap[areaName] || fallback;
  };

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const userData = await authService.getCurrentUser();
        // Preferuj student.id, fallback do user_id
        const id = userData?.student?.id || userData?.user_id;
        if (id) {
          setStudentId(Number(id));
        }
      } catch (err) {
        console.error('Failed to fetch current user:', err);
      }
    };

    fetchCurrentUser();
  }, []);

  const gradeLevel = selectedGrade || '3rd Grade';

  return (
    <div className="mt-8 border-2 border-gray-200 rounded-xl p-6 w-[1024px] relative bg-white mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-[24px] font-bold text-black">{details.title}</h2>
        <div className="flex gap-3">
          <button
            onClick={onEdit}
            className="flex items-center gap-1 text-gray-700 hover:text-blue-500 transition-colors"
            title="Edit prompt"
          >
            <img src={editIcon} alt="Edit" className="w-5 h-5" />
            <span className="text-[14px]">Edit prompt</span>
          </button>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-red-500 transition-colors"
            title="Delete"
          >
            <img src={deleteIcon} alt="Delete" className="w-6 h-6" />
          </button>
        </div>
      </div>

      <div className="border-b border-gray-200 -mx-6 mb-6"></div>

      <div className="mb-6">
        <h3 className="text-[20px] font-bold text-black mb-2">1. Learning Objective:</h3>
        <p className="text-gray-700">{details.content.learningObjective}</p>
      </div>

      {details.content.exercises.map((exercise, index) => (
        <div key={index} className="mb-8">
          <h3 className="text-[20px] font-bold text-black mb-2">
            Exercise {index + 1}: {exercise.title}
          </h3>
          <p className="text-gray-700">{exercise.description}</p>
          {exercise.instructions && (
            <p className="text-gray-700 mt-2 italic">{exercise.instructions}</p>
          )}
          {exercise.images && exercise.images.length > 0 && (
            <div className="grid grid-cols-2 gap-4 mt-4">
              {exercise.images.map((image, imgIndex) => (
                <div key={imgIndex} className="overflow-hidden">
                  <img
                    src={image}
                    alt={`Exercise ${index + 1} image ${imgIndex + 1}`}
                    className="w-full h-auto object-contain"
                    onError={e => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://via.placeholder.com/400x300?text=Image+Not+Found';
                      target.alt = 'Image not available';
                    }}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      ))}

      <div className="border-t border-gray-200 pt-6 mt-6">
        <GameInterventionsComponent
          areaId={getAreaId(details.title)}
          areaName={details.title}
          studentId={studentId || 0}
          gradeLevel={gradeLevel}
        />
      </div>
    </div>
  );
};

export default ReadingAreaDetailsComponent;
