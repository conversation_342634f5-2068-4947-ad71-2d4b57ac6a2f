import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Settings } from 'lucide-react';
import { testService } from '../../services/testService';
import { Test } from '../../types/test';
import { useUserStore } from '../../store/userStore';
import AssignTestModal from '../../components/tests/AssignTestModal';
import TestsGrid from '../../components/tests/TestsGrid';
import testAssignmentService, {
  TestAssignmentSettings,
} from '../../services/testAssignmentService';
import { toast } from 'sonner';

const TestsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useUserStore();
  const [selectedTest, setSelectedTest] = useState<Test | null>(null);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);

  const { data: tests = [], isPending } = useQuery<Test[]>({
    queryKey: ['tests'],
    queryFn: () => testService.listTests(),
  });

  const handleAssignTest = async (settings: TestAssignmentSettings) => {
    if (!selectedTest) return;

    try {
      // For now, we'll use a default student ID since we don't have student selection
      // In a real implementation, you'd have a student selector
      const studentId = 1; // This should come from student selection

      await testAssignmentService.createAndAssignTest(settings, studentId);
      toast.success('Test assigned successfully!');
      setIsAssignModalOpen(false);
      setSelectedTest(null);
    } catch (error) {
      console.error('Error assigning test:', error);
      toast.error('Failed to assign test. Please try again.');
    }
  };

  const handleSettingsClick = (test: Test) => {
    setSelectedTest(test);
    setIsAssignModalOpen(true);
  };

  if (isPending) return <div className="p-4">Loading tests...</div>;

  // Student view - show assigned tests
  if (user?.role === 'student') {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">My Tests</h1>
        <TestsGrid studentId={user.user_id} />
      </div>
    );
  }

  // Teacher view - show all available tests with assign functionality
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Tests</h1>
      <div className="grid gap-6 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
        {tests.map((test: Test) => (
          <div key={test.id} className="rounded-lg border border-gray-200 shadow-sm p-6 bg-white">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <h3 className="text-lg font-semibold mb-2">{test.title}</h3>
                <p className="text-sm text-gray-500">{test.questions.length} questions</p>
              </div>
              <button
                onClick={() => handleSettingsClick(test)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
                title="Assign Test"
              >
                <Settings size={20} />
              </button>
            </div>

            <div className="flex justify-between items-center">
              <button
                onClick={() => navigate(`/dashboard/tests/${test.id}`)}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Preview Test
              </button>
              <button
                onClick={() => handleSettingsClick(test)}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
              >
                Assign
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Assign Test Modal */}
      {selectedTest && (
        <AssignTestModal
          isOpen={isAssignModalOpen}
          onClose={() => {
            setIsAssignModalOpen(false);
            setSelectedTest(null);
          }}
          onSave={handleAssignTest}
          testId={selectedTest.id}
          testTitle={selectedTest.title}
        />
      )}
    </div>
  );
};

export default TestsPage;
