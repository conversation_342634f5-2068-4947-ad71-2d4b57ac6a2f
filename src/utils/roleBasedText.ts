type TextKey =
  | 'dashboard.title'
  | 'dashboard.students'
  | 'dashboard.reports'
  | 'dashboard.messages'
  | 'dashboard.tests'
  | 'dashboard.keyStats'
  | 'dashboard.todayExercises'
  | 'dashboard.myProgress'
  | 'dashboard.tasks'
  | 'menu.overview'
  | 'menu.students'
  | 'menu.interventions'
  | 'menu.todayExercises'
  | 'menu.myProgress'
  | 'menu.tasks'
  | 'menu.help'
  | 'menu.logout';

type RoleKey = 'teacher' | 'student' | 'parent';

type RoleTexts = {
  [key in TextKey]: {
    teacher: string;
    student: string;
    parent?: string;
  };
};

const texts: RoleTexts = {
  'dashboard.title': {
    teacher: 'Overview',
    student: "Today's Exercises",
    parent: 'Overview',
  },
  'dashboard.students': {
    teacher: 'Students',
    student: 'Exercises',
    parent: 'Students',
  },
  'dashboard.reports': {
    teacher: 'Generated Reports',
    student: 'Completed Exercises',
    parent: 'Reports',
  },
  'dashboard.messages': {
    teacher: 'Messages',
    student: 'Messages',
    parent: 'Messages',
  },
  'dashboard.tests': {
    teacher: 'Tests and Quizzes',
    student: 'Quizzes to Complete',
    parent: 'Tests',
  },
  'dashboard.keyStats': {
    teacher: 'Key Statistics',
    student: 'My Statistics',
    parent: 'Statistics',
  },
  'dashboard.todayExercises': {
    teacher: "Today's Exercises",
    student: "Today's Exercises",
    parent: "Today's Exercises",
  },
  'dashboard.myProgress': {
    teacher: 'Progress',
    student: 'My Progress',
    parent: 'Progress',
  },
  'dashboard.tasks': {
    teacher: 'Tasks',
    student: 'My Tasks',
    parent: 'Tasks',
  },
  'menu.overview': {
    teacher: 'Overview',
    student: 'Overview',
    parent: 'Overview',
  },
  'menu.students': {
    teacher: 'Students',
    student: 'Students',
    parent: 'Students',
  },
  'menu.interventions': {
    teacher: 'Interventions',
    student: 'Interventions',
    parent: 'Interventions',
  },
  'menu.todayExercises': {
    teacher: "Today's Exercises",
    student: "Today's Exercises",
    parent: "Today's Exercises",
  },
  'menu.myProgress': {
    teacher: 'Progress',
    student: 'My Progress',
    parent: 'Progress',
  },
  'menu.tasks': {
    teacher: 'Tasks',
    student: "Today's Tasks",
    parent: 'Tasks',
  },
  'menu.help': {
    teacher: 'Help',
    student: 'Help',
    parent: 'Help',
  },
  'menu.logout': {
    teacher: 'Log out',
    student: 'Log out',
    parent: 'Log out',
  },
};

// Funkcja do mapowania roli z USER_ROLES na klucz w obiekcie texts
const mapRoleToKey = (role: string): RoleKey => {
  switch (role) {
    case 'teacher':
    case 'student':
    case 'parent':
      return role as RoleKey;
    default:
      return 'teacher'; // Domyślnie zwracamy 'teacher' jako fallback
  }
};

export const getRoleBasedText = (key: TextKey, role: string): string => {
  if (!texts[key]) {
    console.warn(`Missing translation for key: ${key}`);
    return key;
  }

  const roleKey = mapRoleToKey(role);

  if (!texts[key][roleKey]) {
    console.warn(`Missing translation for key: ${key} and role: ${role}`);
    return texts[key]['teacher'];
  }

  return texts[key][roleKey];
};
