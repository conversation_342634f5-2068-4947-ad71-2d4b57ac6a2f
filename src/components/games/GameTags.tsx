import React from 'react';
import { Game } from '../../services/gameService';

interface GameTagsProps {
  game?: Game;
  className?: string;
  maxTags?: number;
}

export const GameTags: React.FC<GameTagsProps> = ({ game, className = '', maxTags = 3 }) => {
  // Jeśli nie ma danych o grze, pokaż placeholder
  if (!game) {
    return (
      <div className={`flex flex-wrap gap-2 ${className}`}>
        {[1, 2, 3].map(i => (
          <div key={i} className="h-6 w-16 bg-gray-200 rounded-full animate-pulse" />
        ))}
      </div>
    );
  }

  // Jeśli nie ma kategorii, pokaż domyślny tag
  if (!game.categories || game.categories.length === 0) {
    return (
      <div className={`flex flex-wrap gap-2 ${className}`}>
        <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full font-medium">
          Educational Game
        </span>
      </div>
    );
  }

  const getDifficultyColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'easy':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'hard':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyLabel = (level: string) => {
    switch (level.toLowerCase()) {
      case 'easy':
        return 'Easy';
      case 'medium':
        return 'Medium';
      case 'hard':
        return 'Hard';
      default:
        return level;
    }
  };

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {/* Poziom trudności */}
      {game.difficulty_level && (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(game.difficulty_level)}`}
        >
          {getDifficultyLabel(game.difficulty_level)}
        </span>
      )}

      {/* Kategorie */}
      {game.categories
        .slice(0, maxTags - (game.difficulty_level ? 1 : 0) - (game.target_age_group ? 1 : 0))
        .map((category: string, index: number) => (
          <span
            key={index}
            className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full font-medium"
          >
            {category}
          </span>
        ))}

      {/* Informacja o dodatkowych kategoriach */}
      {game.categories.length >
        maxTags - (game.difficulty_level ? 1 : 0) - (game.target_age_group ? 1 : 0) && (
        <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full font-medium">
          +
          {game.categories.length -
            (maxTags - (game.difficulty_level ? 1 : 0) - (game.target_age_group ? 1 : 0))}{' '}
          more
        </span>
      )}
    </div>
  );
};

export default GameTags;
