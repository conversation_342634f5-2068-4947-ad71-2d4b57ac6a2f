import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { gameService, Game } from '../../services/gameService';
import { authService } from '../../services/authService';
import { GameWrapper } from '../../components/games/GameWrapper';
import { useGameStatsStore } from '../../store/gameStatsStore';
import { gameWebSocket } from '@games/common/websocket';
import { GameStats } from '@games/common/types';
import { CompletionBanner } from '../../components/games/CompletionBanner';
import { Loader, ErrorMessage } from '../../components/common';
import GameTags from '../../components/games/GameTags';
import logger from '../../utils/logger';
import QuestionnairePage from '../../features/questionnaire/QuestionnairePage';
import Model3D from '../../components/3d/Model3D';
import { useUserStore } from '../../store/userStore';

interface FinishedGameResult extends Omit<GameStats, 'endTime'> {
  startTime: number;
  endTime: number;
}

export const GameDetailsPage: React.FC = () => {
  const location = useLocation();
  const isTestsPage = location.pathname.startsWith('/dashboard/tests');

  // Always initialize hooks at the top level (rule-of-hooks)
  const { gameId } = useParams<{ gameId: string }>();
  const navigate = useNavigate();
  const [gameCompleted, setGameCompleted] = useState(false);
  const [feedback, setFeedback] = useState({ show: false, isCorrect: false });
  const { user, loadAvatar } = useUserStore();
  const [customAvatarUrl, setCustomAvatarUrl] = useState<string | null>(null);

  useEffect(() => {
    const loadAvatarData = async () => {
      try {
        await loadAvatar();
        if (user?.avatar_url) {
          setCustomAvatarUrl(user.avatar_url);
        }
      } catch (error) {
        console.error('Avatar loading error:', error);
        window.location.href = '/dashboard/settings/avatar-creator';
      }
    };

    if (!user?.avatar_url) {
      loadAvatarData();
    } else {
      setCustomAvatarUrl(user.avatar_url);
    }
  }, [user?.avatar_url, loadAvatar]);

  const { data: userData } = useQuery({
    queryKey: ['currentUser'],
    queryFn: async () => {
      try {
        const user = await authService.getCurrentUser();
        const id = user?.student?.id || user?.user_id;
        return { userId: id?.toString() || 'guest-user' };
      } catch (_error) {
        return { userId: 'guest-user' };
      }
    },
    staleTime: 1000 * 60 * 5,
  });

  const studentId = userData?.userId || 'guest-user';

  // Initialize WebSocket connection when entering intervention game
  useEffect(() => {
    if (gameId) {
      logger.log('[GameDetailsPage] Initializing WebSocket for game:', gameId);
      gameWebSocket.resetConnectionState();
      gameWebSocket.connect();
    }

    return () => {
      logger.log('[GameDetailsPage] Component unmounting, keeping WebSocket connection');
    };
  }, [gameId]);

  const {
    data: gameDetails,
    isLoading,
    error,
  } = useQuery<Game, Error>({
    queryKey: ['game', gameId],
    queryFn: () => gameService.getGame(gameId || ''),
    enabled: !!gameId,
    staleTime: 1000 * 60 * 60, // 1 godzina
  });

  const handleGameFinish = (result: FinishedGameResult) => {
    const currentTime = Date.now();
    const statsForCompletion: FinishedGameResult = {
      ...result,
      endTime: currentTime,
    };

    // Oblicz poprawność odpowiedzi na podstawie dostępnych danych
    const correctAnswers = 'correctAnswers' in result ? result.correctAnswers : 0;
    const totalQuestions = 'totalQuestions' in result ? result.totalQuestions : 0;
    const timeInSeconds = Math.floor(
      (statsForCompletion.endTime - statsForCompletion.startTime) / 1000
    );

    // Przygotuj dane do wysłania przez WebSocket
    const gameStats: GameStats = {
      studentId: String(studentId),
      gameId: gameDetails?.id || 'unknown',
      correctAnswers,
      incorrectAnswers: totalQuestions - correctAnswers,
      totalQuestions,
      completionPercentage: Math.round((correctAnswers / totalQuestions) * 100),
      startTime: statsForCompletion.startTime,
      endTime: currentTime,
      timeInSeconds: timeInSeconds,
    };

    logger.log('[GameDetailsPage] Wysyłanie statystyk gry przez WebSocket:', gameStats);

    // Wysyłanie danych przez WebSocket
    const sent = gameWebSocket.sendGameStats(gameStats);

    if (sent) {
      logger.log('[GameDetailsPage] Statystyki gry wysłane pomyślnie przez WebSocket');
    } else {
      logger.warn(
        '[GameDetailsPage] Nie udało się wysłać statystyk gry przez WebSocket - zostaną wysłane później'
      );
    }

    // Pokaż komunikat o ukończeniu gry
    setGameCompleted(true);

    // Przekierowanie do strony z grą memory po 7 sekundach
    setTimeout(() => {
      navigate('/dashboard/interventions');
    }, 7000);
  };

  const handleBackToGames = () => {
    const currentStudentId = studentId || 'unknown_student';

    const statsFromStore = useGameStatsStore
      .getState()
      .getLatestGameStats(gameId || '', currentStudentId);

    let statsToSave: GameStats | null = null;

    if (statsFromStore) {
      const durationThisSegmentMs = Date.now() - statsFromStore.startTime;
      const durationThisSegmentSec = Math.max(0, Math.round(durationThisSegmentMs / 1000));

      statsToSave = {
        ...statsFromStore,
        endTime: Date.now(),
        timeInSeconds: durationThisSegmentSec,
        completionPercentage: 50,
      };
    } else if (gameId) {
      const sessionStartTime = Date.now();
      statsToSave = {
        gameId: gameId,
        studentId: currentStudentId,
        correctAnswers: 0,
        incorrectAnswers: 0,
        totalQuestions: 0,
        completionPercentage: 50,
        startTime: sessionStartTime,
        endTime: sessionStartTime,
        timeInSeconds: 0,
      };
    }

    if (statsToSave) {
      useGameStatsStore.getState().addGameStats(statsToSave);
    }

    navigate('/dashboard/interventions');
  };

  const handleGameFeedback = (isCorrect: boolean) => {
    setFeedback({ show: true, isCorrect });
    setTimeout(() => {
      setFeedback({ show: false, isCorrect: false });
    }, 3000);
  };

  // Render path based on tab
  if (isTestsPage) {
    return <QuestionnairePage />;
  }

  // Obsługa stanów ładowania i błędów
  if (isLoading) {
    return <Loader />;
  }

  if (error || !gameDetails) {
    return (
      <div className="text-center py-12">
        <ErrorMessage
          title="Game loading error"
          message={error?.message || 'Failed to load game details.'}
          onRetry={() => window.location.reload()}
          onBack={handleBackToGames}
        />
      </div>
    );
  }

  return (
    <div className="space-y-8 mt-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-[32px] leading-[32px] tracking-[0%] font-[400] font-['Fira_Sans'] text-[#5E6C84]">
          {gameDetails.name}
        </h2>
        <button
          onClick={handleBackToGames}
          className="px-6 py-2.5 bg-green-600 hover:bg-white text-white hover:text-green-600 border-2 border-green-600 rounded-lg transition-colors duration-200 font-medium"
        >
          Back to interventions
        </button>
      </div>

      {/* Opis gry i tagi */}
      <div className="mb-6">
        <p className="text-gray-600 mb-3">{gameDetails.description}</p>
        <GameTags game={gameDetails} maxTags={5} />
      </div>

      <div className="bg-white rounded-xl shadow-md p-4 mx-auto">
        {gameId && studentId && !isLoading && !error && gameDetails && (
          <div className="mt-8">
            <div className="flex mx-auto w-full">
              <GameWrapper
                gameId={gameId}
                studentId={studentId}
                onFinish={handleGameFinish}
                onFeedback={handleGameFeedback}
              />
              <Model3D
                showFeedback={feedback.show}
                isCorrect={feedback.isCorrect}
                useCustomAvatar={customAvatarUrl !== null}
                avatarUrl={customAvatarUrl || undefined}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GameDetailsPage;
