import React from 'react';
import detailSettingsIcon from '../../assets/detailSettings.svg';

interface GameSettingsButtonProps {
  onClick: () => void;
  className?: string;
  color?: string;
}

export const GameSettingsButton: React.FC<GameSettingsButtonProps> = ({
  onClick,
  className = '',
  color = '#005773',
}) => {
  return (
    <button
      className={`flex items-center justify-center text-white py-2 px-4 rounded-md ${className}`}
      style={{ backgroundColor: color }}
      onClick={onClick}
    >
      <span className="mr-2">
        <img src={detailSettingsIcon} alt="Settings" width="20" height="20" />
      </span>
      Settings
    </button>
  );
};

export default GameSettingsButton;
