import React from 'react';

interface FloatingInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  id: string;
  label: string;
  mb?: string;
}

export const FloatingInput: React.FC<FloatingInputProps> = ({
  id,
  label,
  mb = 'mb-5',
  ...props
}) => {
  return (
    <div className={`relative z-0 w-full group ${mb}`}>
      <input
        id={id}
        className="floating-input block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-primaryBtn peer"
        placeholder=" "
        {...props}
      />
      <label
        htmlFor={id}
        className="peer-focus:font-medium absolute text-sm text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:start-0 peer-focus:text-primaryBtn peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:z-[-10] peer-focus:scale-75 peer-focus:z-[10] peer-focus:-translate-y-6 peer-valid:z-[10]"
      >
        {label}
      </label>
    </div>
  );
};
