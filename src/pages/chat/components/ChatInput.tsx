import React from 'react';
import { EntryType } from '../types';

interface ChatInputProps {
  message: string;
  setMessage: (message: string) => void;
  handleKeyPress: (e: React.KeyboardEvent) => void;
  handleSendMessage: () => void;
  loading: boolean;
  hasReceivedEnoughContent: boolean;
  selectedEntry: EntryType | null;
  isWsConnected: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  message,
  setMessage,
  handleKeyPress,
  handleSendMessage,
  loading,
  hasReceivedEnoughContent,
  selectedEntry,
  isWsConnected,
}) => {
  return (
    <div className="border-t border-gray-200 pt-4">
      <div className="flex flex-col gap-2">
        {selectedEntry === 'survey' && (
          <>
            {isWsConnected ? (
              <div className="text-xs text-green-600 flex items-center mb-1">
                <span className="inline-block w-2 h-2 bg-green-600 rounded-full mr-1"></span>
                Connected to chat server
              </div>
            ) : (
              <div className="text-xs text-orange-600 flex items-center mb-1">
                <span className="inline-block w-2 h-2 bg-orange-600 rounded-full mr-1 animate-pulse"></span>
                Reconnecting to chat server...
              </div>
            )}
          </>
        )}

        <div className="flex gap-4">
          <input
            type="text"
            value={message}
            onChange={e => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={
              selectedEntry === 'survey'
                ? 'Ask a question about reading skills'
                : 'Write a question or select a predefined prompt'
            }
            className="flex-1 rounded-lg border border-gray-200 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primaryBtn focus:border-transparent"
            disabled={
              (loading && !hasReceivedEnoughContent) ||
              (selectedEntry === 'survey' && !isWsConnected)
            }
          />
          <button
            onClick={handleSendMessage}
            disabled={
              // Wyłączamy przycisk tylko gdy nie ma tekstu lub gdy jest to ankieta i nie ma połączenia WebSocket
              !message.trim() || (selectedEntry === 'survey' && !isWsConnected)
            }
            className="px-6 py-2 bg-[#005773] text-white font-semibold rounded-lg hover:bg-[#004a61] transition-colors disabled:bg-[#005773] disabled:opacity-90 disabled:cursor-not-allowed flex items-center"
          >
            {loading ? (
              <span className="flex items-center">
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Sending...
              </span>
            ) : (
              <span className="flex items-center">
                Send
                <svg
                  className="ml-1 h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M14 5l7 7m0 0l-7 7m7-7H3"
                  ></path>
                </svg>
              </span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatInput;
