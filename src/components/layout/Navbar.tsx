import React from 'react';
import { UserMenu } from './UserMenu';
import sidebarIcon from '../../assets/sidebarIcon.svg';
import notificationIcon from '../../assets/bell-red-dot.svg';
import { useUserStore } from '../../store/userStore';

interface NavbarProps {
  onToggleSidebar: () => void;
}

export const Navbar: React.FC<NavbarProps> = ({ onToggleSidebar }) => {
  const { user } = useUserStore();

  // If no user data is available, don't render user-dependent content
  if (!user) {
    return (
      <div className="h-16 bg-white flex items-center justify-between px-2">
        <button className="w-6 h-6 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
          <img
            src={sidebarIcon}
            alt="Toggle sidebar"
            className="w-6 h-6"
            onClick={onToggleSidebar}
          />
        </button>
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  return (
    <div className="h-16 bg-white flex items-center justify-between px-2">
      <button className="w-6 h-6 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
        <img src={sidebarIcon} alt="Toggle sidebar" className="w-6 h-6" onClick={onToggleSidebar} />
      </button>

      <div className="flex items-center gap-4">
        <button className="relative text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
          <div className="absolute top-0 right-[1px] w-[10px] h-[10px] bg-red-700 rounded-full text-[10px] text-white flex items-center justify-center"></div>
          <img src={notificationIcon} alt="Notifications" className="w-6 h-6" />
        </button>
        <UserMenu />
      </div>
    </div>
  );
};
