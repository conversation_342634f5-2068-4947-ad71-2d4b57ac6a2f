import React, { useEffect } from 'react';

interface FeedbackProps {
  isCorrect: boolean;
  onComplete: () => void;
}

const Feedback: React.FC<FeedbackProps> = ({ isCorrect, onComplete }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onComplete();
    }, 1500);

    return () => {
      clearTimeout(timer);
    };
  }, [onComplete]);

  return (
    <div className="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
      <div
        className={`flex items-center justify-center w-20 h-20 rounded-full
          ${isCorrect ? 'bg-green-500' : 'bg-red-500'}
          animate-bounce shadow-lg`}
      >
        {isCorrect ? (
          <span className="text-white text-3xl font-bold">✓</span>
        ) : (
          <span className="text-white text-3xl font-bold">✗</span>
        )}
      </div>
      {isCorrect && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-yellow-300 rounded-full animate-ping"></div>
          <div className="absolute top-1/3 right-1/3 w-3 h-3 bg-blue-300 rounded-full animate-ping delay-100"></div>
          <div className="absolute bottom-1/3 left-1/3 w-4 h-4 bg-green-300 rounded-full animate-ping delay-200"></div>
          <div className="absolute bottom-1/4 right-1/4 w-2 h-2 bg-purple-300 rounded-full animate-ping delay-300"></div>
        </div>
      )}
    </div>
  );
};

export default Feedback;
