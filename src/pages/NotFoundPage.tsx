import React, { useState } from 'react';
import { Sidebar } from '../components/layout/Sidebar';
import { Navbar } from '../components/layout/Navbar';
import { authService } from '../services/authService';
import workInProgress from '../assets/404-work-in-progress.svg';
import Logo from '../components/Logo/Logo';

const NotFoundPage: React.FC = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const isAuthenticated = authService.checkToken();

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 relative">
        <div className="absolute top-8 left-8">
          <Logo />
        </div>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <img src={workInProgress} alt="404 work in progress" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-white">
      <Sidebar isCollapsed={isSidebarCollapsed} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar onToggleSidebar={() => setIsSidebarCollapsed(!isSidebarCollapsed)} />
        <main className="flex-1 overflow-auto flex items-center justify-center">
          <div className="text-center">
            <img src={workInProgress} alt="404 work in progress" />
          </div>
        </main>
      </div>
    </div>
  );
};

export default NotFoundPage;
