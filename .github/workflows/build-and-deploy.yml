name: Build and Deploy to S3

on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: [self-hosted, yubu]

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build
        env:
          NODE_ENV: production

      - name: Deploy to S3
        run: |
          aws s3 sync dist/ s3://yubu-dev-frontend --delete

      - name: CloudFront Invalidation
        run: |
          aws cloudfront create-invalidation --distribution-id E1O5NATU0QCIKG --paths "/*"
