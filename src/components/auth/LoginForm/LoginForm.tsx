import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Link } from 'react-router-dom';
import logo from '../../../assets/logo.svg';

const loginSchema = z.object({
  email: z.string().email('Wprowadź poprawny adres email'),
  password: z.string().min(8, 'Hasło musi mieć co najmniej 8 znaków'),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

interface LoginFormProps {
  onSubmit: (data: LoginFormData) => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSubmit }) => {
  const { register, handleSubmit } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      rememberMe: false,
    },
  });

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <img className="mx-auto h-12 w-auto" src={logo} alt="Logo" />
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
        </div>
        <form onSubmit={handleSubmit(onSubmit)} className="mt-8 space-y-6" action="#" method="POST">
          <input type="hidden" name="remember" value="true" />
          <div className="rounded-md sxhadow-sm -space-y-px">
            <div>
              <label htmlFor="email-address" className="sr-only">
                Email address
              </label>
              <input
                id="email-address"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn focus:z-10 sm:text-sm"
                placeholder="Email address"
                {...register('email')}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <input
                id="password"
                type="password"
                autoComplete="current-password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn focus:z-10 sm:text-sm"
                placeholder="Password"
                {...register('password')}
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                type="checkbox"
                className="h-4 w-4 text-primaryBtn focus:ring-primaryBtn border-gray-300 rounded"
                {...register('rememberMe')}
              />
              <label
                htmlFor="remember-me"
                className="ml-2 block text-[12.8px] leading-[22.53px] tracking-[0px] font-[400] font-['Fira_Sans'] text-[#000000]"
              >
                Remember me
              </label>
            </div>

            <div className="text-[12.8px] leading-[22.53px] tracking-[0px] font-[500] font-['Fira_Sans']">
              <a href="#" className="text-[#424242] hover:text-gray-700">
                Forgot your password?
              </a>
            </div>
          </div>

          <div>
            <button
              type="submit"
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primaryBtn hover:bg-primaryBtn focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primaryBtn"
            >
              CONTINUE
            </button>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              New User?{' '}
              <Link to="/register" className="font-medium text-black underline">
                SIGN UP HERE
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginForm;
