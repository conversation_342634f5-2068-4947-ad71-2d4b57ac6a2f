import { Test } from '../types/test';
import { api } from './api';
import testAssignmentService from './testAssignmentService';

interface RawAssessment {
  assessment_id: string;
  title: string;
  scale: { label: string; value: number }[];
  questions: { id: number; question: string }[];
}

const map = (r: RawAssessment): Test => ({
  id: r.assessment_id,
  title: r.title,
  scale: r.scale,
  questions: r.questions,
});

export const testService = {
  /**
   * Return only assessments assigned to a given student (student_assessments).
   */
  async listAssignedTests(studentId: number): Promise<Test[]> {
    const assignments = await testAssignmentService.getScheduledAssessments(studentId);
    if (!assignments || assignments.length === 0) return [];
    // fetch full catalog once and build map
    const allTests = await this.listTests();
    const byId = new Map(allTests.map(t => [t.id, t]));
    return assignments.map(a => byId.get(a.assessment_id)).filter(Boolean) as Test[];
  },
  async listTests(): Promise<Test[]> {
    const { data } = await api.get<RawAssessment[]>('/assessments/all_assessments');
    return data.map(map);
  },

  async getTestById(id: string): Promise<Test> {
    const tests = await this.listTests();
    const test = tests.find(t => t.id === id);
    if (!test) throw new Error('Assessment not found');
    return test;
  },

  // TODO: implement POST /assessments/answers
  async submitAnswers(): Promise<void> {
    return Promise.resolve();
  },
};
