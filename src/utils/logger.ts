const DEBUG = process.env.NODE_ENV !== 'production';

export const logger = {
  log: (...args: unknown[]): void => {
    if (DEBUG) {
      console.log(...args);
    }
  },

  error: (...args: unknown[]): void => {
    if (DEBUG) {
      console.error(...args);
    }
  },

  warn: (...args: unknown[]): void => {
    if (DEBUG) {
      console.warn(...args);
    }
  },

  info: (...args: unknown[]): void => {
    if (DEBUG) {
      console.info(...args);
    }
  },

  debug: (...args: unknown[]): void => {
    if (DEBUG) {
      console.debug(...args);
    }
  },
};

export default logger;
