import React from 'react';
import arrow_right from '../../assets/arrow_right.svg';

interface DashboardButtonProps {
  icon: string;
  text: string;
  url: string;
  onClick?: () => void;
}

export const DashboardButton: React.FC<DashboardButtonProps> = ({ icon, text, onClick }) => {
  return (
    <div
      className="block rounded-xl p-7 hover:shadow-md transition-shadow bg-[#F9F9F9] cursor-pointer"
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-8 h-8 rounded-full flex items-center justify-center">
            <span>
              <img src={icon} alt={text} />
            </span>
          </div>
          <span className="text-base font-medium text-[#424242]">{text}</span>
        </div>
        <span>
          <img src={arrow_right} alt="" />
        </span>
      </div>
    </div>
  );
};
