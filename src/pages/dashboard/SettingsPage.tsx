import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUserStore } from '../../store/userStore';
import { authService } from '../../services/authService';
import { AvatarDisplay } from '../../components/common/AvatarDisplay';
import { QuickAvatarDownload } from '../../components/common/QuickAvatarDownload';

interface ChangePasswordForm {
  old_password: string;
  new_password: string;
  confirm_password: string;
}

export const SettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, updateAvatar, loadAvatar } = useUserStore();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showAvatarDownloader, setShowAvatarDownloader] = useState(false);
  const [avatarLoading, setAvatarLoading] = useState(false);
  const [form, setForm] = useState<ChangePasswordForm>({
    old_password: '',
    new_password: '',
    confirm_password: '',
  });

  useEffect(() => {
    const loadUserAvatar = async () => {
      try {
        await loadAvatar();
      } catch (error) {
        console.error('Error loading avatar:', error);
        navigate('/dashboard/settings/avatar-creator');
      }
    };

    if (user && !user.avatar_url) {
      loadUserAvatar();
    }
  }, [user, loadAvatar, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    if (form.new_password !== form.confirm_password) {
      setError('New passwords do not match');
      return;
    }

    try {
      await authService.changePassword({
        old_password: form.old_password,
        new_password: form.new_password,
      });
      setSuccess('Password changed successfully');
      setForm({
        old_password: '',
        new_password: '',
        confirm_password: '',
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while changing password');
    }
  };

  const handleOpenAvatarCreator = () => {
    navigate('/dashboard/settings/avatar-creator');
  };

  const handleOpenAvatarDownloader = () => {
    setShowAvatarDownloader(true);
    setError(null);
    setSuccess(null);
  };

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Settings</h1>

      {error && (
        <div
          className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {success && (
        <div
          className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <span className="block sm:inline">{success}</span>
        </div>
      )}

      {/* Avatar Section */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Avatar</h2>
        <div className="flex items-center space-x-6">
          <AvatarDisplay avatarUrl={user?.avatar_url} size="xl" onClick={handleOpenAvatarCreator} />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Your 3D Avatar</h3>
            <p className="text-sm text-gray-500 mb-4">
              Click on the avatar to create or change your 3D avatar.
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Change Password</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="old_password" className="block text-sm font-medium text-gray-700">
              Current Password
            </label>
            <input
              type="password"
              id="old_password"
              name="old_password"
              value={form.old_password}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="new_password" className="block text-sm font-medium text-gray-700">
              New Password
            </label>
            <input
              type="password"
              id="new_password"
              name="new_password"
              value={form.new_password}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn sm:text-sm"
            />
            <p className="mt-1 text-sm text-gray-500">
              Password must contain at least 8 characters, including uppercase, lowercase, number
              and special character.
            </p>
          </div>

          <div>
            <label htmlFor="confirm_password" className="block text-sm font-medium text-gray-700">
              Confirm New Password
            </label>
            <input
              type="password"
              id="confirm_password"
              name="confirm_password"
              value={form.confirm_password}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn sm:text-sm"
            />
          </div>

          <div>
            <button
              type="submit"
              className="mx-auto flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-black bg-primaryBtn hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primaryBtn"
            >
              Change Password
            </button>
          </div>
        </form>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Preferences</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Email notifications</h3>
              <p className="text-sm text-gray-500">Receive notifications about important events</p>
            </div>
            <button
              type="button"
              className="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primaryBtn bg-gray-200"
              role="switch"
              aria-checked="false"
            >
              <span className="sr-only">Enable notifications</span>
              <span
                aria-hidden="true"
                className="pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 translate-x-0"
              ></span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
