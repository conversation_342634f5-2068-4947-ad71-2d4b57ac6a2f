import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON>Lef<PERSON>, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import {
  useSessionDetails,
  useSubmitAssessmentWithStudent,
} from '../../services/queries/assessmentQueries';
import { AssessmentAnswer } from '../../services/assessmentService';

interface AssessmentExecutionProps {
  studentId?: number;
}

const AssessmentExecution: React.FC<AssessmentExecutionProps> = ({ studentId }) => {
  const { assessmentId, sessionId } = useParams<{ assessmentId: string; sessionId: string }>();
  const navigate = useNavigate();

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<number, number>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: sessionDetails, isLoading, error } = useSessionDetails(sessionId || '');

  const submitMutation = useSubmitAssessmentWithStudent();

  // Extract student ID from session details if not provided
  const effectiveStudentId = studentId || sessionDetails?.student.id;

  const questions = sessionDetails?.assessment.questions || [];
  const currentQuestion = Array.isArray(questions) ? questions[currentQuestionIndex] : null;

  // Get scale options from API response
  const getScaleOptions = (scale: unknown) => {
    // If scale is already an array of objects with label and value, use it directly
    if (
      Array.isArray(scale) &&
      scale.length > 0 &&
      typeof scale[0] === 'object' &&
      scale[0] !== null &&
      'label' in scale[0] &&
      'value' in scale[0] &&
      typeof scale[0].value === 'number'
    ) {
      return scale as Array<{ label: string; value: number }>;
    }

    // If scale is a string, parse it
    if (typeof scale === 'string') {
      if (scale.includes('–') || scale.includes('-')) {
        const parts = scale.split(/[–-]/);
        if (parts.length === 2) {
          const min = parseInt(parts[0].trim());
          const max = parseInt(parts[1].trim());
          const options = [];
          for (let i = min; i <= max; i++) {
            options.push({ label: i.toString(), value: i });
          }
          return options;
        }
      }
    }

    // Default scale if parsing fails
    return [
      { label: '1', value: 1 },
      { label: '2', value: 2 },
      { label: '3', value: 3 },
      { label: '4', value: 4 },
      { label: '5', value: 5 },
    ];
  };

  const scaleOptions = sessionDetails ? getScaleOptions(sessionDetails.assessment.scale) : [];

  const handleAnswerSelect = (questionIndex: number, value: number) => {
    setAnswers(prev => ({
      ...prev,
      [questionIndex]: value,
    }));
  };

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    if (!sessionId || !effectiveStudentId) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert answers to API format
      const assessmentAnswers: AssessmentAnswer[] = Object.entries(answers).map(
        ([questionIndex, value]) => ({
          question_id: parseInt(questionIndex) + 1, // API expects 1-based question IDs
          value,
        })
      );

      await submitMutation.mutateAsync({
        request: {
          session_id: sessionId,
          answers: assessmentAnswers,
        },
        studentId: effectiveStudentId,
      });

      // Navigate to results/summary page
      navigate(`/dashboard/assessments/${assessmentId}/results/${sessionId}`, {
        state: { completed: true },
      });
    } catch {
      // Error submitting assessment - handled by mutation
    } finally {
      setIsSubmitting(false);
    }
  };

  const isCurrentQuestionAnswered = Object.prototype.hasOwnProperty.call(
    answers,
    currentQuestionIndex
  );
  const allQuestionsAnswered =
    questions.length > 0 && Object.keys(answers).length === questions.length;
  const canSubmit = allQuestionsAnswered && !isSubmitting;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !sessionDetails) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Assessment</h2>
          <p className="text-gray-600 mb-4">
            {error ? (error as Error).message : 'Assessment not found'}
          </p>
          <button
            onClick={() => navigate(-1)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">No Questions Available</h2>
          <p className="text-gray-600 mb-4">This assessment doesn&apos;t have any questions.</p>
          <button
            onClick={() => navigate(-1)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {sessionDetails.assessment.title}
              </h1>
              <p className="text-gray-600 mt-1">
                Question {currentQuestionIndex + 1} of {questions.length}
              </p>
            </div>
            <button
              onClick={() => navigate(-1)}
              className="flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="w-5 h-5 mr-1" />
              Back
            </button>
          </div>

          {/* Progress bar */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Question */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            {typeof currentQuestion === 'string' ? currentQuestion : currentQuestion?.question}
          </h2>

          {/* Scale options */}
          <div className="space-y-3">
            <p className="text-sm text-gray-600 mb-4">Please select your response:</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-3">
              {scaleOptions.map(option => (
                <button
                  key={option.value}
                  onClick={() => handleAnswerSelect(currentQuestionIndex, option.value)}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    answers[currentQuestionIndex] === option.value
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300 text-gray-700'
                  }`}
                >
                  <div className="text-lg font-semibold">{option.label}</div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <button
            onClick={handlePrevious}
            disabled={currentQuestionIndex === 0}
            className={`flex items-center px-4 py-2 rounded-lg ${
              currentQuestionIndex === 0
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Previous
          </button>

          <div className="flex items-center space-x-2">
            {questions.map((_, index) => (
              <div
                key={index}
                className={`w-3 h-3 rounded-full ${
                  Object.prototype.hasOwnProperty.call(answers, index)
                    ? 'bg-green-500'
                    : index === currentQuestionIndex
                      ? 'bg-blue-500'
                      : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          {currentQuestionIndex === questions.length - 1 ? (
            <button
              onClick={handleSubmit}
              disabled={!canSubmit}
              className={`flex items-center px-6 py-2 rounded-lg ${
                canSubmit
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Submitting...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Submit Assessment
                </>
              )}
            </button>
          ) : (
            <button
              onClick={handleNext}
              disabled={!isCurrentQuestionAnswered}
              className={`flex items-center px-4 py-2 rounded-lg ${
                isCurrentQuestionAnswered
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            >
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssessmentExecution;
