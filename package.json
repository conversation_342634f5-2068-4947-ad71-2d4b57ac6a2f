{"name": "yubu", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ignore-pattern 'scripts/**'", "lint:fix": "eslint . --ignore-pattern 'scripts/**' --fix", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install", "preview": "vite preview"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroui/tooltip": "^2.2.16", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@readyplayerme/react-avatar-creator": "^0.5.0", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.76.2", "@tanstack/react-query-devtools": "^5.79.0", "@types/react-router-dom": "^5.3.3", "@types/three": "^0.176.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.509.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-doc-viewer": "^0.1.14", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-query-kit": "^3.3.1", "react-router-dom": "^7.5.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "three": "^0.176.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.1.4", "@types/axios": "^0.14.4", "@types/node": "^22.15.16", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "husky": "^9.0.11", "lint-staged": "^15.5.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.9", "typescript": "~5.7.2", "typescript-eslint": "^8.32.0", "vite": "^6.2.0"}}