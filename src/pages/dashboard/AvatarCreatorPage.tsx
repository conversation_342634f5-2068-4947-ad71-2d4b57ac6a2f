import React from 'react';
import { useNavigate } from 'react-router-dom';
import { AvatarCreator } from '../../components/common/AvatarCreator';
import { useUserStore } from '../../store/userStore';
import { authService } from '../../services/authService';
import { getAvatarId } from '../../utils/avatarUtils';

export const AvatarCreatorPage: React.FC = () => {
  const navigate = useNavigate();
  const { updateAvatar, user } = useUserStore();

  const handleAvatarCreated = async (avatarUrl: string) => {
    try {
      await authService.updateAvatar(avatarUrl);
      updateAvatar(avatarUrl);
      navigate('/dashboard/settings');
    } catch (err) {
      console.error('Błąd podczas aktualizacji awatara:', err);
    }
  };

  const avatarId = user?.avatar_url ? getAvatarId(user.avatar_url) : null;

  return (
    <div>
      <AvatarCreator
        onAvatarCreated={handleAvatarCreated}
        onClose={() => navigate('/dashboard/settings')}
        avatarId={avatarId}
      />
    </div>
  );
};
