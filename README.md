# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Adding New Games to Yubu

This guide explains how to add new games to the Yubu platform. Follow these steps to integrate your game seamlessly with the existing system.

### Prerequisites

- Basic knowledge of React and TypeScript
- Access to the `yubu-games` package
- Understanding of the Yubu game architecture

### Step 1: Create a Game Component

Create a new game component in the `yubu-games/src` directory. Your game component should accept props for tasks, student ID, game ID, and completion callbacks.

### Step 2: Register the Game in GameRegistry

Open `src/components/games/GameRegistry.ts` and add your new game to the registry. Specify the game ID, component, and default props.

### Step 3: Create Game Data

If your game requires specific data, add it to the `yubu-games/src/data.ts` file. Follow the existing data structure for consistency.

### Step 4: WebSocket Integration

Your game will automatically integrate with the WebSocket system through the `GameContent` component. When a user completes the game, statistics will be sent via WebSocket and stored in the Zustand store.

### Step 5: Styling

Use Tailwind CSS classes for styling your game. The Tailwind configuration is already set up to handle files from `yubu-games`.

### Step 6: Testing

Test your new game by navigating to `/dashboard/games/your-game-id`. The game should load automatically and display correctly.

### Game Status

Games can have two statuses:

1. **Completed**: Displayed with a green bar when the user has completed the entire game (completionPercentage = 100%)
2. **In Progress**: Displayed with a blue bar and the progressGames.svg icon when the user has started but not completed the game (completionPercentage < 100%)

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type-aware lint rules:

```js
export default tseslint.config({
  extends: [
    // Remove ...tseslint.configs.recommended and replace with this
    ...tseslint.configs.recommendedTypeChecked,
    // Alternatively, use this for stricter rules
    ...tseslint.configs.strictTypeChecked,
    // Optionally, add this for stylistic rules
    ...tseslint.configs.stylisticTypeChecked,
  ],
  languageOptions: {
    // other options...
    parserOptions: {
      project: ['./tsconfig.node.json', './tsconfig.app.json'],
      tsconfigRootDir: import.meta.dirname,
    },
  },
});
```

You can also install [eslint-plugin-react-x](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-x) and [eslint-plugin-react-dom](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-dom) for React-specific lint rules:

```js
// eslint.config.js
import reactX from 'eslint-plugin-react-x';
import reactDom from 'eslint-plugin-react-dom';

export default tseslint.config({
  plugins: {
    // Add the react-x and react-dom plugins
    'react-x': reactX,
    'react-dom': reactDom,
  },
  rules: {
    // other rules...
    // Enable its recommended typescript rules
    ...reactX.configs['recommended-typescript'].rules,
    ...reactDom.configs.recommended.rules,
  },
});
```
