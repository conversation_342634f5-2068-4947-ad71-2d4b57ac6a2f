import React, { useState, useEffect } from 'react';
import { GameRendererProps } from '../types';
import { WordBuildingTask } from '../types';
import { Shuffle, HelpCircle } from 'lucide-react';
import { gameWebSocket } from '../../common/websocket';
import logger from '../../../utils/logger';

interface LetterTile {
  id: string;
  letter: string;
  isUsed: boolean;
}

interface WordSlot {
  id: string;
  letter: string | null;
  tileId: string | null;
}

const WordBuildingRenderer: React.FC<GameRendererProps> = props => {
  const {
    task,
    onAnswer,
    showFeedback = false,
    currentTaskIndex = 0,
    totalTasks = 1,
    sessionId,
    stats = {
      correctAnswers: 0,
      incorrectAnswers: 0,
      totalQuestions: 1,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: '',
      studentId: '',
    },
  } = props;

  const wordBuildingTask = task as unknown as WordBuildingTask;
  const targetWord = wordBuildingTask.targetWord.toUpperCase();

  const [letterTiles, setLetterTiles] = useState<LetterTile[]>([]);
  const [wordSlots, setWordSlots] = useState<WordSlot[]>([]);
  const [currentWord, setCurrentWord] = useState<string>('');
  const [showHint, setShowHint] = useState<boolean>(false);
  const [attempts, setAttempts] = useState<number>(0);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);

  // Inicjalizacja gry
  useEffect(() => {
    initializeGame();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [wordBuildingTask]);

  // Inicjalizacja gry - przygotowanie kafelków z literami i slotów na słowo
  const initializeGame = () => {
    // Przygotowanie slotów na litery docelowego słowa
    const slots: WordSlot[] = targetWord.split('').map((letter, index) => ({
      id: `slot-${index}`,
      letter: null,
      tileId: null,
    }));
    setWordSlots(slots);

    // Przygotowanie kafelków z dostępnymi literami
    const availableLetters =
      wordBuildingTask.availableLetters ||
      shuffleArray([...targetWord, ...generateRandomLetters(targetWord.length)]);

    const tiles: LetterTile[] = availableLetters.map((letter, index) => ({
      id: `tile-${index}`,
      letter: letter.toUpperCase(),
      isUsed: false,
    }));

    setLetterTiles(shuffleArray(tiles));
    setCurrentWord('');
    setIsCorrect(null);
    setAttempts(0);
    setShowHint(false);
  };

  // Generowanie losowych liter do uzupełnienia zestawu
  const generateRandomLetters = (count: number): string[] => {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const randomLetters: string[] = [];

    for (let i = 0; i < count; i++) {
      const randomIndex = Math.floor(Math.random() * alphabet.length);
      randomLetters.push(alphabet[randomIndex]);
    }

    return randomLetters;
  };

  // Tasowanie tablicy (Fisher-Yates shuffle)
  const shuffleArray = <T,>(array: T[]): T[] => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  };

  // Obsługa kliknięcia na kafelek z literą
  const handleTileClick = (tileId: string) => {
    if (showFeedback) return;

    const tile = letterTiles.find(t => t.id === tileId);
    if (!tile || tile.isUsed) return;

    // Znajdź pierwszy wolny slot
    const emptySlotIndex = wordSlots.findIndex(slot => slot.letter === null);
    if (emptySlotIndex === -1) return;

    // Aktualizuj stan kafelków - oznacz wybrany jako użyty
    const updatedTiles = letterTiles.map(t => (t.id === tileId ? { ...t, isUsed: true } : t));
    setLetterTiles(updatedTiles);

    // Aktualizuj stan slotów - umieść literę w wolnym slocie
    const updatedSlots = [...wordSlots];
    updatedSlots[emptySlotIndex] = {
      ...updatedSlots[emptySlotIndex],
      letter: tile.letter,
      tileId: tileId,
    };
    setWordSlots(updatedSlots);

    // Aktualizuj bieżące słowo
    const newWord = updatedSlots.map(slot => slot.letter || '').join('');
    setCurrentWord(newWord);

    // Sprawdź, czy słowo jest kompletne
    if (newWord.length === targetWord.length) {
      checkAnswer(newWord);
    }
  };

  // Obsługa kliknięcia na slot z literą (usunięcie litery)
  const handleSlotClick = (slotId: string) => {
    if (showFeedback) return;

    const slot = wordSlots.find(s => s.id === slotId);
    if (!slot || slot.letter === null) return;

    // Aktualizuj stan kafelków - oznacz odpowiedni kafelek jako dostępny
    const updatedTiles = letterTiles.map(t => (t.id === slot.tileId ? { ...t, isUsed: false } : t));
    setLetterTiles(updatedTiles);

    // Aktualizuj stan slotów - usuń literę z klikniętego slotu
    const updatedSlots = wordSlots.map(s =>
      s.id === slotId ? { ...s, letter: null, tileId: null } : s
    );
    setWordSlots(updatedSlots);

    // Aktualizuj bieżące słowo
    const newWord = updatedSlots.map(slot => slot.letter || '').join('');
    setCurrentWord(newWord);
  };

  // Sprawdzenie odpowiedzi
  const checkAnswer = (word: string) => {
    const correct = word === targetWord;
    setIsCorrect(correct);
    setAttempts(attempts + 1);

    // Aktualizacja statystyk
    if (stats) {
      const currentTime = Date.now();

      // Inicjalizuj progress jeśli nie istnieje
      if (!stats.progress) {
        stats.progress = [];
      }

      // Dodaj nowy krok
      stats.progress.push({
        step: attempts + 1,
        correct: correct,
      });

      const updatedStats = {
        ...stats,
        sessionId: sessionId,
        correctAnswers: stats.correctAnswers + (correct ? 1 : 0),
        incorrectAnswers: stats.incorrectAnswers + (correct ? 0 : 1),
        completionPercentage: correct ? 100 : Math.min(90, attempts * 10),
        endTime: currentTime,
        timeInSeconds: Math.round((currentTime - stats.startTime) / 1000),
        progress: [...stats.progress],
      };

      // Wysyłanie statystyk przez WebSocket
      try {
        gameWebSocket.sendGameStats(updatedStats);
        logger.log('[WordBuildingRenderer] Stats sent:', updatedStats);
      } catch (error) {
        logger.error('[WordBuildingRenderer] Failed to send stats:', error);
      }
    }

    // Wywołanie funkcji zwrotnej z informacją o odpowiedzi
    setTimeout(() => {
      onAnswer(correct, word);
    }, 1000);
  };

  // Resetowanie gry
  const resetGame = () => {
    initializeGame();
  };

  // Pokazanie podpowiedzi
  const toggleHint = () => {
    setShowHint(!showHint);
  };

  // Tasowanie kafelków z literami
  const shuffleTiles = () => {
    const unusedTiles = letterTiles.filter(tile => !tile.isUsed);
    const usedTiles = letterTiles.filter(tile => tile.isUsed);

    setLetterTiles([...usedTiles, ...shuffleArray(unusedTiles)]);
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="w-full mb-8">
        <div className="flex items-center justify-between mb-4">
          <div className="text-lg font-semibold">
            Zadanie {currentTaskIndex + 1} z {totalTasks}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={shuffleTiles}
              className="p-2 bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200 transition-colors"
              title="Tasuj litery"
            >
              <Shuffle size={20} />
            </button>
            <button
              onClick={toggleHint}
              className="p-2 bg-yellow-100 text-yellow-600 rounded-full hover:bg-yellow-200 transition-colors"
              title="Pokaż podpowiedź"
            >
              <HelpCircle size={20} />
            </button>
          </div>
        </div>

        {/* Obraz podpowiedź */}
        <div className="flex justify-center mb-6">
          <div className="relative w-48 h-48 bg-gray-100 rounded-lg overflow-hidden">
            <img
              src={wordBuildingTask.image}
              alt="Podpowiedź obrazkowa"
              className="w-full h-full object-contain"
            />
          </div>
        </div>

        {/* Podpowiedź tekstowa */}
        {showHint && wordBuildingTask.hint && (
          <div className="mb-4 p-3 bg-yellow-50 text-yellow-800 rounded-lg">
            <p className="font-medium">Podpowiedź: {wordBuildingTask.hint}</p>
          </div>
        )}

        {/* Sloty na litery */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-2">
            {wordSlots.map(slot => (
              <div
                key={slot.id}
                onClick={() => handleSlotClick(slot.id)}
                className={`
                  w-12 h-12 flex items-center justify-center 
                  border-2 ${slot.letter ? 'border-blue-500' : 'border-gray-300'} 
                  rounded-lg text-xl font-bold cursor-pointer
                  ${isCorrect === false ? 'animate-shake' : ''}
                  ${isCorrect === true ? 'bg-green-100 border-green-500' : ''}
                `}
              >
                {slot.letter}
              </div>
            ))}
          </div>
        </div>

        {/* Kafelki z literami */}
        <div className="flex flex-wrap justify-center gap-2 mb-6">
          {letterTiles.map(tile => (
            <div
              key={tile.id}
              onClick={() => handleTileClick(tile.id)}
              className={`
                w-10 h-10 flex items-center justify-center 
                bg-blue-100 text-blue-800 rounded-lg 
                text-lg font-bold cursor-pointer transition-all
                ${tile.isUsed ? 'opacity-0 pointer-events-none' : 'hover:bg-blue-200'}
              `}
            >
              {tile.letter}
            </div>
          ))}
        </div>

        {/* Informacja zwrotna */}
        {isCorrect !== null && !showFeedback && (
          <div
            className={`mt-4 p-3 rounded-lg ${
              isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}
          >
            {isCorrect ? (
              <p className="font-semibold">Brawo! Ułożyłeś poprawne słowo!</p>
            ) : (
              <p className="font-semibold">
                Niestety, to nie jest poprawne słowo. Spróbuj ponownie!
              </p>
            )}
          </div>
        )}

        {/* Przycisk resetowania */}
        {!isCorrect && attempts > 0 && !showFeedback && (
          <div className="flex justify-center mt-4">
            <button
              onClick={resetGame}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Spróbuj ponownie
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default WordBuildingRenderer;
