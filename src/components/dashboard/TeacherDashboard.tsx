import React from 'react';
import { StatCard } from './StatCard';
import { StudentsTable } from './StudentsTable';
import { useTeacherDashboardData } from '../../services/queries/studentQueries';

export const TeacherDashboard: React.FC = () => {
  const { globalStats, myStudentsStats, isLoading, isError, error } = useTeacherDashboardData();
  if (isError) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-semibold">Overview</h1>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">
            Error loading dashboard data: {error?.message || 'Unknown error'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold">Overview</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Students"
          value={myStudentsStats?.total_students ?? 0}
          bgColor="bg-gray-100"
        />
        <StatCard title="Teachers" value={globalStats?.total_teachers ?? 0} bgColor="bg-gray-100" />
        <StatCard
          title="Interventions"
          value={globalStats?.total_interventions ?? 0}
          subtitle={`Reading: ${globalStats?.total_reading_interventions ?? 0}, Math: ${globalStats?.total_math_interventions ?? 0}`}
          bgColor="bg-gray-100"
        />
      </div>

      <StudentsTable students={myStudentsStats?.students || []} loading={isLoading} />
    </div>
  );
};
