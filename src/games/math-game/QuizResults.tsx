import React from 'react';
import { QuizState } from '../common/types';

interface QuizResultsProps {
  quizState: QuizState;
  onRestart: () => void;
  score?: number;
  totalQuestions?: number;
  timeInSeconds?: number;
}

const QuizResults: React.FC<QuizResultsProps> = ({
  quizState,
  onRestart,
  score: propScore,
  totalQuestions: propTotalQuestions,
  timeInSeconds,
}) => {
  const score = propScore !== undefined ? propScore : quizState.score;
  const totalQuestions =
    propTotalQuestions !== undefined ? propTotalQuestions : quizState.questions.length;
  const percentage = Math.round((score / totalQuestions) * 100);

  const getFeedbackMessage = () => {
    if (percentage === 100) return "Perfect score! You're a math genius! 🏆";
    if (percentage >= 80) return "Excellent work! You're really good at this! 🌟";
    if (percentage >= 60) return 'Good job! Keep practicing to improve! 👍';
    if (percentage >= 40) return 'Nice effort! More practice will help you get better! 📚';
    return "Keep practicing! You'll get better with time! 💪";
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-3xl shadow-lg">
      <div className="text-center py-12">
        <h2 className="text-4xl font-bold text-green-600 mb-6">
          <span className="inline-block mr-2">✓</span>Completed
        </h2>

        <div className="mb-8 text-center">
          <div className="inline-block w-32 h-32 rounded-full flex items-center justify-center border-8 border-green-200 mb-4">
            <span className="text-4xl font-bold text-green-600">{percentage}%</span>
          </div>
          <p className="text-2xl text-gray-700 mb-4">
            You&apos;ve completed all the math questions!
          </p>
          <div className="mb-8 text-lg text-gray-600">
            <p className="mb-2">
              Score: {score}/{totalQuestions}
            </p>
            {timeInSeconds !== undefined && <p className="mb-2">Time: {timeInSeconds} seconds</p>}
            <p className="mb-2">Accuracy: {percentage}%</p>
          </div>
          <p className="text-lg text-gray-600">{getFeedbackMessage()}</p>
        </div>
      </div>

      <div className="space-y-4 mb-8">
        <h3 className="text-xl font-semibold text-gray-800 mb-3">Question Summary:</h3>
        {quizState.questions.map((question, index) => {
          const userAnswer = quizState.userAnswers[index];
          const isCorrect = userAnswer === question.equation.result;

          return (
            <div
              key={question.id || index}
              className={`p-4 rounded-lg ${isCorrect ? 'bg-green-50 border-l-4 border-green-400' : 'bg-red-50 border-l-4 border-red-400'}`}
            >
              <p className="font-medium">
                Question {index + 1}: {question.equation.num1} - {question.equation.num2} = ?
              </p>
              <div className="flex justify-between text-sm mt-1">
                <span>Your answer: {userAnswer !== null ? userAnswer : 'No answer'}</span>
                <span className="font-medium">Correct answer: {question.equation.result}</span>
              </div>
            </div>
          );
        })}
      </div>

      <div className="text-center">
        <button
          onClick={onRestart}
          className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-full transition duration-300 text-lg"
        >
          Play Again
        </button>
      </div>
    </div>
  );
};

export default QuizResults;
