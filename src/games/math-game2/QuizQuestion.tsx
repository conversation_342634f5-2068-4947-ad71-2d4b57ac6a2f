import React, { useState, useEffect } from 'react';

interface QuizQuestionProps {
  num1: number;
  num2: number;
  onSubmit: (answer: number) => void;
  showFeedback: boolean;
  isCorrect: boolean;
  correctAnswer: number;
}

type Operation = '+' | '-' | '×' | '÷';

const QuizQuestion: React.FC<QuizQuestionProps> = ({
  num1,
  num2,
  onSubmit,
  showFeedback,
  isCorrect,
  correctAnswer,
}) => {
  const [selectedOperation, setSelectedOperation] = useState<Operation | null>(null);
  const [animateIn, setAnimateIn] = useState(false);

  const operations: Operation[] = ['+', '-', '×', '÷'];

  const calculateResult = (operation: Operation): number => {
    switch (operation) {
      case '+':
        return num1 + num2;
      case '-':
        return num1 - num2;
      case '×':
        return num1 * num2;
      case '÷':
        return Math.floor(num1 / num2); // Zaokrąglamy w dół dla uproszczenia
      default:
        return 0;
    }
  };

  const handleOperationSelect = (operation: Operation) => {
    setSelectedOperation(operation);
    const result = calculateResult(operation);
    onSubmit(result);
  };

  useEffect(() => {
    setAnimateIn(true);
    setSelectedOperation(null);
    return () => setAnimateIn(false);
  }, [num1, num2]);

  // Znajdź poprawną operację
  const correctOperation = operations.find(op => calculateResult(op) === correctAnswer) || '×';

  return (
    <div
      className={`w-full p-6 rounded-xl bg-white shadow-lg transition-all duration-500 ease-in-out ${
        animateIn ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
      }`}
    >
      <div className="mb-8">
        <div className="flex items-center justify-center text-3xl font-bold gap-3 py-4">
          <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800">
            {num1}
          </div>
          {showFeedback ? (
            <div
              className={`flex items-center justify-center w-14 h-14 rounded-full border-2 ${
                isCorrect ? 'border-green-500 bg-green-100' : 'border-red-500 bg-red-100'
              } transition-colors duration-300`}
            >
              {correctOperation}
            </div>
          ) : (
            <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800 text-center">
              ?
            </div>
          )}
          <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800">
            {num2}
          </div>
          <span>=</span>
          <div className="flex items-center justify-center w-14 h-14 rounded-full border-2 border-gray-800">
            {correctAnswer}
          </div>
        </div>
      </div>

      <div className="flex flex-col items-center">
        {/* Przyciski operacji */}
        {!showFeedback && (
          <div className="flex justify-center gap-4 mb-6">
            {operations.map(operation => (
              <button
                key={operation}
                onClick={() => handleOperationSelect(operation)}
                disabled={showFeedback}
                className={`w-14 h-14 rounded-full text-2xl font-bold border-2 ${
                  selectedOperation === operation
                    ? 'bg-blue-500 text-white border-blue-600'
                    : 'bg-white border-gray-300 hover:bg-gray-100'
                } transition-colors`}
              >
                {operation}
              </button>
            ))}
          </div>
        )}

        {showFeedback && (
          <div
            className={`mt-4 px-6 py-3 rounded-full text-white font-bold ${
              isCorrect ? 'bg-green-500' : 'bg-red-500'
            }`}
          >
            {isCorrect ? 'Correct!' : `Incorrect! The answer is ${correctOperation}`}
          </div>
        )}
      </div>
    </div>
  );
};

export default QuizQuestion;
