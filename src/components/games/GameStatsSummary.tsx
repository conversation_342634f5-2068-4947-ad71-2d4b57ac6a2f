import React from 'react';
import { useGameStatsStore } from '../../store/gameStatsStore';
import checkIcon from '../../assets/checkGames.svg';
import clockIcon from '../../assets/clockGames.svg';

interface GameStatsSummaryProps {
  gameId: string;
  studentId: string;
}

export const GameStatsSummary: React.FC<GameStatsSummaryProps> = ({ gameId, studentId }) => {
  const latestStats = useGameStatsStore(state => state.getLatestGameStats(gameId, studentId));

  if (!latestStats) {
    return null;
  }

  const isCompleted =
    latestStats.completionPercentage === 100 ||
    (gameId === 'math-game' && latestStats.completionPercentage >= 83);

  const formattedDate = latestStats.endTime
    ? new Date(latestStats.endTime).toLocaleDateString()
    : '';

  const formattedTime = latestStats.endTime
    ? new Date(latestStats.endTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    : '';

  const timeSpent = (() => {
    if (latestStats.endTime && latestStats.startTime) {
      return Math.round((latestStats.endTime - latestStats.startTime) / 1000);
    } else if (latestStats.timeInSeconds) {
      return latestStats.timeInSeconds;
    } else if (latestStats.startTime) {
      return Math.round((Date.now() - latestStats.startTime) / 1000);
    }
    return 0;
  })();

  return (
    <div className="mt-2 text-sm text-gray-700 flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center">
          <img src={checkIcon} alt="Check" className="w-5 h-5 mr-2" />
          <span>Score:</span>
        </div>
        <span className="font-medium">
          {latestStats.correctAnswers}/{latestStats.totalQuestions}
        </span>
      </div>

      {(() => {
        if (isCompleted) {
          return (
            <div className="flex items-center justify-between mb-2">
              <span>Completed:</span>
              <span className="font-medium">
                {formattedDate} {formattedTime}
              </span>
            </div>
          );
        } else {
          return (
            <div className="flex items-center justify-between mb-2">
              <span>Status:</span>
              <span className="font-medium text-blue-500">In Progress</span>
            </div>
          );
        }
      })()}

      {timeSpent > 0 && (
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <img src={clockIcon} alt="Clock" className="w-5 h-5 mr-2" />
            <span>Time:</span>
          </div>
          <span className="font-medium">
            {Math.floor(timeSpent / 60)}:{(timeSpent % 60).toString().padStart(2, '0')}
          </span>
        </div>
      )}

      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
        <div
          className="bg-green-500 h-2 rounded-full"
          style={{
            width: isCompleted ? '100%' : `${latestStats.completionPercentage}%`,
          }}
        ></div>
      </div>
    </div>
  );
};

export default GameStatsSummary;
