import { Question } from '../types';

export const questions: Question[] = [
  {
    id: 1,
    text: 'Would the behaviour occur continuously, over and over, if this person was left alone for long periods of time? (e.g., several hours)',
  },
  {
    id: 2,
    text: 'Does the behaviour occur following a request to perform a difficult task?',
  },
  {
    id: 3,
    text: 'Does the behaviour seem to occur in response to your talking to other persons in the room?',
  },
  {
    id: 4,
    text: "Does the behaviour ever occur to get a toy, food, or activity that this person has been told they can't have?",
  },
  {
    id: 5,
    text: 'Would the behaviour occur repeatedly, in the same way, for very long periods of time, if no one was around? (e.g., rocking back and forth for over an hour)',
  },
  {
    id: 6,
    text: 'Does the behaviour occur when any request is made of this person?',
  },
  {
    id: 7,
    text: 'Does the behaviour occur whenever you stop attending to this person?',
  },
  {
    id: 8,
    text: 'Does the behaviour occur when you take away a favourite toy, food or activity?',
  },
  {
    id: 9,
    text: 'Does it appear to you that this person enjoys performing the behaviour? (It feels, tastes, looks, smells and/or sounds pleasing)',
  },
  {
    id: 10,
    text: 'Does this person seem to do the behaviour to upset or annoy you when you are trying to get them to do what you ask?',
  },
  {
    id: 11,
    text: "Does this person seem to do the behaviour to upset or annoy you when you are not paying attention to them? (e.g., you're in a separate room)",
  },
  {
    id: 12,
    text: 'Does the behaviour stop occurring shortly after you give this person the toy, food or activity they have requested?',
  },
  {
    id: 13,
    text: 'When the behaviour is occurring, does this person seem calm and unaware of anything else going on around them?',
  },
  {
    id: 14,
    text: 'Does the behaviour stop occurring shortly after (1–5 minutes) you stop working or making demands of this person?',
  },
  {
    id: 15,
    text: 'Does this person seem to do the behaviour to get you to spend some time with them?',
  },
  {
    id: 16,
    text: "Does the behaviour seem to occur when this person has been told they can't do something they wanted to do?",
  },
];

export const scaleOptions = [
  { value: 0, label: 'Never' },
  { value: 1, label: 'Almost Never' },
  { value: 2, label: 'Seldom' },
  { value: 3, label: 'Half the time' },
  { value: 4, label: 'Usually' },
  { value: 5, label: 'Almost Always' },
  { value: 6, label: 'Always' },
];
