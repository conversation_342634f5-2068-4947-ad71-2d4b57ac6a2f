import { authService } from './authService';
import logger from '../utils/logger';

const API_URL = import.meta.env.VITE_API_URL || 'https://api.dev.yubu.ai';

// Types for assessment system
export interface AssessmentQuestion {
  id: number;
  question: string;
}

export interface AssessmentScale {
  label: string;
  value: number;
}

export interface Assessment {
  assessment_id: string;
  title: string;
  scale: string;
  questions: string[] | AssessmentQuestion[];
}

export interface AssessmentSession {
  session_id: string;
  assigned_assessment_id: string;
  assessment_id: string;
  date: string;
  status: 'planned' | 'in_progress' | 'completed';
}

export interface TodaysAssessmentSession {
  student_id: number;
  today: string;
  sessions: AssessmentSession[];
}

export interface SessionAssessmentDetails {
  student: {
    id: number;
    name: string;
  };
  session_date: string;
  assessment: Assessment;
  assigned_assessment_id: string;
  repeat_every: string;
  start_date: string;
  end_date?: string;
}

export interface AssessmentAnswer {
  question_id: number;
  value: number;
}

export interface SubmitAssessmentRequest {
  session_id: string;
  answers: AssessmentAnswer[];
}

export interface SubmitAssessmentResponse {
  message: string;
  status: string;
}

export interface AssessmentResult {
  session_id: string;
  assessment_id: string;
  student_id: number;
  completion_date: string;
  answers: AssessmentAnswer[];
  status: 'completed' | 'in_progress';
}

const assessmentService = {
  // Get today's assessment sessions for student
  async getTodaysAssessments(studentId: number): Promise<TodaysAssessmentSession> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/assessments/todays_sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ student_id: studentId }),
      });

      if (!response.ok) {
        await response.json().catch(() => ({}));
        throw new Error(
          `Failed to get today's assessments: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      logger.info("Today's assessments fetched successfully:", data);
      return data;
    } catch (error) {
      logger.error("Error getting today's assessments:", error);
      throw error;
    }
  },

  // Get session assessment details
  async getSessionDetails(sessionId: string): Promise<SessionAssessmentDetails> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/assessments/get_session_assessment_details`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ session_id: sessionId }),
      });

      if (!response.ok) {
        await response.json().catch(() => ({}));
        throw new Error(`Failed to get session details: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      logger.info('Session details fetched successfully:', data);
      return data;
    } catch (error) {
      logger.error('Error getting session details:', error);
      throw error;
    }
  },

  // Submit assessment answers
  async submitAnswers(request: SubmitAssessmentRequest): Promise<SubmitAssessmentResponse> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/submit_assessment_answers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        await response.json().catch(() => ({}));
        throw new Error(`Failed to submit answers: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      logger.info('Assessment answers submitted successfully:', data);
      return data;
    } catch (error) {
      logger.error('Error submitting assessment answers:', error);
      throw error;
    }
  },

  // Get all assessments catalog
  async getAllAssessments(): Promise<Assessment[]> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/assessments/all_assessments`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        await response.json().catch(() => ({}));
        throw new Error(`Failed to get assessments: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      logger.info('All assessments fetched successfully:', data);
      return data;
    } catch (error) {
      logger.error('Error getting all assessments:', error);
      throw error;
    }
  },
};

export default assessmentService;
