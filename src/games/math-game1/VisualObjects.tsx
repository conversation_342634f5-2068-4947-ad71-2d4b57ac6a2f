import React from 'react';
import {
  Calculator,
  BookCopy,
  ChromeIcon as Mu<PERSON>room<PERSON><PERSON>,
  Coffee,
  Flower,
  Pencil,
} from 'lucide-react';

interface VisualObjectsProps {
  objectType: 'mushroom' | 'donut' | 'flower' | 'pencil' | 'backpack' | 'calculator';
  count: number;
  crossedOut?: number;
  className?: string;
}

const VisualObjects: React.FC<VisualObjectsProps> = ({
  objectType,
  count,
  crossedOut = 0,
  className = '',
}) => {
  const getObjectIcon = (type: string, index: number) => {
    const isCrossedOut = index >= count - crossedOut;
    const baseClasses = `w-10 h-10 md:w-12 md:h-12 transition-all duration-300 ${isCrossedOut ? 'opacity-30' : 'opacity-100'}`;
    const lineThrough = isCrossedOut
      ? 'relative after:content-[""] after:absolute after:top-1/2 after:left-0 after:w-full after:h-0.5 after:bg-red-500 after:transform after:-rotate-45'
      : '';

    switch (type) {
      case 'mushroom':
        return (
          <div className={`${baseClasses} ${lineThrough} text-purple-500`}>
            <MushroomIcon className="w-full h-full" />
          </div>
        );
      case 'donut':
        return (
          <div className={`${baseClasses} ${lineThrough} text-amber-500`}>
            <Coffee className="w-full h-full" />
          </div>
        );
      case 'flower':
        return (
          <div className={`${baseClasses} ${lineThrough} text-pink-500`}>
            <Flower className="w-full h-full" />
          </div>
        );
      case 'pencil':
        return (
          <div className={`${baseClasses} ${lineThrough} text-blue-500`}>
            <Pencil className="w-full h-full" />
          </div>
        );
      case 'backpack':
        return (
          <div className={`${baseClasses} ${lineThrough} text-red-500`}>
            <BookCopy className="w-full h-full" />
          </div>
        );
      case 'calculator':
        return (
          <div className={`${baseClasses} ${lineThrough} text-slate-700`}>
            <Calculator className="w-full h-full" />
          </div>
        );
      default:
        return <div className={`${baseClasses} ${lineThrough}`}>?</div>;
    }
  };

  return (
    <div className={`flex flex-wrap gap-3 justify-center ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="transition-all duration-300 ease-in-out hover:scale-110">
          {getObjectIcon(objectType, index)}
        </div>
      ))}
    </div>
  );
};

export default VisualObjects;
