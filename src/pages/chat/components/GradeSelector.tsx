import React from 'react';
import { gradeLevels } from '../types';
import arrowsUpDown from '../../../assets/arrowsUpDown.svg';
import stars from '../../../assets/stars.svg';

interface GradeSelectorProps {
  selectedGrade: string;
  isDropdownOpen: boolean;
  displayGradeText: string;
  gradeSelectorText: string;
  setIsDropdownOpen: (isOpen: boolean) => void;
  handleGradeSelect: (grade: string) => void;
}

export const GradeSelector: React.FC<GradeSelectorProps> = ({
  selectedGrade,
  isDropdownOpen,
  displayGradeText,
  gradeSelectorText,
  setIsDropdownOpen,
  handleGradeSelect,
}) => {
  return (
    <div className="mt-4 pl-16">
      <div className="flex items-center mb-3 h-8">
        <img src={stars} alt="" className="w-6 h-6 mr-2 ml-[13px]" />
        <p className="text-xl font-semibold text-gray-700">{displayGradeText}</p>
      </div>

      {displayGradeText.length === gradeSelectorText.length && (
        <>
          <label
            htmlFor="grade-select"
            className="block text-base font-medium text-gray-700 mt-[15px] text-[16px] pl-[13px]"
          >
            Grade Level:
          </label>
          <div className="relative mt-1">
            <button
              type="button"
              id="grade-select"
              className="w-[1024px] mt-[12px] h-10 bg-white border border-gray-300 rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none sm:text-sm flex items-center justify-between"
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            >
              {selectedGrade}
              <img src={arrowsUpDown} alt="" className="w-5 h-5 text-gray-400" />
            </button>
            {isDropdownOpen && (
              <ul className="absolute z-10 mt-1 w-[1024px] bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                {gradeLevels.map(grade => (
                  <li
                    key={grade}
                    className="text-gray-900 cursor-default select-none relative py-2 pl-3 pr-9 hover:bg-gray-100"
                    onClick={() => handleGradeSelect(grade)}
                  >
                    {grade}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default GradeSelector;
