export interface SessionProgress {
  step: number;
  correct: boolean;
}

export interface Session {
  session_id: string;
  game_id: string;
  total_questions: number | null;
  progress: SessionProgress[];
  status: 'planned' | 'in_progress' | 'completed';
  duration_seconds: number | null;
  still_in_progress: boolean;
  correct_percent: number | null;
  intervention_id?: string; // Added to preserve intervention_id when flattening
}

export interface SessionGroup {
  intervention_id: string;
  entries: Session[];
}

export interface InterventionGroup {
  intervention_id: string;
  entries: Session[];
}

export interface TodaysSessions {
  date: string;
  weekday: string;
  is_today: boolean;
  in_current_month: boolean;
  sessions?: SessionGroup[];
  interventions?: InterventionGroup[];
  error?: string; // Opcjonalne pole dla komunikatów błędów
}
