# React Query Optimization Guide

## Overview

This document outlines the React Query optimizations implemented in the Teacher Dashboard and throughout the application to improve performance, caching, and user experience.

## Key Optimizations Implemented

### 1. Centralized Query Configuration

**File**: `src/main.tsx`

```typescript
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: (failureCount, error: any) => {
        // Don't retry for 4xx errors (client errors)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Max 2 retries for other errors
        return failureCount < 2;
      },
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});
```

### 2. Structured Query Keys

**File**: `src/services/queries/studentQueries.ts`

```typescript
export const studentQueryKeys = {
  all: ['students'] as const,
  lists: () => [...studentQueryKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...studentQueryKeys.lists(), { filters }] as const,
  details: () => [...studentQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...studentQueryKeys.details(), id] as const,
  stats: () => [...studentQueryKeys.all, 'stats'] as const,
  globalStats: () => [...studentQueryKeys.stats(), 'global'] as const,
  myStudentsStats: () => [...studentQueryKeys.stats(), 'myStudents'] as const,
  calendar: (studentId: string) => [...studentQueryKeys.all, 'calendar', studentId] as const,
};
```

### 3. Optimized Teacher Dashboard Hooks

**File**: `src/services/queries/studentQueries.ts`

#### Global Statistics Hook

```typescript
export const useStudentsStats = () => {
  return useQuery<StudentsStats>({
    queryKey: studentQueryKeys.globalStats(),
    queryFn: () => studentService.getStudentsStatistics(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};
```

#### Teacher's Students Statistics Hook

```typescript
export const useMyStudentsStats = () => {
  return useQuery<MyStudentsStatsResponse>({
    queryKey: studentQueryKeys.myStudentsStats(),
    queryFn: () => studentService.getMyStudentsStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes - more frequent updates
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: true,
    retry: 2,
  });
};
```

#### Combined Dashboard Data Hook

```typescript
export const useTeacherDashboardData = () => {
  const globalStatsQuery = useStudentsStats();
  const myStudentsStatsQuery = useMyStudentsStats();

  return {
    globalStats: globalStatsQuery.data,
    myStudentsStats: myStudentsStatsQuery.data,
    isLoading: globalStatsQuery.isLoading || myStudentsStatsQuery.isLoading,
    isError: globalStatsQuery.isError || myStudentsStatsQuery.isError,
    error: globalStatsQuery.error || myStudentsStatsQuery.error,
    refetch: () => {
      globalStatsQuery.refetch();
      myStudentsStatsQuery.refetch();
    },
  };
};
```

### 4. Mutation Optimizations

#### Smart Cache Invalidation

```typescript
export const useAddStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: studentData => studentService.addStudent(studentData),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.myStudentsStats() });
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.globalStats() });
    },
  });
};
```

## Performance Benefits

### 1. Reduced API Calls

- **Caching**: Data is cached for 5-10 minutes, reducing unnecessary API calls
- **Smart Invalidation**: Only relevant queries are invalidated after mutations
- **Background Updates**: Data is updated in the background when stale

### 2. Better User Experience

- **Loading States**: Proper loading states for all data fetching
- **Error Handling**: Centralized error handling with retry logic
- **Optimistic Updates**: UI updates immediately for better perceived performance

### 3. Memory Management

- **Garbage Collection**: Unused data is cleaned up after 10 minutes
- **Selective Refetching**: Only refetch when necessary (e.g., on reconnect)

## Development Tools

### React Query Devtools

- **Enabled in Development**: Provides insights into query states and cache
- **Query Inspector**: View query keys, data, and cache status
- **Performance Monitoring**: Track query performance and optimization opportunities

## Best Practices Implemented

### 1. Query Key Structure

- **Hierarchical**: Organized from general to specific
- **Consistent**: Same pattern across all queries
- **Type-Safe**: Using TypeScript const assertions

### 2. Stale Time Configuration

- **Global Stats**: 5 minutes (less frequently changing)
- **Teacher's Students**: 2 minutes (more dynamic data)
- **Real-time Data**: 30 seconds (for active sessions)

### 3. Error Handling

- **Smart Retry**: Don't retry client errors (4xx)
- **Limited Retries**: Maximum 2 retries to avoid infinite loops
- **Graceful Degradation**: Show error states with retry options

### 4. Cache Management

- **Automatic Invalidation**: After successful mutations
- **Background Refetching**: Keep data fresh without blocking UI
- **Memory Efficient**: Automatic cleanup of unused data

## Migration from Zustand

The Teacher Dashboard was migrated from Zustand store to React Query for better:

- **Server State Management**: React Query is designed for server state
- **Caching Strategy**: Built-in intelligent caching
- **Background Synchronization**: Automatic data synchronization
- **Developer Experience**: Better debugging and monitoring tools

## Future Optimizations

1. **Prefetching**: Implement prefetching for predictable navigation patterns
2. **Optimistic Updates**: Add optimistic updates for mutations
3. **Infinite Queries**: For paginated data (student lists, etc.)
4. **Suspense Integration**: Use React Suspense for better loading states
5. **Offline Support**: Implement offline-first strategies with React Query

## Monitoring and Debugging

- Use React Query Devtools in development
- Monitor query performance in production
- Track cache hit rates and optimization opportunities
- Set up error tracking for failed queries
