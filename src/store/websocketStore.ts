import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { GameStats } from '@games/common/types';
import logger from '../utils/logger';

// Control flag to completely bypass WebSockets
const MOCK_WEBSOCKET = false;

export interface WebSocketState {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  connectionFailed: boolean;
  hasAttemptedConnection: boolean;
  reconnectAttempts: number;

  // Message queue for offline/failed sends
  messageQueue: GameStats[];

  // Actions
  setConnectionState: (
    state: Partial<Pick<WebSocketState, 'isConnected' | 'isConnecting' | 'connectionFailed'>>
  ) => void;
  setAttemptedConnection: (attempted: boolean) => void;
  incrementReconnectAttempts: () => void;
  resetReconnectAttempts: () => void;

  // Message queue management
  addToQueue: (stats: GameStats) => void;
  removeFromQueue: (stats: GameStats) => void;
  clearQueue: () => void;
  getQueueLength: () => number;

  // Reset all state
  resetState: () => void;
}

const initialState = {
  // When mock is enabled, we pretend to always be connected
  isConnected: MOCK_WEBSOCKET ? true : false,
  isConnecting: false,
  connectionFailed: false,
  hasAttemptedConnection: MOCK_WEBSOCKET ? true : false,
  reconnectAttempts: 0,
  messageQueue: [],
};

export const useWebSocketStore = create<WebSocketState>()(
  persist(
    (set, get) => ({
      ...initialState,

      setConnectionState: newState => {
        logger.log('[WebSocketStore] Setting connection state:', newState);
        // When mocked, keep connected = true regardless of real state changes
        if (MOCK_WEBSOCKET) {
          const mockedState = { ...newState };
          // Never allow connection to be set to false when mocked
          if (mockedState.isConnected === false) {
            mockedState.isConnected = true;
          }
          set(state => ({ ...state, ...mockedState }));
        } else {
          set(state => ({ ...state, ...newState }));
        }
      },

      setAttemptedConnection: attempted => {
        logger.log('[WebSocketStore] Setting attempted connection:', attempted);
        // When mocked, always indicate we've attempted (and succeeded)
        if (MOCK_WEBSOCKET) {
          set({ hasAttemptedConnection: true });
        } else {
          set({ hasAttemptedConnection: attempted });
        }
      },

      incrementReconnectAttempts: () => {
        set(state => {
          const newAttempts = state.reconnectAttempts + 1;
          logger.log(`[WebSocketStore] Incrementing reconnect attempts to: ${newAttempts}`);
          return { reconnectAttempts: newAttempts };
        });
      },

      resetReconnectAttempts: () => {
        logger.log('[WebSocketStore] Resetting reconnect attempts');
        set({ reconnectAttempts: 0 });
      },

      addToQueue: stats => {
        logger.log('[WebSocketStore] Adding stats to queue:', stats);
        // When mocked, log but don't actually queue the message
        if (MOCK_WEBSOCKET) {
          logger.info('[MOCK WebSocket] Simulating successful message send:', stats);
          // In mock mode, we pretend the message was sent successfully
          // so we don't add it to the queue
          return;
        }

        set(state => ({
          messageQueue: [...state.messageQueue, stats],
        }));
      },

      removeFromQueue: statsToRemove => {
        logger.log('[WebSocketStore] Removing stats from queue:', statsToRemove);
        set(state => ({
          messageQueue: state.messageQueue.filter(
            stats =>
              !(
                stats.gameId === statsToRemove.gameId &&
                stats.studentId === statsToRemove.studentId &&
                stats.startTime === statsToRemove.startTime
              )
          ),
        }));
      },

      clearQueue: () => {
        const queueLength = get().messageQueue.length;
        if (queueLength > 0) {
          logger.log(`[WebSocketStore] Clearing message queue (${queueLength} messages)`);
          set({ messageQueue: [] });
        }
      },

      getQueueLength: () => {
        return get().messageQueue.length;
      },

      resetState: () => {
        logger.log('[WebSocketStore] Resetting all WebSocket state');
        set(initialState);
      },
    }),
    {
      name: 'yubu-websocket',
      // Only persist the message queue, not connection state
      partialize: state => ({
        messageQueue: state.messageQueue,
      }),
    }
  )
);
