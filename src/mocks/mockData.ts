import { StudentData, UserData } from '../services/authService';
import { MyStudentsStatsResponse } from '../services/studentService';

// Interfejs dla statystyk studentów
export interface StudentsStats {
  totalStudents: number;
  activeStudents: number;
  newStudentsThisMonth: number;
  averageSessionsPerStudent: number;
  completedInterventions: number;
  pendingInterventions: number;
}

// <PERSON> (nauczyciel)
export const mockTeacher: UserData = {
  user_id: 1,
  email: '<EMAIL>',
  role: 'teacher',
  role_label: 'Teacher',
  is_logged_in: true,
  avatar_url: 'https://randomuser.me/api/portraits/men/1.jpg',
};

// Lista studentów
export const mockStudents: StudentData[] = [
  {
    id: 101,
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    age: 8,
    gender: 'male',
    additional_info: 'Struggles with reading comprehension',
    created_at: '2024-04-15T10:00:00Z',
    user_id: 201,
  },
  {
    id: 102,
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    age: 7,
    gender: 'female',
    additional_info: 'Excellent in math',
    created_at: '2024-04-15T10:30:00Z',
    user_id: 202,
  },
  {
    id: 103,
    first_name: 'Alex',
    last_name: 'Johnson',
    age: 9,
    gender: 'male',
    additional_info: 'Needs help with vocabulary',
    created_at: '2024-04-16T09:15:00Z',
    user_id: 203,
  },
  {
    id: 104,
    first_name: 'Emma',
    last_name: 'Wilson',
    age: 6,
    gender: 'female',
    additional_info: 'New student, needs assessment',
    created_at: '2024-04-17T14:20:00Z',
    user_id: 204,
  },
];

// Dane dla dashboardu nauczyciela
export const mockTeacherDashboardData = {
  students_count: 28,
  reports_count: 86,
  pending_reports: 2,
  messages_count: 125,
  new_messages: 3,
  tests_count: 33,
};

// Statystyki studentów
export const mockStudentsStats: StudentsStats = {
  totalStudents: 28,
  activeStudents: 24,
  newStudentsThisMonth: 4,
  averageSessionsPerStudent: 12.5,
  completedInterventions: 156,
  pendingInterventions: 23,
};

// Statystyki moich studentów
export const mockMyStudentsStats: MyStudentsStatsResponse = {
  user_id: 1,
  role: 'teacher',
  total_students: 4,
  students: [
    {
      student_id: 101,
      full_name: 'John Doe',
      intervention_starts: '2024-04-15T10:00:00Z',
      intervention_completed: '75%',
      avg_score_progress: '85%',
      interventions_above_30: {
        count: 12,
        percent: 80,
      },
      interventions_days_in_a_row: 5,
      last_intervention: '2024-04-20T10:00:00Z',
    },
    {
      student_id: 102,
      full_name: 'Jane Smith',
      intervention_starts: '2024-04-16T10:30:00Z',
      intervention_completed: '90%',
      avg_score_progress: '92%',
      interventions_above_30: {
        count: 18,
        percent: 95,
      },
      interventions_days_in_a_row: 7,
      last_intervention: '2024-04-21T14:30:00Z',
    },
    {
      student_id: 103,
      full_name: 'Alex Johnson',
      intervention_starts: '2024-04-17T09:15:00Z',
      intervention_completed: '40%',
      avg_score_progress: '65%',
      interventions_above_30: {
        count: 6,
        percent: 60,
      },
      interventions_days_in_a_row: 2,
      last_intervention: '2024-04-19T09:15:00Z',
    },
    {
      student_id: 104,
      full_name: 'Emma Wilson',
      intervention_starts: '2024-04-18T14:20:00Z',
      intervention_completed: '15%',
      avg_score_progress: '45%',
      interventions_above_30: {
        count: 2,
        percent: 25,
      },
      interventions_days_in_a_row: 1,
      last_intervention: '2024-04-18T16:45:00Z',
    },
  ],
};

// Lista interwencji
export const mockInterventions = [
  {
    id: 501,
    name: 'Memory Game',
    type: 'memory-game',
    description: 'Improve memory and cognitive skills',
    difficulty: 1,
    duration_minutes: 10,
    status: 'active',
    image_url: 'https://via.placeholder.com/150',
    created_at: '2024-04-10T10:00:00Z',
  },
  {
    id: 502,
    name: 'Math Challenge',
    type: 'math-game',
    description: 'Practice basic arithmetic operations',
    difficulty: 2,
    duration_minutes: 15,
    status: 'active',
    image_url: 'https://via.placeholder.com/150',
    created_at: '2024-04-11T10:00:00Z',
  },
  {
    id: 503,
    name: 'Word Building',
    type: 'blend-game--word-building',
    description: 'Improve vocabulary and spelling',
    difficulty: 1,
    duration_minutes: 10,
    status: 'active',
    image_url: 'https://via.placeholder.com/150',
    created_at: '2024-04-12T10:00:00Z',
  },
  {
    id: 504,
    name: 'Reading Comprehension',
    type: 'support-strategy--semantic-mapping',
    description: 'Enhance reading comprehension skills',
    difficulty: 3,
    duration_minutes: 20,
    status: 'active',
    image_url: 'https://via.placeholder.com/150',
    created_at: '2024-04-13T10:00:00Z',
  },
];

// Przypisane interwencje dla studentów
export const mockAssignedInterventions = [
  {
    id: 601,
    intervention_id: 501,
    student_id: 101,
    status: 'completed',
    progress: 100,
    start_date: '2024-05-01T10:00:00Z',
    end_date: '2024-05-08T10:00:00Z',
    sessions_completed: 5,
    sessions_total: 5,
  },
  {
    id: 602,
    intervention_id: 502,
    student_id: 101,
    status: 'in-progress',
    progress: 60,
    start_date: '2024-05-10T10:00:00Z',
    end_date: null,
    sessions_completed: 3,
    sessions_total: 5,
  },
  {
    id: 603,
    intervention_id: 503,
    student_id: 102,
    status: 'locked',
    progress: 0,
    start_date: '2024-05-15T10:00:00Z',
    end_date: null,
    sessions_completed: 0,
    sessions_total: 5,
  },
];

// Dane sesji dla studenta
export const mockTodaysSessions = {
  todaySessions: [
    {
      id: 701,
      title: 'Memory Game',
      description: 'Practice memory skills',
      difficulty: 'Easy',
      scheduled_date: '2024-06-12T11:00:00Z',
      type: 'memory-game',
      duration_minutes: 10,
      intervention_id: 501,
    },
    {
      id: 702,
      title: 'Math Challenge',
      description: 'Solve arithmetic problems',
      difficulty: 'Medium',
      scheduled_date: '2024-06-12T14:30:00Z',
      type: 'math-game',
      duration_minutes: 15,
      intervention_id: 502,
    },
  ],
};

// Dane czatu
export const mockChatMessages = [
  {
    id: 'msg1',
    text: 'Hello! How can I help you today?',
    sender: 'bot',
    timestamp: new Date().toISOString(),
    type: 'text',
  },
];

// Ostatnio aktywni studenci w czacie
export const mockLastActiveStudents = [
  {
    student_id: 101,
    first_name: 'John',
    last_name: 'Doe',
    last_activity: new Date(Date.now() - 10 * 60000).toISOString(), // 10 minut temu
    messages_count: 15,
    avatar_url: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    student_id: 102,
    first_name: 'Jane',
    last_name: 'Smith',
    last_activity: new Date(Date.now() - 25 * 60000).toISOString(), // 25 minut temu
    messages_count: 8,
    avatar_url: 'https://randomuser.me/api/portraits/women/44.jpg',
  },
  {
    student_id: 103,
    first_name: 'Alex',
    last_name: 'Johnson',
    last_activity: new Date(Date.now() - 120 * 60000).toISOString(), // 2 godziny temu
    messages_count: 23,
    avatar_url: 'https://randomuser.me/api/portraits/men/22.jpg',
  },
];

// Dane dla gier
export const mockGamesData = [
  {
    id: 'blend-game',
    name: 'Word Blending',
    description: 'Practice blending sounds to form words',
    thumbnail: 'https://placehold.co/100',
    difficulty: 'beginner',
  },
  {
    id: 'comprehension-game',
    name: 'Reading Comprehension',
    description: 'Answer questions about short stories',
    thumbnail: 'https://placehold.co/100',
    difficulty: 'intermediate',
  },
  {
    id: 'phonics-game',
    name: 'Phonics Challenge',
    description: 'Match sounds with letters',
    thumbnail: 'https://placehold.co/100',
    difficulty: 'beginner',
  },
  {
    id: 'vocabulary-game',
    name: 'Vocabulary Builder',
    description: 'Learn new words and their meanings',
    thumbnail: 'https://placehold.co/100',
    difficulty: 'advanced',
  },
  {
    id: 'blend-game--word-building',
    name: 'Word Building',
    description: 'Improve vocabulary and spelling',
    image: 'https://via.placeholder.com/150',
    type: 'blend-game--word-building',
  },
];

// Różne role użytkowników
export const roles = [
  { id: 1, name: 'teacher' },
  { id: 2, name: 'student' },
  { id: 3, name: 'parent' },
  { id: 4, name: 'admin' },
];

// Dane dokumentów medycznych
export const mockDocuments = [
  {
    id: 1,
    student_id: 1,
    file_name: 'medical_report_001.txt',
    content: 'Patient shows signs of dyslexia with difficulties in phonological processing.',
    upload_date: new Date().toISOString(),
    status: 'processed',
  },
  {
    id: 2,
    student_id: 2,
    file_name: 'assessment_002.txt',
    content: 'Moderate reading difficulties observed. Recommend phonics intervention.',
    upload_date: new Date().toISOString(),
    status: 'processed',
  },
  {
    id: 3,
    student_id: 1,
    file_name: 'progress_report_003.txt',
    content: 'Showing improvement after 4 weeks of phonological awareness training.',
    upload_date: new Date().toISOString(),
    status: 'processed',
  },
];

// Dane ankiet dla nauczycieli
export const mockEducatorSurveys = [
  {
    id: 1,
    student_id: 1,
    difficulties: 'Difficulty focusing on texts',
    frequency: 'Often',
    strategies: 'Uses highlighting and repeated reading',
    attitude: 'Positive',
    created_at: new Date().toISOString(),
  },
  {
    id: 2,
    student_id: 2,
    difficulties: 'Slow reading speed, frequent word confusion',
    frequency: 'Always',
    strategies: 'Text-to-speech tools, reading guides',
    attitude: 'Frustrated',
    created_at: new Date().toISOString(),
  },
];

// Opcje dla ankiet edukacyjnych
export const mockEducatorSurveyOptions = {
  frequencies: ['Rarely', 'Sometimes', 'Often', 'Always'],
  attitudes: ['Negative', 'Frustrated', 'Neutral', 'Positive', 'Enthusiastic'],
};

// Dane sesji gier
export const mockGameSessions = [
  {
    sessionId: 'f7d8393e-e603-41b9-bab9-5cd2f9b27b16',
    gameId: 'blend-game',
    studentId: '1',
    totalQuestions: 6,
    correctAnswers: 5,
    incorrectAnswers: 1,
    completionPercentage: 100,
    startTime: Date.now() - 3600000, // godzina temu
    endTime: Date.now() - 3540000, // 10 minut temu
    progress: [
      { step: 1, correct: true },
      { step: 2, correct: true },
      { step: 3, correct: false },
      { step: 4, correct: true },
      { step: 5, correct: true },
      { step: 6, correct: true },
    ],
  },
  {
    sessionId: 'a1b2c3d4-e5f6-4321-8765-9abc87654321',
    gameId: 'comprehension-game',
    studentId: '2',
    totalQuestions: 4,
    correctAnswers: 2,
    incorrectAnswers: 2,
    completionPercentage: 100,
    startTime: Date.now() - 7200000, // 2 godziny temu
    endTime: Date.now() - 7100000,
    progress: [
      { step: 1, correct: false },
      { step: 2, correct: true },
      { step: 3, correct: false },
      { step: 4, correct: true },
    ],
  },
  {
    sessionId: 'b2c3d4e5-f6a7-5432-9876-0abc98765432',
    gameId: 'blend-game',
    studentId: '3',
    totalQuestions: 6,
    correctAnswers: 2,
    incorrectAnswers: 1,
    completionPercentage: 50,
    startTime: Date.now() - 300000, // 5 minut temu
    endTime: null, // nadal w trakcie
    progress: [
      { step: 1, correct: true },
      { step: 2, correct: true },
      { step: 3, correct: false },
    ],
  },
];

// Role użytkowników
export const mockRoles = [
  { id: 1, name: 'admin', label: 'Administrator' },
  { id: 2, name: 'teacher', label: 'Teacher' },
  { id: 3, name: 'student', label: 'Student' },
];
