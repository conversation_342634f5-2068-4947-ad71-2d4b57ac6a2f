import React from 'react';

interface Props {
  current: number;
  total: number;
}
export const ProgressBar: React.FC<Props> = ({ current, total }) => {
  const pct = Math.round((current / total) * 100);
  return (
    <div className="mb-8">
      <div className="flex justify-between mb-2 text-sm font-medium text-gray-600">
        <span>Progress</span>
        <span className="text-blue-600">
          {current}/{total}
        </span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500"
          style={{ width: `${pct}%` }}
        />
      </div>
      <p className="text-xs text-gray-500 mt-1">{pct}% complete</p>
    </div>
  );
};
