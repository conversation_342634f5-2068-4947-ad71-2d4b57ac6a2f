import React from 'react';
import { <PERSON>, ArrowR<PERSON>, FileText, Clock } from 'lucide-react';

interface WelcomeScreenProps {
  onStart: () => void;
  totalQuestions: number;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onStart, totalQuestions }) => (
  <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center px-4">
    <div className="max-w-2xl text-center">
      <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-100 rounded-full mb-8 animate-pulse">
        <Brain className="w-10 h-10 text-blue-600" />
      </div>
      <h1 className="text-4xl font-bold text-gray-800 mb-6">Behavioral Questionnaire</h1>
      <p className="text-xl text-gray-600 mb-8 leading-relaxed">
        Assess the frequency of specific behaviors using a scale from &quot;Never&quot; to
        &quot;Always&quot;.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <FileText className="w-8 h-8 text-blue-600 mx-auto mb-3" />
          <h3 className="font-semibold text-gray-800 mb-2">{totalQuestions} Questions</h3>
          <p className="text-sm text-gray-600">One question per screen</p>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <Clock className="w-8 h-8 text-green-600 mx-auto mb-3" />
          <h3 className="font-semibold text-gray-800 mb-2">~5 minutes</h3>
          <p className="text-sm text-gray-600">Estimated completion time</p>
        </div>
      </div>
      <button
        onClick={onStart}
        className="inline-flex items-center px-8 py-4 bg-blue-600 text-white text-lg font-semibold rounded-xl hover:bg-blue-700 transition-all duration-200 transform hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        Start Questionnaire
        <ArrowRight className="w-5 h-5 ml-2" />
      </button>
      <p className="text-sm text-gray-500 mt-6">You can navigate back at any time</p>
    </div>
  </div>
);
