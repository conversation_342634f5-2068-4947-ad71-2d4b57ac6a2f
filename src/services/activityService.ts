import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createWebSocketConnection } from './websocketService';
import { authService } from './authService';
import { Session, TodaysSessions } from '../types/session';
import { useUserStore } from '../store/userStore';
import logger from '../utils/logger';

const API_URL = 'https://api.dev.yubu.ai';
const ACTIVITIES_ENDPOINT = `${API_URL}/games/activities`;
const TODAYS_SESSIONS_ENDPOINT = `${API_URL}/students/todays_sessions`;
const WS_ACTIVITY_PROGRESS_ENDPOINT = '/ws/games/activity-progress';

export interface Activity {
  id: string;
  type: 'blend' | 'reading' | 'listening' | 'writing' | 'speaking';
  title: string;
  description: string;
  duration: number;
  difficulty: 'easy' | 'medium' | 'hard';
  status: 'not_started' | 'in_progress' | 'completed';
}

export interface ActivityProgress {
  exerciseNumber: number;
  iteration1: boolean;
  iteration2: boolean;
}

export interface ActivityDetail {
  id: string;
  type: string;
  title: string;
  studentName: string;
  currentIteration: number;
  totalIterations: number;
  timeSpent: string;
  progress: ActivityProgress[];
}

export interface UpdateProgressPayload {
  activityId: string;
  exerciseNumber: number;
  iteration: 1 | 2;
  completed: boolean;
}

const MOCK_ACTIVITIES: Activity[] = [
  {
    id: '1',
    type: 'blend',
    title: 'Blend Activity',
    description: '10 min / day • Do anytime',
    duration: 10,
    difficulty: 'medium',
    status: 'not_started',
  },
  {
    id: '2',
    type: 'reading',
    title: 'Reading Exercise',
    description: '15 min / day • Morning activity',
    duration: 15,
    difficulty: 'easy',
    status: 'not_started',
  },
  {
    id: '3',
    type: 'listening',
    title: 'Listening Practice',
    description: '20 min / day • Afternoon activity',
    duration: 20,
    difficulty: 'hard',
    status: 'not_started',
  },
];

class ActivityService {
  private socket: WebSocket | null = null;
  private progressListeners: ((progress: ActivityProgress) => void)[] = [];

  constructor() {}

  private initWebSocket() {
    this.socket = createWebSocketConnection(WS_ACTIVITY_PROGRESS_ENDPOINT);

    this.socket.onmessage = event => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'activity_progress_update') {
          this.progressListeners.forEach(listener => listener(data.progress));
        }
      } catch (error) {
        logger.error('Error processing WebSocket message:', error);
      }
    };
  }

  subscribeToProgressUpdates(callback: (progress: ActivityProgress) => void) {
    this.progressListeners.push(callback);
    return () => {
      this.progressListeners = this.progressListeners.filter(listener => listener !== callback);
    };
  }

  /**
   * Gets the list of activities for the logged in student
   */
  async getStudentActivities(): Promise<Activity[]> {
    try {
      const todaysSessions = await this.getTodaysSessions();

      const allSessions = this.flattenSessionsList(todaysSessions);

      const activities: Activity[] = allSessions.map(session => ({
        id: session.session_id,
        type: this.getActivityTypeFromGameId(session.game_id),
        title: this.formatGameTitle(session.game_id),
        description: session.status === 'completed' ? 'Completed' : 'To do',
        duration: 10, // Default value
        difficulty: 'medium', // Default value
        status: session.status as 'not_started' | 'in_progress' | 'completed',
      }));

      return activities;
    } catch (error) {
      logger.error('Error fetching activities:', error);
      return [];
    }
  }

  /**
   * Gets today's sessions for the logged in student
   */
  async getTodaysSessions(): Promise<TodaysSessions> {
    try {
      const token = authService.getAccessToken();
      if (!token) {
        throw new Error('Missing authorization token');
      }

      const user = useUserStore.getState().user;

      const studentId = user?.student?.id || user?.user_id || 1;

      const response = await fetch(TODAYS_SESSIONS_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ student_id: studentId }),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        if (response.status === 401 && errorData?.detail === 'Token expired') {
          try {
            await authService.refreshToken();

            return this.getTodaysSessions();
          } catch (refreshError) {
            logger.error('Error refreshing token:', refreshError);
            throw new Error('Failed to refresh token');
          }
        }

        throw new Error(`API Error: ${response.status} ${errorData?.detail || ''}`);
      }

      const data: TodaysSessions = await response.json();
      return data;
    } catch (error) {
      logger.error('Error fetching sessions:', error);
      return {
        date: new Date().toISOString().split('T')[0],
        weekday: new Date().toLocaleDateString('en-US', { weekday: 'long' }),
        is_today: true,
        in_current_month: true,
        sessions: [],
      };
    }
  }

  /**
   * Flattens the list of sessions from all intervention groups
   * @param todaysSessions - Today's sessions
   * @param gameId - Optional game ID for filtering sessions
   * @returns Flat list of sessions with preserved intervention_id
   */
  flattenSessionsList(todaysSessions: TodaysSessions, gameId?: string): Session[] {
    if (!todaysSessions.interventions) return [];

    return todaysSessions.interventions
      .flatMap(
        intervention =>
          intervention.entries?.map(session => ({
            ...session,
            intervention_id: intervention.intervention_id,
          })) || []
      )
      .filter(session => !gameId || session.game_id === gameId);
  }

  /**
   * Finds a session with the given ID among today's sessions
   * @param todaysSessions - Today's sessions
   * @param sessionId - Session ID to find
   * @returns Found session or undefined
   */
  findSessionById(todaysSessions: TodaysSessions, sessionId: string): Session | undefined {
    if (!todaysSessions.interventions) return undefined;

    for (const intervention of todaysSessions.interventions) {
      const session = intervention.entries?.find(s => s.session_id === sessionId);
      if (session) {
        return { ...session, intervention_id: intervention.intervention_id };
      }
    }
    return undefined;
  }

  /**
   * Gets session details by ID
   * @param sessionId - Session ID
   */
  async getSessionDetails(sessionId: string): Promise<Session | null> {
    try {
      const todaysSessions = await this.getTodaysSessions();

      // Znajdujemy sesję o podanym ID
      const foundSession = this.findSessionById(todaysSessions, sessionId);

      if (!foundSession) {
        throw new Error('No session found with the given ID');
      }

      return foundSession;
    } catch (error) {
      logger.error('Error fetching session details:', error);
      return null;
    }
  }

  /**
   * Gets activity details by ID
   * @param activityId - Activity ID (session_id in this case)
   */
  async getActivityDetails(activityId: string): Promise<ActivityDetail> {
    try {
      const session = await this.getSessionDetails(activityId);

      if (!session) {
        throw new Error('No session found with the given ID');
      }

      const activityDetail: ActivityDetail = {
        id: session.session_id,
        type: this.getActivityTypeFromGameId(session.game_id),
        title: this.formatGameTitle(session.game_id),
        studentName: 'Student', // Temporarily using a constant value
        currentIteration: 1,
        totalIterations: session.total_questions || 5,
        timeSpent: session.duration_seconds
          ? `${Math.floor(session.duration_seconds / 60)}:${session.duration_seconds % 60}`
          : '0:00',
        progress: session.progress.map((p, index) => ({
          exerciseNumber: p.step || index + 1,
          iteration1: p.correct,
          iteration2: false, // Temporarily using a constant value
        })),
      };

      return activityDetail;
    } catch (error) {
      logger.error('Error fetching activity details:', error);

      return {
        id: activityId,
        type: 'blend',
        title: 'Unknown activity',
        studentName: 'Student',
        currentIteration: 1,
        totalIterations: 5,
        timeSpent: '0:00',
        progress: [
          { exerciseNumber: 1, iteration1: false, iteration2: false },
          { exerciseNumber: 2, iteration1: false, iteration2: false },
          { exerciseNumber: 3, iteration1: false, iteration2: false },
          { exerciseNumber: 4, iteration1: false, iteration2: false },
          { exerciseNumber: 5, iteration1: false, iteration2: false },
        ],
      };
    }
  }

  /**
   * Starts an activity with the given ID
   * @param activityId - Activity ID
   */
  async startActivity(activityId: string): Promise<boolean> {
    try {
      // Here you can add code to start the activity via API
      return true;
    } catch (error) {
      logger.error('Error starting activity:', error);
      return false;
    }
  }

  /**
   * Updates activity progress
   * @param payload - Progress update data
   */
  async updateProgress(payload: UpdateProgressPayload): Promise<boolean> {
    try {
      // Here you can add code to update progress via API
      return true;
    } catch (error) {
      logger.error('Error updating progress:', error);
      return false;
    }
  }

  /**
   * Marks activity as completed
   * @param activityId - Activity ID
   */
  async completeActivity(activityId: string): Promise<boolean> {
    try {
      // Here you can add code to mark the activity as completed via API
      return true;
    } catch (error) {
      logger.error('Error marking activity as completed:', error);
      return false;
    }
  }

  /**
   * Helper method for formatting game title
   * @param gameId - Game ID
   */
  private formatGameTitle(gameId: string): string {
    if (!gameId) return 'Unknown activity';

    // Remove 'game-' prefix if it exists
    let title = gameId.replace(/^game-/, '');

    // Replace hyphens and underscores with spaces
    title = title.replace(/[-_]/g, ' ');

    // Capitalize first letter
    return title.charAt(0).toUpperCase() + title.slice(1);
  }

  /**
   * Helper method for determining activity type based on game ID
   * @param gameId - Game ID
   */
  private getActivityTypeFromGameId(
    gameId: string
  ): 'blend' | 'reading' | 'listening' | 'writing' | 'speaking' {
    if (!gameId) return 'blend';

    if (gameId.includes('blend')) return 'blend';
    if (gameId.includes('read')) return 'reading';
    if (gameId.includes('listen')) return 'listening';
    if (gameId.includes('writ')) return 'writing';
    if (gameId.includes('speak')) return 'speaking';

    return 'blend'; // Default type
  }
}

export const activityService = new ActivityService();

// React Query hooks for use in components
export const useActivities = () => {
  return useQuery({
    queryKey: ['activities'],
    queryFn: () => activityService.getStudentActivities(),
  });
};

// Tworzymy cache dla zapytań, aby uniknąć duplikacji
const todaysSessionsCache: {
  data: TodaysSessions | null;
  timestamp: number;
  promise: Promise<TodaysSessions> | null;
} = {
  data: null,
  timestamp: 0,
  promise: null,
};

// Funkcja do pobierania danych z cache lub wykonywania nowego zapytania
const getTodaysSessionsCached = async (): Promise<TodaysSessions> => {
  const now = Date.now();
  const cacheValidTime = 30000; // Cache valid for 30 seconds

  // If we have an active query, return it
  if (todaysSessionsCache.promise) {
    return todaysSessionsCache.promise;
  }

  // If we have data in cache and it's still valid, return it
  if (todaysSessionsCache.data && now - todaysSessionsCache.timestamp < cacheValidTime) {
    return todaysSessionsCache.data;
  }

  // W przeciwnym razie wykonaj nowe zapytanie
  todaysSessionsCache.promise = activityService
    .getTodaysSessions()
    .then(data => {
      todaysSessionsCache.data = data;
      todaysSessionsCache.timestamp = now;
      todaysSessionsCache.promise = null;
      return data;
    })
    .catch(error => {
      todaysSessionsCache.promise = null;
      throw error;
    });

  return todaysSessionsCache.promise;
};

// Function to clear the cache - useful when we know data has changed
export const clearTodaysSessionsCache = () => {
  todaysSessionsCache.data = null;
  todaysSessionsCache.timestamp = 0;
  todaysSessionsCache.promise = null;
};

export const useActivityDetails = (activityId: string) => {
  return useQuery({
    queryKey: ['activity', activityId],
    queryFn: () => activityService.getActivityDetails(activityId),
    enabled: !!activityId,
    staleTime: 30000, // Dane są świeże przez 30 sekund
    refetchInterval: 30000, // Odświeżanie co 30 sekund
  });
};

// Hook do pobierania szczegółów sesji
export const useSessionDetails = (sessionId: string) => {
  return useQuery<Session>({
    queryKey: ['session', sessionId],
    queryFn: async () => {
      // Używamy bezpośrednio activityService.getTodaysSessions() bez cache
      const todaysSessions = await activityService.getTodaysSessions();

      // Znajdujemy sesję o podanym ID
      const foundSession = activityService.findSessionById(todaysSessions, sessionId);

      if (!foundSession) {
        logger.error('No session found with the given ID:', sessionId);
        throw new Error('No session found with the given ID');
      }

      return foundSession;
    },
    enabled: !!sessionId,
    staleTime: 0, // Dane nigdy nie są cache'owane
    gcTime: 0, // Dane nie są przechowywane w pamięci
    refetchOnMount: true, // Zawsze refetch przy mount
    refetchOnWindowFocus: true, // Refetch przy focus
  });
};

export const useStartActivity = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (activityId: string) => activityService.startActivity(activityId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['activities'] });
    },
  });
};

export const useUpdateProgress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateProgressPayload) => activityService.updateProgress(payload),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['activity', variables.activityId] });
    },
  });
};

export const useCompleteActivity = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (activityId: string) => activityService.completeActivity(activityId),
    onSuccess: (_, activityId) => {
      queryClient.invalidateQueries({ queryKey: ['activity', activityId] });
      queryClient.invalidateQueries({ queryKey: ['activities'] });
    },
  });
};
