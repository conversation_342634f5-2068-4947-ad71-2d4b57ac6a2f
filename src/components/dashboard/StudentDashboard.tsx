import React from 'react';
import { useNavigate } from 'react-router-dom';

import { useTodaysSessions } from '../../services/sessionService';
import { Session } from '../../types/session';
import { useInterventionStore } from '../../store/interventionStore';
import { useUserStore } from '../../store/userStore';
import { USER_ROLES } from '../../contants';
import { StatCard } from './StatCard';
import { StudentsTable } from './StudentsTable';
import { useStudentDashboardData } from '../../services/queries/studentQueries';
import { useTodaysAssessments } from '../../services/queries/assessmentQueries';
import { AssessmentSession } from '../../services/assessmentService';

interface DashboardActivity {
  id: string;
  session_id: string;
  game_id?: string;
  assessment_id?: string;
  type: 'blend' | 'reading' | 'listening' | 'writing' | 'speaking' | 'assessment';
  title: string;
  description: string;
  duration: number;
  difficulty: 'easy' | 'medium' | 'hard';
  status: 'planned' | 'in_progress' | 'completed';
  activityType: 'intervention' | 'assessment';
}

export const StudentDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { data: todaysSessions, isLoading: _isLoading, error: _error } = useTodaysSessions();

  const interventionStore = useInterventionStore();
  const { user } = useUserStore();

  const isStudent = user?.role === USER_ROLES.STUDENT;
  const studentId = user?.student?.id || user?.user_id || 0;

  // Pobieranie dzisiejszych sesji testów
  const { data: todaysAssessments, isLoading: _assessmentsLoading } =
    useTodaysAssessments(studentId);

  React.useEffect(() => {
    if (!interventionStore.isLoaded) {
      interventionStore.fetchInterventions();
    }
  }, [interventionStore]);

  const mapGameIdToType = (
    gameId?: string
  ): 'blend' | 'reading' | 'listening' | 'writing' | 'speaking' => {
    if (!gameId) return 'blend';

    if (gameId.includes('math')) return 'blend';
    if (gameId.includes('read')) return 'reading';
    if (gameId.includes('listen')) return 'listening';
    if (gameId.includes('write')) return 'writing';
    if (gameId.includes('speak')) return 'speaking';
    return 'blend';
  };

  const mapSessionToTitle = React.useCallback(
    (session: Session): string => {
      if (session.intervention_id) {
        return interventionStore.getGameNameByInterventionId(
          session.intervention_id,
          session.game_id
        );
      }
      return interventionStore.getGameNameById(session.game_id);
    },
    [interventionStore]
  );

  const getSessionDescription = (session: Session): string => {
    const duration = session.duration_seconds
      ? `${Math.floor(session.duration_seconds / 60)} min`
      : '10 min';

    if (session.status === 'planned') {
      return `${duration} / day • Do anytime`;
    } else if (session.status === 'in_progress') {
      return `${duration} / day • In progress`;
    } else {
      return `${duration} / day • Completed ${session.correct_percent ? `(${session.correct_percent}% correct)` : ''}`;
    }
  };

  const getSessionDifficulty = (session: Session): 'easy' | 'medium' | 'hard' => {
    if (!session.correct_percent) return 'medium';

    if (session.correct_percent >= 80) return 'easy';
    if (session.correct_percent >= 50) return 'medium';
    return 'hard';
  };

  // Funkcje pomocnicze dla sesji testów
  const formatAssessmentTitle = (assessmentId: string): string => {
    return assessmentId
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const mapAssessmentToActivity = React.useCallback(
    (session: AssessmentSession): DashboardActivity => {
      return {
        id: session.session_id,
        session_id: session.session_id,
        assessment_id: session.assessment_id,
        type: 'assessment',
        title: formatAssessmentTitle(session.assessment_id),
        description: 'Complete your assessment',
        duration: 15, // Domyślny czas dla testów
        difficulty: 'medium',
        status: session.status,
        activityType: 'assessment',
      };
    },
    []
  );

  const activities = React.useMemo(() => {
    const allActivities: DashboardActivity[] = [];

    // Dodaj sesje interwencji
    if (todaysSessions) {
      const allSessions: Session[] = [];

      if (todaysSessions.interventions && Array.isArray(todaysSessions.interventions)) {
        todaysSessions.interventions.forEach(
          (intervention: { intervention_id: string; entries: Session[] }) => {
            if (intervention.entries && Array.isArray(intervention.entries)) {
              intervention.entries.forEach((entry: Session) => {
                // Dodajemy każdy wpis jako sesję
                allSessions.push(entry);
              });
            }
          }
        );
      } else if (todaysSessions.sessions && Array.isArray(todaysSessions.sessions)) {
        // Zachowujemy kompatybilność ze starą strukturą danych
        todaysSessions.sessions.forEach(sessionGroup => {
          if (sessionGroup.entries && Array.isArray(sessionGroup.entries)) {
            allSessions.push(...sessionGroup.entries);
          }
        });
      }

      // Filtrujemy sesje, aby usunąć te bez session_id
      const validSessions = allSessions.filter(session => session.session_id);

      const mappedInterventions: DashboardActivity[] = validSessions.map((session: Session) => {
        const activityItem: DashboardActivity = {
          id: session.session_id,
          session_id: session.session_id,
          game_id: session.game_id,
          type: mapGameIdToType(session.game_id),
          title: mapSessionToTitle(session),
          description: getSessionDescription(session),
          duration: session.duration_seconds ? Math.floor(session.duration_seconds / 60) : 10,
          difficulty: getSessionDifficulty(session),
          status: session.status,
          activityType: 'intervention',
        };

        return activityItem;
      });

      allActivities.push(...mappedInterventions);
    }

    // Dodaj sesje testów
    if (todaysAssessments?.sessions && Array.isArray(todaysAssessments.sessions)) {
      const mappedAssessments: DashboardActivity[] = todaysAssessments.sessions.map(session =>
        mapAssessmentToActivity(session)
      );
      allActivities.push(...mappedAssessments);
    }

    return allActivities;
  }, [todaysSessions, todaysAssessments, mapAssessmentToActivity, mapSessionToTitle]);

  // Dane statystyczne dla Key Stats
  const stats = [
    { name: 'Listening', value: 76 },
    { name: 'Reading', value: 67 },
    { name: 'Fluency', value: 70 },
    { name: 'Focusing', value: 24 },
  ];

  const handleStartActivity = async (activity: DashboardActivity) => {
    if (activity.activityType === 'assessment') {
      // Przekieruj do wykonania testu
      navigate(`/dashboard/assessments/${activity.assessment_id}/session/${activity.session_id}`);
    } else if (activity.game_id) {
      // Zabezpieczenie przed undefined ID
      if (activity.game_id.trim() === '') {
        return;
      }
      // Przekieruj do nowego layoutu gry zamiast starego widoku aktywności
      navigate(`/dashboard/games/exercise/${activity.game_id}`);
    }
  };

  // Funkcja zwracająca odpowiednią ikonę dla danego typu aktywności
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'blend':
        return (
          <div
            key={`icon-${type}`}
            className="w-16 h-16 bg-purple-900 rounded-lg flex items-center justify-center"
          >
            <span key={`icon-text-${type}`} className="text-white font-bold text-xs">
              QUIZ
              <br />
              TIME
            </span>
          </div>
        );
      case 'reading':
        return (
          <div
            key={`icon-${type}`}
            className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center"
          >
            <span key={`icon-text-${type}`} className="text-white font-bold text-xs">
              READ
            </span>
          </div>
        );
      case 'listening':
        return (
          <div
            key={`icon-${type}`}
            className="w-16 h-16 bg-green-600 rounded-lg flex items-center justify-center"
          >
            <span key={`icon-text-${type}`} className="text-white font-bold text-xs">
              LISTEN
            </span>
          </div>
        );
      case 'writing':
        return (
          <div
            key={`icon-${type}`}
            className="w-16 h-16 bg-red-600 rounded-lg flex items-center justify-center"
          >
            <span key={`icon-text-${type}`} className="text-white font-bold text-xs">
              WRITE
            </span>
          </div>
        );
      case 'speaking':
        return (
          <div
            key={`icon-${type}`}
            className="w-16 h-16 bg-yellow-600 rounded-lg flex items-center justify-center"
          >
            <span key={`icon-text-${type}`} className="text-white font-bold text-xs">
              SPEAK
            </span>
          </div>
        );
      case 'assessment':
        return (
          <div
            key={`icon-${type}`}
            className="w-16 h-16 bg-orange-600 rounded-lg flex items-center justify-center"
          >
            <span key={`icon-text-${type}`} className="text-white font-bold text-xs">
              TEST
            </span>
          </div>
        );
      default:
        return (
          <div
            key={`icon-default`}
            className="w-16 h-16 bg-gray-600 rounded-lg flex items-center justify-center"
          >
            <span key={`icon-text-default`} className="text-white font-bold text-xs">
              TASK
            </span>
          </div>
        );
    }
  };

  const {
    globalStats: _globalStats,
    isLoading: teacherDashboardLoading,
    isError,
    error,
  } = useStudentDashboardData();

  // Show error state
  if (isError) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-semibold">Overview</h1>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">
            Error loading dashboard data: {error?.message || 'Unknown error'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-screen-xl mx-auto p-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Lewa kolumna - Today's Exercises - tylko dla studentów */}
        {isStudent && (
          <div className="md:col-span-2 space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">Today&apos;s Exercises</h2>

            {teacherDashboardLoading ? (
              <div key="loading-container" className="flex justify-center py-8">
                <div
                  key="loading-spinner"
                  className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-teal-700"
                ></div>
              </div>
            ) : activities.length === 0 ? (
              <div
                key="no-activities-container"
                className="bg-white rounded-lg shadow-sm border border-gray-100 p-6 text-center"
              >
                <p key="no-activities-text" className="text-gray-500">
                  No activities available for today.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Teraz activity.id to game_id */}
                {activities.map(activity => (
                  <div
                    key={activity.id}
                    className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden"
                  >
                    <div className="flex p-6 items-center">
                      <div className="flex-shrink-0 mr-6">{getActivityIcon(activity.type)}</div>
                      <div className="flex-1">
                        <h3 className="text-lg font-bold text-gray-800">{activity.title}</h3>
                        <p className="text-sm text-gray-500">{activity.description}</p>
                      </div>
                      <button
                        onClick={() => handleStartActivity(activity)}
                        className="bg-[#005773] cursor-pointer hover:bg-[#00425a] text-white py-3 px-6 rounded-md text-sm font-medium transition-colors duration-200 uppercase"
                      >
                        {activity.activityType === 'assessment' ? 'Start Test' : 'Start Now'}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Prawa kolumna - Key Stats */}
        <div className="md:col-span-1">
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-800 mb-8">Key stats</h2>
            <div className="grid grid-cols-2 gap-8">
              {stats.map(stat => (
                <div key={stat.name} className="flex flex-col items-center">
                  <div className="relative w-24 h-24">
                    <svg width="100%" height="100%" viewBox="0 0 100 100">
                      <circle
                        key={`${stat.name}-bg-circle`}
                        cx="50"
                        cy="50"
                        r="45"
                        fill="none"
                        stroke="#E5E7EB"
                        strokeWidth="10"
                      />
                      <circle
                        key={`${stat.name}-progress-circle`}
                        cx="50"
                        cy="50"
                        r="45"
                        fill="none"
                        stroke={stat.name === 'Focusing' ? '#EF4444' : '#4DB6AC'}
                        strokeWidth="10"
                        strokeDasharray={`${stat.value * 2.83}, 283`}
                        strokeLinecap="round"
                        transform="rotate(-90 50 50)"
                      />
                      <text
                        key={`${stat.name}-text`}
                        x="50"
                        y="55"
                        textAnchor="middle"
                        fontSize="20"
                        fontWeight="bold"
                        fill="#1F2937"
                      >
                        {stat.value}%
                      </text>
                    </svg>
                  </div>
                  <span className="mt-2 text-sm font-medium text-gray-700">{stat.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
