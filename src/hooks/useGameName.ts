import { useEffect } from 'react';
import { useInterventionStore } from '../store/interventionStore';

/**
 * Hook to get a single game name by game ID
 * @param gameId - The game ID to look up
 * @returns The formatted game name
 */
export const useGameName = (gameId: string): string => {
  const interventionStore = useInterventionStore();

  // Auto-fetch interventions if not loaded
  useEffect(() => {
    if (!interventionStore.isLoaded && !interventionStore.isLoading) {
      interventionStore.fetchInterventions();
    }
  }, [interventionStore]);

  return interventionStore.getGameNameById(gameId);
};

/**
 * Hook to get multiple game names by game IDs
 * @param gameIds - Array of game IDs to look up
 * @returns Object mapping game IDs to their formatted names
 */
export const useGameNames = (gameIds: string[]): Record<string, string> => {
  const interventionStore = useInterventionStore();

  // Auto-fetch interventions if not loaded
  useEffect(() => {
    if (!interventionStore.isLoaded && !interventionStore.isLoading) {
      interventionStore.fetchInterventions();
    }
  }, [interventionStore]);

  const gameNames: Record<string, string> = {};

  for (const gameId of gameIds) {
    gameNames[gameId] = interventionStore.getGameNameById(gameId);
  }

  return gameNames;
};

/**
 * Hook to get a game name using intervention ID and game ID (preferred method)
 * This provides more accurate mapping when intervention context is available
 * @param interventionId - The intervention ID for context
 * @param gameId - The game ID to look up
 * @returns The formatted game name
 */
export const useGameNameByInterventionId = (interventionId: string, gameId: string): string => {
  const interventionStore = useInterventionStore();

  // Auto-fetch interventions if not loaded
  useEffect(() => {
    if (!interventionStore.isLoaded && !interventionStore.isLoading) {
      interventionStore.fetchInterventions();
    }
  }, [interventionStore]);

  return interventionStore.getGameNameByInterventionId(interventionId, gameId);
};
