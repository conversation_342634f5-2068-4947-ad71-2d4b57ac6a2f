import React from 'react';
import { EntryType } from '../types';
import ellipse11 from '../../../assets/Ellipse11.svg';
import ellipse12 from '../../../assets/Ellipse12.svg';

interface ChatEntrySelectorProps {
  selected: EntryType | null;
  onSelect: (type: EntryType) => void;
  loading: boolean;
}

export const ChatEntrySelector: React.FC<ChatEntrySelectorProps> = ({
  selected,
  onSelect,
  loading,
}) => (
  <div className="flex gap-6 justify-center my-8">
    <div className="relative">
      <img
        src={ellipse11}
        alt=""
        className="
          absolute
          top-[44px]
          left-[-6px]
          w-[20px] h-[20px]
         
          z-0
        "
      />
      <img
        src={ellipse12}
        alt=""
        className="
          absolute
          top-[62px]
          left-[-10px]    
          w-[10px] h-[10px]
          z-0
        "
      />
      <button
        className={`
          relative z-10
          px-6 py-4 rounded-xl border transition-all shadow-sm
          hover:border-[#005773]
          ${
            selected === 'know'
              ? 'border-2 border-[#005773] bg-white font-semibold shadow-lg'
              : 'border-gray-200 bg-gray-50 hover:bg-white'
          }
          hover:border-2 hover:border-[#005773] hover:bg-white
        `}
        onClick={() => onSelect('know')}
        disabled={loading}
      >
        I Know What I Need
      </button>
    </div>
    <div className="relative">
      <img
        src={ellipse11}
        alt=""
        className="
          absolute
          top-[44px]
          left-[-6px]
          w-[20px] h-[20px]
         
          z-0
        "
      />
      <img
        src={ellipse12}
        alt=""
        className="
          absolute
          top-[62px]
          left-[-10px]    
          w-[10px] h-[10px]
          z-0
        "
      />
      <button
        className={`relative z-10 px-6 py-4 rounded-xl border transition-all shadow-sm hover:border-[#005773]  ${
          selected === 'quick'
            ? 'border-2 border-[#005773] bg-white font-semibold shadow-lg'
            : 'border-gray-200 bg-gray-50 hover:bg-white'
        }
      hover:border-2 hover:border-[#005773] hover:bg-white
      `}
        onClick={() => onSelect('quick')}
        disabled={loading}
      >
        Not Sure? Take a Quick Assessment
      </button>
    </div>
    <div className="relative">
      <img
        src={ellipse11}
        alt=""
        className="
          absolute
          top-[44px]
          left-[-6px]
          w-[20px] h-[20px]
         
          z-0
        "
      />
      <img
        src={ellipse12}
        alt=""
        className="
          absolute
          top-[62px]
          left-[-10px]    
          w-[10px] h-[10px]
          z-0
        "
      />
    </div>
    <div className="relative">
      <img
        src={ellipse11}
        alt=""
        className="
          absolute
          top-[44px]
          left-[-6px]
          w-[20px] h-[20px]
         
          z-0
        "
      />
      <img
        src={ellipse12}
        alt=""
        className="
          absolute
          top-[62px]
          left-[-10px]    
          w-[10px] h-[10px]
          z-0
        "
      />
      <button
        className={`relative z-10 px-6 py-4 rounded-xl border transition-all shadow-sm hover:border-[#005773] ${
          selected === 'survey'
            ? 'border-2 border-[#005773] bg-white font-semibold shadow-lg'
            : 'border-gray-200 bg-gray-50 hover:bg-white'
        }
        hover:border-2 hover:border-[#005773] hover:bg-white
      `}
        onClick={() => onSelect('survey')}
        disabled={loading}
      >
        <div className="flex items-center">
          <span>Parent/Teacher? Answer a Few Questions</span>
        </div>
      </button>
    </div>
  </div>
);

export default ChatEntrySelector;
