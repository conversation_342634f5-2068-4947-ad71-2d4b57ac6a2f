import React, { useState, useEffect } from 'react';
import { GameRendererProps } from '../types';
import { MathTask } from '../types';
import VisualObjects from '@games/math-game/VisualObjects';
import { Calculator, Check, X, Shuffle } from 'lucide-react';

const MathRenderer: React.FC<GameRendererProps> = props => {
  const {
    task,
    onAnswer,
    showFeedback = false,
    userAnswer = null,
    currentTaskIndex = 0,
    totalTasks = 1,
    stats = {
      correctAnswers: 0,
      incorrectAnswers: 0,
      totalQuestions: 1,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: '',
      studentId: '',
    },
  } = props;

  const mathTask = task as MathTask;

  const [inputValue, setInputValue] = useState<string>('');
  const [crossedCount, setCrossedCount] = useState(0);

  const isCorrect = userAnswer === mathTask.equation.result;

  useEffect(() => {
    if (showFeedback) {
      const timer = setTimeout(() => {
        setCrossedCount(mathTask.visualObjects.subtract);
      }, 500);
      return () => clearTimeout(timer);
    } else {
      setCrossedCount(0);
      setInputValue('');
    }
  }, [showFeedback, mathTask.visualObjects.subtract]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const numValue = parseInt(inputValue);
    if (!isNaN(numValue)) {
      onAnswer(numValue === mathTask.equation.result, numValue);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="w-full mb-8">
        <div className="flex items-center justify-center mb-4">
          <Shuffle className="w-8 h-8 text-blue-600 mr-2" />
          <h1 className="text-3xl md:text-4xl font-bold text-blue-700">Math Quiz</h1>
        </div>

        <div className="flex justify-between items-center px-2">
          <div className="text-sm md:text-base text-gray-700 font-medium">
            Question: <span className="text-blue-700">{currentTaskIndex + 1}</span> / {totalTasks}
          </div>

          <div className="bg-gradient-to-r from-blue-500 to-blue-700 rounded-full h-2 w-1/2 mx-2">
            <div
              className="bg-gradient-to-r from-blue-300 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${((currentTaskIndex + 1) / totalTasks) * 100}%` }}
            ></div>
          </div>

          <div className="text-sm md:text-base text-gray-700 font-medium">
            Score: <span className="text-blue-700">{stats.correctAnswers}</span>
          </div>
        </div>
      </div>

      <div className="w-full p-6 rounded-xl bg-white shadow-lg transition-all duration-500 ease-in-out">
        <div className="mb-8">
          <VisualObjects
            objectType={mathTask.visualObjects.objectType}
            count={mathTask.visualObjects.total}
            crossedOut={crossedCount}
            className="mb-6 py-3"
          />

          <div className="bg-blue-50 p-6 rounded-xl mb-6 border border-blue-100">
            <div className="flex items-center justify-center text-4xl font-bold gap-4 py-2">
              <div className="flex items-center justify-center w-16 h-16 rounded-xl bg-white shadow-md border border-gray-100">
                <span className="text-blue-800">{mathTask.equation.num1}</span>
              </div>
              <span className="text-blue-800">−</span>
              <div className="flex items-center justify-center w-16 h-16 rounded-xl bg-white shadow-md border border-gray-100">
                <span className="text-blue-800">{mathTask.equation.num2}</span>
              </div>
              <span className="text-blue-800">=</span>
              <div
                className={`flex items-center justify-center w-16 h-16 rounded-xl shadow-md ${
                  showFeedback
                    ? isCorrect
                      ? 'bg-blue-50 border-2 border-blue-400'
                      : 'bg-red-50 border-2 border-red-400'
                    : 'bg-white border border-gray-100'
                } transition-all duration-300`}
              >
                {showFeedback ? (
                  <span className="text-2xl font-bold text-blue-900">
                    {mathTask.equation.result}
                  </span>
                ) : (
                  <span className="text-2xl text-gray-400">?</span>
                )}
              </div>
            </div>
          </div>
        </div>

        {!showFeedback ? (
          <form onSubmit={handleSubmit} className="flex flex-col items-center">
            <div className="mb-6 w-full max-w-xs">
              <label
                htmlFor="answer"
                className="block text-lg font-medium text-gray-700 mb-3 text-center"
              >
                Your answer:
              </label>
              <div className="relative">
                <input
                  type="number"
                  id="answer"
                  value={inputValue}
                  onChange={e => setInputValue(e.target.value)}
                  className="w-full px-6 py-4 text-2xl text-center rounded-xl border-2 border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-300"
                  placeholder="?"
                  min="0"
                  max="20"
                  autoFocus
                />
              </div>
            </div>

            <button
              type="submit"
              disabled={inputValue === ''}
              className="px-8 py-3 bg-blue-600 text-white font-bold text-lg rounded-xl shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Check Answer
            </button>
          </form>
        ) : (
          <div
            className={`p-6 rounded-xl text-center ${
              isCorrect
                ? 'bg-blue-50 border-2 border-blue-200'
                : 'bg-red-50 border-2 border-red-200'
            }`}
          >
            <div className="flex flex-col items-center">
              <div
                className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
                  isCorrect ? 'bg-blue-100 text-blue-600' : 'bg-red-100 text-red-600'
                }`}
              >
                {isCorrect ? <Check className="w-10 h-10" /> : <X className="w-10 h-10" />}
              </div>
              <h3
                className={`text-xl font-bold mb-2 ${isCorrect ? 'text-blue-800' : 'text-red-800'}`}
              >
                {isCorrect ? 'Correct! 🎉' : 'Not quite'}
              </h3>
              {!isCorrect && (
                <p className="text-lg text-gray-700 mb-4">
                  The correct answer is{' '}
                  <span className="font-bold text-blue-700">{mathTask.equation.result}</span>
                </p>
              )}
              <p className="text-gray-600">
                {isCorrect ? 'Great job! You got it right!' : 'Keep trying, you can do it!'}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MathRenderer;
