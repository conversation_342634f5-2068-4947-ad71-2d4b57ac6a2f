import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { studentService, MyStudentsStatsResponse } from '../studentService';
import { StudentsStats } from '../../store/studenstStore';
import logger from '../../utils/logger';

// Query keys for better cache management
export const studentQueryKeys = {
  all: ['students'] as const,
  lists: () => [...studentQueryKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...studentQueryKeys.lists(), { filters }] as const,
  details: () => [...studentQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...studentQueryKeys.details(), id] as const,
  stats: () => [...studentQueryKeys.all, 'stats'] as const,
  globalStats: () => [...studentQueryKeys.stats(), 'global'] as const,
  myStudentsStats: () => [...studentQueryKeys.stats(), 'myStudents'] as const,
  calendar: (studentId: string) => [...studentQueryKeys.all, 'calendar', studentId] as const,
};

// Hook for fetching global students statistics
export const useStudentsStats = () => {
  return useQuery<StudentsStats>({
    queryKey: studentQueryKeys.globalStats(),
    queryFn: () => studentService.getStudentsStatistics(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Hook for fetching teacher's students statistics
export const useMyStudentsStats = () => {
  return useQuery<MyStudentsStatsResponse>({
    queryKey: studentQueryKeys.myStudentsStats(),
    queryFn: () => studentService.getMyStudentsStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes - more frequent updates for teacher's data
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: true,
    retry: 2,
  });
};

// Hook for fetching students list
export const useStudentsList = () => {
  return useQuery({
    queryKey: studentQueryKeys.lists(),
    queryFn: () => studentService.getStudents(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Hook for fetching student calendar
export const useStudentCalendar = (studentId: string) => {
  return useQuery({
    queryKey: studentQueryKeys.calendar(studentId),
    queryFn: () => studentService.getCalendar(studentId),
    enabled: !!studentId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Mutation for adding a student
export const useAddStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (studentData: Parameters<typeof studentService.addStudent>[0]) =>
      studentService.addStudent(studentData),
    onSuccess: () => {
      logger.log('Student added successfully');
      // Invalidate and refetch students list and stats
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.myStudentsStats() });
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.globalStats() });
    },
    onError: error => {
      logger.error('Error adding student:', error);
    },
  });
};

// Mutation for editing a student
export const useEditStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: number;
      data: Parameters<typeof studentService.editStudent>[1];
    }) => studentService.editStudent(id, data),
    onSuccess: (_, { id }) => {
      logger.log('Student edited successfully');
      // Invalidate specific student and related queries
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.myStudentsStats() });
    },
    onError: error => {
      logger.error('Error editing student:', error);
    },
  });
};

// Mutation for deleting a student
export const useDeleteStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => studentService.deleteStudent(id),
    onSuccess: () => {
      logger.log('Student deleted successfully');
      // Invalidate students list and stats
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.myStudentsStats() });
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.globalStats() });
    },
    onError: error => {
      logger.error('Error deleting student:', error);
    },
  });
};

// Mutation for registering a student by teacher
export const useRegisterStudentByTeacher = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (studentData: Parameters<typeof studentService.registerStudentByTeacher>[0]) =>
      studentService.registerStudentByTeacher(studentData),
    onSuccess: () => {
      logger.log('Student registered by teacher successfully');
      // Invalidate students list and stats
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.myStudentsStats() });
      queryClient.invalidateQueries({ queryKey: studentQueryKeys.globalStats() });
    },
    onError: error => {
      logger.error('Error registering student by teacher:', error);
    },
  });
};

export const useTeacherDashboardData = () => {
  const globalStatsQuery = useStudentsStats();
  const myStudentsStatsQuery = useMyStudentsStats();

  return {
    globalStats: globalStatsQuery.data,
    myStudentsStats: myStudentsStatsQuery.data,
    isLoading: globalStatsQuery.isLoading || myStudentsStatsQuery.isLoading,
    isError: globalStatsQuery.isError || myStudentsStatsQuery.isError,
    error: globalStatsQuery.error || myStudentsStatsQuery.error,
    refetch: () => {
      globalStatsQuery.refetch();
      myStudentsStatsQuery.refetch();
    },
  };
};

export const useStudentDashboardData = () => {
  const globalStatsQuery = useStudentsStats();

  return {
    globalStats: globalStatsQuery.data,
    isLoading: globalStatsQuery.isLoading,
    isError: globalStatsQuery.isError,
    error: globalStatsQuery.error,
    refetch: () => {
      globalStatsQuery.refetch();
    },
  };
};
