import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { GameStats } from '@games/common/types';
import logger from '../utils/logger';

interface GameStatsState {
  gameStats: Record<string, GameStats[]>;
  addGameStats: (stats: GameStats) => void;
  getGameStats: (gameId: string, studentId: string) => GameStats[];
  getLatestGameStats: (gameId: string, studentId: string) => GameStats | null;
}

export const useGameStatsStore = create<GameStatsState>()(
  persist(
    (set, get) => ({
      gameStats: {},

      addGameStats: (stats: GameStats) => {
        // Upewnij się, że studentId jest stringiem
        const studentIdStr = String(stats.studentId);
        const key = `${stats.gameId}_${studentIdStr}`;

        set(state => {
          const existingStatsArrayForKey = state.gameStats[key] || [];
          const updatedStatsArray = [...existingStatsArrayForKey];

          const existingRecordIndex = updatedStatsArray.findIndex(
            es => es.startTime === stats.startTime
          );

          if (existingRecordIndex !== -1) {
            logger.log(`[GameStatsStore] Updating existing stats at index ${existingRecordIndex}`);
            updatedStatsArray[existingRecordIndex] = {
              ...stats,
              studentId: studentIdStr, // Zapewniamy spójność typu
            };
          } else {
            logger.log(`[GameStatsStore] Adding new stats to array`);
            updatedStatsArray.push({
              ...stats,
              studentId: studentIdStr, // Zapewniamy spójność typu
            });
          }

          return {
            gameStats: {
              ...state.gameStats,
              [key]: updatedStatsArray,
            },
          };
        });
      },

      getGameStats: (gameId: string, studentId: string) => {
        const studentIdStr = String(studentId);
        const key = `${gameId}_${studentIdStr}`;
        logger.log(`[GameStatsStore] Getting stats with key: ${key}`);
        const stats = get().gameStats[key] || [];
        logger.log(`[GameStatsStore] Found ${stats.length} stats for key: ${key}`);
        return stats;
      },

      getLatestGameStats: (gameId: string, studentId: string) => {
        const studentIdStr = String(studentId);
        const statsArray = get().getGameStats(gameId, studentIdStr);
        logger.log(
          `[GameStatsStore] getLatestGameStats for ${gameId}_${studentIdStr} - All Stats:`,
          JSON.stringify(statsArray, null, 2)
        );

        if (statsArray.length === 0) {
          logger.log(`[GameStatsStore] No stats found for ${gameId}_${studentIdStr}`);
          return null;
        }

        // Sortujemy statystyki według czasu rozpoczęcia (od najnowszych)
        const sortedStats = [...statsArray].sort((a, b) => b.startTime - a.startTime);
        const latestStat = sortedStats[0];

        logger.log(
          `[GameStatsStore] getLatestGameStats for ${gameId}_${studentIdStr} - Selected Latest:`,
          JSON.stringify(latestStat, null, 2)
        );
        return latestStat;
      },
    }),
    {
      name: 'yubu-game-stats',
    }
  )
);
