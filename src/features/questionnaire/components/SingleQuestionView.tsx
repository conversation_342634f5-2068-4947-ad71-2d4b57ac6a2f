import React from 'react';
import { Question, Answer } from '../types';
import { scaleOptions } from '../data/questions';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { ProgressBar } from './ProgressBar';
interface Props {
  question: Question;
  answer: Answer | undefined;
  onAnswerChange: (id: number, val: number) => void;
  onNext: () => void;
  onPrevious: () => void;
  questionNumber: number;
  totalQuestions: number;
  canGoNext: boolean;
  canGoPrevious: boolean;
}
export const SingleQuestionView: React.FC<Props> = ({
  question,
  answer,
  onAnswerChange,
  onNext,
  onPrevious,
  questionNumber,
  totalQuestions,
  canGoNext,
  canGoPrevious,
}) => {
  const select = (v: number) => {
    onAnswerChange(question.id, v);
    setTimeout(() => onNext(), 600);
  };
  return (
    <div className="min-h-screen flex items-center justify-center px-4 bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="w-full max-w-3xl">
        <ProgressBar current={questionNumber} total={totalQuestions} />
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">{question.text}</h2>
          <div className="space-y-4">
            {scaleOptions.map(opt => (
              <label
                key={opt.value}
                className={`flex items-center p-4 rounded-xl border-2 cursor-pointer ${answer?.value === opt.value ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-gray-50'}`}
              >
                <input
                  type="radio"
                  className="sr-only"
                  name={`q-${question.id}`}
                  value={opt.value}
                  checked={answer?.value === opt.value}
                  onChange={() => select(opt.value)}
                />
                <div
                  className={`w-6 h-6 rounded-full border-2 mr-4 ${answer?.value === opt.value ? 'border-blue-500 bg-blue-500' : 'border-gray-300'}`}
                />
                <span
                  className={`text-lg font-medium ${answer?.value === opt.value ? 'text-blue-700' : 'text-gray-700'}`}
                >
                  {opt.label}
                </span>
              </label>
            ))}
          </div>
        </div>
        <div className="flex justify-between items-center">
          <button
            disabled={!canGoPrevious}
            onClick={onPrevious}
            className={`flex items-center px-6 py-3 rounded-xl font-medium ${canGoPrevious ? 'bg-gray-100 hover:bg-gray-200' : 'bg-gray-50 text-gray-400'}`}
          >
            {' '}
            <ChevronLeft className="w-5 h-5 mr-2" /> Previous{' '}
          </button>
          <button
            disabled={!canGoNext}
            onClick={onNext}
            className={`flex items-center px-6 py-3 rounded-xl font-medium ${canGoNext ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-300 text-gray-500'}`}
          >
            {' '}
            {questionNumber === totalQuestions ? 'Finish' : 'Next'}{' '}
            <ChevronRight className="w-5 h-5 ml-2" />
          </button>
        </div>
      </div>
    </div>
  );
};
