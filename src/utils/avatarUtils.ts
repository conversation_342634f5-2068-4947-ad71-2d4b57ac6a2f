export interface AvatarDownloadOptions {
  format?: 'glb' | 'gltf';
  quality?: 'low' | 'medium' | 'high';
  pose?: 'A' | 'T';
  morphTargets?: string[];
  textureAtlas?: 'none' | '256' | '512' | '1024';
}

export const downloadAvatar = async (
  avatarUrl: string,
  options: AvatarDownloadOptions = {}
): Promise<Blob> => {
  const { format = 'glb', quality = 'medium', pose = 'A', textureAtlas = '512' } = options;

  const downloadUrl = new URL(avatarUrl);
  downloadUrl.searchParams.set('format', format);
  downloadUrl.searchParams.set('quality', quality);
  downloadUrl.searchParams.set('pose', pose);
  if (textureAtlas !== 'none') {
    downloadUrl.searchParams.set('textureAtlas', textureAtlas);
  }

  try {
    const response = await fetch(downloadUrl.toString());

    if (!response.ok) {
      throw new Error(`Błąd pobierania awatara: ${response.status} ${response.statusText}`);
    }

    return await response.blob();
  } catch (error) {
    console.error('Błąd podczas pobierania awatara:', error);
    throw error;
  }
};

export const saveAvatarLocally = (blob: Blob, filename?: string): void => {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename || `avatar_${Date.now()}.glb`;

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};

export const getAvatarId = (avatarUrl: string): string | null => {
  try {
    const url = new URL(avatarUrl);
    const pathParts = url.pathname.split('/');
    // URL Ready Player Me ma format: https://models.readyplayer.me/[id].glb
    const filename = pathParts[pathParts.length - 1];
    return filename.replace(/\.(glb|gltf)$/, '');
  } catch (error) {
    console.error('Błąd podczas wyciągania ID awatara:', error);
    return null;
  }
};

export const generateAvatarFilename = (avatarUrl: string, format: string = 'glb'): string => {
  const avatarId = getAvatarId(avatarUrl);
  const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
  return `avatar_${avatarId || 'unknown'}_${timestamp}.${format}`;
};
