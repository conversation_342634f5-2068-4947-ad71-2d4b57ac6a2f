import React from 'react';
import { GameRendererProps, GameTask } from './types';
import gameRegistry from './GameRegistry';

const GameRenderer: React.FC<GameRendererProps> = props => {
  const { task } = props;

  const Renderer = gameRegistry.getRenderer(task.type);

  if (Renderer) {
    return <Renderer {...props} task={task} />;
  }

  return (
    <div className="p-4 bg-red-100 text-red-800 rounded-md">
      <h3 className="font-bold">Unsupported Game Type</h3>
      <p>The game type &quot;{(task as GameTask).type}&quot; is not supported.</p>
      <p className="text-center text-red-500 mt-4">
        Error: &quot;No renderer found for game type {(task as GameTask).type}&quot;. Please check
        if the renderer is registered.
      </p>
    </div>
  );
};

export default GameRenderer;
