import Game from './Game';
import Game<PERSON>ender<PERSON> from './GameRenderer';
import gameRegistry from './GameRegistry';
import gameFactory from './GameFactory';

import * as gameData from './data';

import {
  BaseTask,
  GameTask,
  GameProps,
  GameRendererProps,
  GameState,
  BlendTask,
  MathTask,
  MemoryTask,
  MemoryTask1,
  MemoryTask2,
} from './types';

import {
  <PERSON><PERSON>d<PERSON><PERSON><PERSON>,
  MathRenderer,
  MemoryRenderer,
  MemoryRenderer1,
  MemoryRenderer2,
} from './renderers';
import BaseRenderer from './renderers/BaseRenderer';

export {
  Game,
  GameRenderer,
  gameRegistry,
  gameFactory,
  BaseRenderer,
  BlendRenderer,
  MathRenderer,
  MemoryRenderer,
  MemoryRenderer1,
  MemoryRenderer2,
  gameData,
};

export type {
  BaseTask,
  GameTask,
  GameProps,
  GameRendererProps,
  GameState,
  BlendTask,
  MathTask,
  MemoryTask,
  MemoryTask1,
  MemoryTask2,
};
