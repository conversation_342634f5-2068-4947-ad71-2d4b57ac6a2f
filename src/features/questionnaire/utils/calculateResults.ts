import { Answer, QuestionnaireResults } from '../types';

export const calculateResults = (answers: Answer[]): QuestionnaireResults => {
  const totalScore = answers.reduce((sum, a) => sum + a.value, 0);
  const maxScore = answers.length * 6;
  const percentage = Math.round((totalScore / maxScore) * 100);

  let interpretation = '';
  if (percentage <= 30) interpretation = 'Low frequency behaviors - Generally within typical range';
  else if (percentage <= 60)
    interpretation = 'Moderate frequency behaviors - May benefit from monitoring';
  else interpretation = 'High frequency behaviors - Consider professional consultation';

  return { totalScore, maxScore, percentage, interpretation };
};
