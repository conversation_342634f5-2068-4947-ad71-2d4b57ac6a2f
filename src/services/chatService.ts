import logger from 'src/utils/logger';
import { api } from './api';

const API_URL = '/api/chat';
const WS_URL = 'wss://api.dev.yubu.ai/chat/ws/chat';
const PROXY_STREAM_NORMAL_URL = 'https://api.dev.yubu.ai/chat/proxy-stream-normal';
const CHAT_HISTORY = 'https://api.dev.yubu.ai/chat/history';

export interface ChatStreamPayload {
  session_id?: string;
  domains?: string[];
  message?: string;
  question?: string;
  [key: string]: unknown;
}

export interface ChatStreamChunk {
  id?: number;
  domain?: string;
  type?: string;
  title?: string;
  exercise?: string;
  description?: string;
  exerciseTime?: string;
  imageUrl?: string;
  subskill?: string;
  [key: string]: unknown;
}

const getToken = () => {
  const cookies = document.cookie.split(';');
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'access_token') {
      return value;
    }
  }
  return localStorage.getItem('access_token');
};

export interface Message {
  id: number;
  text: string;
  sender: 'user' | 'system' | 'bot' | 'bot_partial';
  timestamp: string;
  type: 'text' | 'survey' | 'assessment' | 'support';
  data?: Record<string, unknown> | Array<Record<string, unknown>>;
  isStreaming?: boolean;
  role?: string; // For compatibility with the new streaming API
}

export interface ReadingAreaDetails {
  title: string;
  content: {
    learningObjective: string;
    exercises: {
      title: string;
      description: string;
      instructions?: string;
      images?: string[];
    }[];
  };
}

interface ApiResponse<T = unknown> {
  data: T;
  status: number;
}

// Funkcja do pobierania wiadomości z czatu
const getMessagesFn = async (userId: string, chatId: string): Promise<Message[]> => {
  try {
    const response = await api.get(`${API_URL}/messages?user_id=${userId}&chat_id=${chatId}`);
    const data = response.data as { messages: Message[] };
    return data.messages;
  } catch {
    return [];
  }
};

// Zmienne i funkcje do obsługi połączenia WebSocket
let webSocketInstance: WebSocket | null = null;
let messageHandler: ((message: Message) => void) | null = null;
let connectionChangeHandler: ((connected: boolean, reconnecting: boolean) => void) | null = null;
let reconnectTimeout: ReturnType<typeof setTimeout> | null = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 3000;

// Zmienne potrzebne dla funkcji attemptReconnect
let userId: string | number = '';
let chatId: string | number = '';

// Funkcja do próby ponownego połączenia WebSocket
const attemptReconnect = () => {
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
  }

  reconnectAttempts++;

  if (connectionChangeHandler) {
    connectionChangeHandler(false, true);
  }

  reconnectTimeout = setTimeout(() => {
    if (messageHandler) {
      connectWebSocketFn(userId, chatId, messageHandler, connectionChangeHandler || undefined);
    }
  }, RECONNECT_INTERVAL);
};

// Funkcja do nawiązywania połączenia WebSocket
const connectWebSocketFn = (
  userIdParam: number | string,
  chatIdParam: number | string,
  onMessage: (message: Message) => void,
  onConnectionChange?: (connected: boolean, reconnecting: boolean) => void
) => {
  if (webSocketInstance) {
    return;
  }

  // Zapisujemy ID użytkownika i czatu do zmiennych globalnych dla funkcji attemptReconnect
  userId = userIdParam;
  chatId = chatIdParam;

  messageHandler = onMessage;
  connectionChangeHandler = onConnectionChange || null;

  const token = getToken();
  if (!token) {
    logger.error('No authentication token');
    return;
  }

  const url = `${WS_URL}?user_id=${userId}&chat_id=${chatId}`;
  webSocketInstance = new WebSocket(url);

  webSocketInstance.onopen = () => {
    reconnectAttempts = 0;
    if (connectionChangeHandler) {
      connectionChangeHandler(true, false);
    }
  };

  webSocketInstance.onmessage = event => {
    try {
      const message = JSON.parse(event.data);
      if (messageHandler) {
        messageHandler(message);
      }
    } catch {
      return;
    }
  };

  webSocketInstance.onclose = () => {
    webSocketInstance = null;

    if (connectionChangeHandler) {
      connectionChangeHandler(false, reconnectAttempts > 0);
    }

    // Próba ponownego połączenia
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      attemptReconnect();
    }
  };

  webSocketInstance.onerror = () => {
    return;
  };
};

// Funkcja do rozłączania WebSocket
const disconnectWebSocketFn = () => {
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
    reconnectTimeout = null;
  }

  if (webSocketInstance) {
    webSocketInstance.close();
    webSocketInstance = null;
  }

  messageHandler = null;
  connectionChangeHandler = null;
  reconnectAttempts = 0;
};

// Funkcja do wysyłania wiadomości przez WebSocket
const sendWebSocketMessageFn = (message: Record<string, unknown>) => {
  if (!webSocketInstance || webSocketInstance.readyState !== WebSocket.OPEN) {
    return false;
  }

  webSocketInstance.send(JSON.stringify(message));
  return true;
};

// Funkcja do wysyłania wiadomości przez API
const sendMessageFn = async (
  userId: string | number,
  chatId: string | number,
  message: string
): Promise<boolean> => {
  try {
    const response = (await api.post(`${API_URL}/send`, {
      user_id: userId,
      chat_id: chatId,
      message,
    })) as ApiResponse;

    return response.status === 200;
  } catch {
    return false;
  }
};

/**
 * Wysyła pytanie do czatu i obsługuje streaming odpowiedzi
 * @param payload Dane do wysłania
 * @param onChunk Callback dla każdego fragmentu tekstu
 * @param onFinish Callback po zakończeniu streamingu
 * @param processChunk Opcjonalny callback do przetwarzania fragmentów
 */
const sendChatQuestionFn = async (
  payload: ChatStreamPayload,
  onChunk: (text: string) => void,
  onFinish: () => void
) => {
  const token = getToken();
  if (!token) {
    logger.error('No authentication token');
    return;
  }

  try {
    const response = await fetch(PROXY_STREAM_NORMAL_URL, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`HTTP error: ${response.status}`);
    }

    if (!response.body) {
      onFinish();
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');

    // Handle response streaming
    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        onFinish();
        break;
      }

      // Dekodowanie fragmentu
      const textChunk = decoder.decode(value, { stream: true });

      // Przekazanie surowego fragmentu do callbacka
      onChunk(textChunk);
    }
  } catch {
    onFinish();
  }
};

/**
 * Pobiera listę istniejących sesji czatu dla użytkownika
 * @returns Promise z listą sesji lub pustą tablicę w przypadku błędu
 */
const getSessionsFn = async (): Promise<
  Array<{
    session_id: string;
    student_id: number;
    document_id?: number;
    age?: number;
    grade_level?: string | null;
    diagnosis?: string | null;
    created_at: string;
  }>
> => {
  const token = getToken();
  if (!token) {
    logger.error('No authentication token');
    return [];
  }

  try {
    const response = await fetch('https://api.dev.yubu.ai/chat/my-sessions', {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error: ${response.status}`);
    }

    const data = await response.json();
    return data || [];
  } catch {
    return [];
  }
};

/**
 * Rozpoczyna nową sesję czatu dla danego studenta i opcjonalnie dla dokumentu
 * @param studentId Identyfikator studenta
 * @param documentId Opcjonalny identyfikator dokumentu
 * @returns Promise z identyfikatorem sesji lub null w przypadku błędu
 */
const startSessionFn = async (
  studentId: string | number,
  documentId?: string | number
): Promise<string | null> => {
  const token = getToken();
  if (!token) {
    logger.error('No authentication token');
    return null;
  }

  try {
    const payload: { student_id: string | number; document_id?: number } = {
      student_id: studentId,
    };

    // Jeśli podano document_id, dodaj go do payloadu
    if (documentId) {
      payload.document_id = Number(documentId);
    }

    logger.info('Tworzenie nowej sesji z payloadem:', payload);

    const response = await fetch('https://api.dev.yubu.ai/chat/start-session', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`HTTP error: ${response.status}`);
    }

    const data = await response.json();
    return data.session_id || null;
  } catch {
    return null;
  }
};

/**
 * Pobiera obszary czytania dla danego poziomu klasy
 */
const getReadingAreasFn = async (
  grade?: string
): Promise<{
  reading_areas: Array<{ id: number; name: string; description: string; grade_levels?: string[] }>;
}> => {
  try {
    const response = await api.get(`/api/reading-areas${grade ? `?grade=${grade}` : ''}`);
    return response.data as {
      reading_areas: Array<{
        id: number;
        name: string;
        description: string;
        grade_levels?: string[];
      }>;
    };
  } catch {
    return { reading_areas: [] };
  }
};

/**
 * Pobiera historię czatu dla danej sesji
 * @param sessionId Identyfikator sesji
 * @returns Promise z tablicą wiadomości
 */
const getChatHistoryFn = async (sessionId: string): Promise<Message[]> => {
  const token = getToken();
  if (!token) {
    logger.error('No authentication token');
    return [];
  }

  try {
    const response = await fetch(CHAT_HISTORY, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        session_id: sessionId,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error: ${response.status}`);
    }

    const data = await response.json();
    return data || [];
  } catch {
    return [];
  }
};

/**
 * Pobiera sesje czatu dla konkretnego dokumentu
 * @param documentId Identyfikator dokumentu
 * @returns Promise z listą sesji dla danego dokumentu lub pustą tablicę w przypadku błędu
 */
const getSessionsForDocumentFn = async (
  documentId: string | number
): Promise<
  Array<{
    session_id: string;
    student_id: number;
    document_id?: number;
    created_at: string;
  }>
> => {
  const allSessions = await getSessionsFn();
  const numericDocumentId = Number(documentId);

  // Filtrujemy sesje, które mają document_id równy podanemu documentId
  return allSessions.filter(
    session => session.document_id && session.document_id === numericDocumentId
  );
};

export const chatService = {
  getLastActiveStudents: async () => {
    const response = await api.get('/chat/last_active_students');
    return response.data;
  },
  getMessages: getMessagesFn,
  connectWebSocket: connectWebSocketFn,
  disconnectWebSocket: disconnectWebSocketFn,
  sendWebSocketMessage: sendWebSocketMessageFn,
  sendMessage: sendMessageFn,
  sendChatQuestion: sendChatQuestionFn,
  getSessions: getSessionsFn,
  getSessionsForDocument: getSessionsForDocumentFn,
  startSession: startSessionFn,
  getReadingAreas: getReadingAreasFn,
  getChatHistory: getChatHistoryFn,
};
