# Game Engine Architecture

## Core Components

### Game Engine

The core engine (`Game.tsx`) handles:

- Game state management
- Task progression
- Answer validation
- Statistics tracking
- WebSocket communication for real-time updates

### Renderers

Each game type has a dedicated renderer that:

- Implements game-specific UI and interactions
- Processes user input
- Manages game-specific state
- Communicates with the core engine via standardized interfaces

### Memory Game Implementation

The Memory Game demonstrates the engine's flexibility:

1. **State Management**:

   - Cards state (flipped, matched)
   - Task progression across multiple levels
   - Score tracking per completed level

2. **Scoring System**:

   - Each completed level counts as one correct answer
   - Total of 3 levels (totalQuestions = 3)
   - Completion percentage calculated as (completedLevels / totalLevels) \* 100

## Common UI Components

### GameResults

The `GameResults` component is a universal component that displays a summary after game completion:

- Shows statistics (correct answers, time, completion percentage)
- Displays detailed task progress
- Includes a "Play Again" button
- Shows motivational messages based on the result

### ProgressBar

The `ProgressBar` component is used to visualize progress in the game:

- Shows current progress as a gradient bar
- Displays information about the number of completed tasks
- Dynamically changes color depending on the completion percentage
- Ensures consistent appearance across all games

## Creating New Games

To add a new game:

1. Create a new renderer extending `BaseRenderer`
2. Implement game-specific UI and logic
3. Register the renderer in `renderers/index.ts`
4. Define game tasks in the appropriate format
5. Register the game in `GameRegistry.ts`

### Requirements for New Games

Each new game **must** meet the following requirements:

1. **Use the GameResults component**:

   ```tsx
   import GameResults from '../common/GameResults';

   // After game completion
   if (gameCompleted && finalStats) {
     return <GameResults stats={finalStats} onRestart={handleRestart} />;
   }
   ```

2. **Use the ProgressBar component** to show progress:

   ```tsx
   import ProgressBar from '../blend-game/ProgressBar';

   // In the game component
   <ProgressBar currentQuestion={currentTaskIndex + 1} totalQuestions={tasks.length} />;
   ```

3. **Properly handle game statistics**:

   - Track correct and incorrect answers
   - Calculate completion percentage
   - Measure game time
   - Save progress for individual tasks

4. **Implement game restart function**:

   ```tsx
   const handleRestart = () => {
     // Reset game state
     setGameCompleted(false);
     setCurrentTaskIndex(0);
     // Other game-specific resets
   };
   ```

5. **Send statistics via WebSocket**:

   ```tsx
   import { gameWebSocket } from '../common/websocket';

   // After game completion
   gameWebSocket.sendGameStats(finalStats);
   ```
