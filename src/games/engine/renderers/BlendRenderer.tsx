import React from 'react';
import { GameRendererProps } from '../types';
import { BlendTask } from '../types';
import BaseRenderer from './BaseRenderer';
import OptionButton from '@games/blend-game/OptionButton';
import Question from '@games/blend-game/Question';
import Feedback from '@games/blend-game/Feedback';
import ProgressBar from '@games/blend-game/ProgressBar';

class BlendRenderer extends BaseRenderer<BlendTask> {
  handleOptionClick = (option: string) => {
    const { onAnswer, showFeedback } = this.props;
    const blendTask = this.props.task as BlendTask;

    if (showFeedback) return;

    const correct = option === blendTask.answer;

    onAnswer(correct, option);
  };

  isAnswerCorrect(userAnswer: string): boolean {
    const blendTask = this.props.task as BlendTask;
    return userAnswer === blendTask.answer;
  }

  renderFeedback(): React.ReactNode {
    return null;
  }

  renderContent(): React.ReactNode {
    const blendTask = this.props.task as BlendTask;
    const {
      showFeedback,
      userAnswer,
      isCorrect,
      currentTaskIndex = 0,
      totalTasks = 10,
    } = this.props;

    return (
      <div className="max-w-4xl mx-auto p-6 bg-white rounded-3xl shadow-lg relative">
        <div className="mb-6">
          <ProgressBar currentQuestion={currentTaskIndex + 1} totalQuestions={totalTasks} />
        </div>

        {showFeedback && <Feedback isCorrect={isCorrect || false} onComplete={() => {}} />}

        <Question
          image={blendTask.image}
          word={blendTask.word}
          isCorrect={isCorrect}
          showFeedback={showFeedback}
        />

        <div className="grid grid-cols-2 gap-4 mt-8">
          {blendTask.options.map((option, index) => (
            <OptionButton
              key={index}
              option={option}
              onClick={() => this.handleOptionClick(option)}
              isSelected={userAnswer === option}
              isCorrect={userAnswer === option ? isCorrect : undefined}
              disabled={showFeedback || false}
            />
          ))}
        </div>
      </div>
    );
  }
}

export default BlendRenderer;
