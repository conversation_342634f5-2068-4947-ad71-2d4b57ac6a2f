# API Documentation – `chat/` Module

**Prefix for all endpoints:** `/chat`

**General Description:**  
This API module supports student reading assessment and selection of support areas by teachers. It includes endpoints for submitting educator surveys, quick assessments, and selecting personalized learning areas.

---

## 1. POST `/chat/educator_survey`

**Description**: Submit a survey regarding a student's reading difficulties.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only users with the `teacher` role can submit educator surveys.
- **Helpful**: You can fetch available survey options (frequencies and attitudes) via `GET /chat/educator_survey/options`.

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "student_id": 123,
  "difficulties": "Difficulty focusing on texts",
  "frequency": "Often",
  "strategies": "Uses highlighting and repeated reading",
  "attitude": "Positive"
}
```

### Responses:

- `200 OK`:

```json
{
  "message": "Survey submitted successfully"
}
```

- `403 Forbidden`: Access denied.
- `500 Internal Server Error`: Failed to save educator survey.

---

## 2. POST `/chat/know_what_i_need`

**Description**: Save selected areas where the student needs reading support, including grade level and age.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only users with the `teacher` role can submit reading support selections.
- Student's age is automatically fetched from the database and used in the generated prompt.
- **Helpful**: You can fetch available reading areas via `GET /chat/reading_areas`.

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "student_id": 123,
  "grade_level": "3rd Grade",
  "areas": ["Phonemic Awareness", "Reading Fluency"]
}
```

### Responses:

- `200 OK`:

```json
{
  "message": "Selections saved",
  "grade_level": "3rd Grade",
  "area_ids": [1, 3],
  "generated_prompt": "My student is 9 years old, in 3rd Grade and needs help with the following areas of reading: Phonemic Awareness and Reading Fluency. What instructional strategies or exercises would best support improvement in these areas?"
}
```

- `403 Forbidden`: Access denied.
- `404 Not Found`: Student not found.
- `500 Internal Server Error`: Failed to save selections.

---

## 3. POST `/chat/quick_assessment`

**Description**: Submit a quick assessment of student's reading skills.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only users with the `teacher` role can submit quick assessments.
- **Helpful**: You can fetch available assessment areas/questions via `GET /chat/assessment_areas`.

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "student_id": 123,
  "answers": {
    "1": 1,
    "2": 0,
    "3": 1
  }
}
```

_(1 = yes, 0 = no for each question ID)_

### Responses:

- `200 OK`:

```json
{
  "message": "Assessment saved",
  "generated_prompt": "My student has difficulties with recognizing words quickly and reading slowly or stumbling over words. What exercises or strategies can help address these issues?"
}
```

If no difficulties are selected (answers is empty or all values are 0):

```json
{
  "message": "Assessment saved",
  "generated_prompt": "No difficulties selected."
}
```

- `400 Bad Request`: Invalid question ID provided.
- `403 Forbidden`: Access denied.
- `500 Internal Server Error`: Failed to save assessment.

---

## Additional GET Endpoints

These endpoints provide necessary data for the above POST requests:

- `GET /chat/educator_survey/options` → Fetch available educator survey options (frequencies and attitudes).
- `GET /chat/reading_areas` → Fetch available reading support areas.
- `GET /chat/assessment_areas` → Fetch available assessment questions for quick assessment.

---

## 4. WebSocket `/ws/chat`

**Description**:  
Initiates a real-time WebSocket session with the locally loaded language model (LLM) backend.
This endpoint is for internal use – it receives a fully formatted prompt (already constructed by the API layer) and streams the LLM's output token-by-token.

**Notes:**

- Requires a valid access token via query parameter (`?token=<JWT>`).
- Accepts a single string prompt from the client.
- Streams each generated token as plain text; each token is sent in a separate message.
- Ends every generation with the message [[END]].
- Closes automatically if the model is unavailable.

### URL:

```
wss://api.dev.yubu.ai/ws/chat?token=<JWT>
```

### Authorization:

- The token must be passed as a **query parameter**:  
  `?token=eyJhbGciOi...`

### Messages:

- **Client → Server**:  
  A single string prompt to send to the model.
- **Server → Client**:
  - Tokens streamed as plain text.
  - Final message will be `[[END]]`.

### Example Usage:

**Client sends:**

```
Hello, how are you today?
```

**Server streams:**

```
I'm
fine,
thank
you!
[[END]]
```

### Errors:

- `1008 Policy Violation`: Sent if token is missing or invalid.
- `Model not available. Running in mock mode.`: Sent if model is not loaded (e.g., invalid `MODEL_PATH`).

---

## 5. POST `/chat/assign_interventions`

**Description**:  
Assign one or more intervention activities to a student. The assigning user must be a teacher or parent. The backend automatically extracts the assigning user's ID and role from the JWT token.
**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- You should only send `student_id` and `intervention_id` — the system automatically associates the assignment with the current user (teacher or parent).

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
[
  {
    "student_id": 123,
    "intervention_id": "blend-game--word-building"
  },
  {
    "student_id": 124,
    "intervention_id": "memory-game2"
  }
]
```

### Responses:

- `200 OK`:

```json
{
  "detail": "2 intervention assignments received successfully.",
  "assigned_by": {
    "user_id": 8,
    "role": "teacher"
  }
}
```

- `403 Forbidden`: Access denied.
- `400 Bad Request`: Missing or malformed token, or invalid input format (e.g., not a list).

## 6. POST `/chat/view_assigned_interventions`

**Description**:  
Retrieve all interventions assigned to a given student.
Authorization is required, and the requesting user (teacher, parent, or student) must have access to the specified student.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- The response includes all interventions ever assigned to the student, sorted from newest to oldest.

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "student_id": 123
}
```

### Responses:

- `200 OK`:

```json
{
  "student_id": 123,
  "interventions": [
    {
      "id": "e92cf3d4-5e7f-4f63-84fa-307cb9c97e45",
      "intervention_id": "blend-game--word-building",
      "assigned_by_id": 8,
      "assigned_by_role": "teacher",
      "created_at": "2024-05-26T16:13:45.923000"
    },
    {
      "id": "1f04f828-6cbe-49eb-825d-5c2668d86dd2",
      "intervention_id": "memory-game2",
      "assigned_by_id": 12,
      "assigned_by_role": "parent",
      "created_at": "2024-05-25T10:32:12.112000"
    }
  ]
}
```

- `403 Forbidden`: Access denied (the requesting user does not have access to this student).
- `401 Unauthorized:`: Missing or malformed token, or invalid user role.
- `400 Bad Request:`: Bad input data (e.g., missing required fields).

## 7. POST `/chat/proxy-stream-normal` (REST + SSE, chat-style prompt)

**Description**:  
Chat-like educational advice endpoint.
Builds a prompt for the LLM using student data and an explicit user question.
Streams the LLM's chat-style answer (concise sentences) via SSE.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Accepts age, grade, domains, and a free-text question.
- The prompt builder ensures the LLM answers as an educational consultant, without JSON output.
- Streams back each token as a separate data: SSE event.
- When finished, sends a [FULL_RESPONSE] event and [[END]].

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "age": 9,
  "grade": "3rd Grade",
  "domains": ["Reading Fluency", "Comprehension"],
  "question": "What are daily habits to help improve my student's reading confidence?"
}
```

### SSE Streaming Response

Each token streamed as:

```json
data: <token>
```

Each token streamed as:

```json
data: [FULL_RESPONSE]<full_output>
data: [[END]]
```

### SSE Streaming Response Example (SSE, simplified)

```json
data: Reading
data: together
data: daily
data: builds
data: confidence.
data: Encourage
data: praise
data: for
data: progress.
data: [FULL_RESPONSE]Reading together daily builds confidence. Encourage praise for progress.
data: [[END]]
```

- `401 Unauthorized`: Missing or invalid token.
- `data: [ERROR] <error message>`: Streamed in case of internal error.

## 8. POST `/chat/start-session`

**Description**:  
Initiates a new chat or assessment session for a selected student.
Only a teacher who has access to the student may start a session.
The endpoint returns session metadata (including student info), and the session is stored in the chat_sessions table.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only teachers assigned to a student can start a session for that student.
- Student data (such as age, grade level, diagnosis) is included for frontend use.

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "student_id": 123
}
```

### Responses:

- `200 OK`:

```json
{
  "session_id": "16be973b-2377-4d41-9e8d-2395e985c44c",
  "student_id": 123,
  "teacher_id": 88,
  "age": 9,
  "grade_level": "3rd Grade",
  "diagnosis": "Dyslexia",
  "created_at": "2025-05-27T12:14:10.234000"
}
```

- `401 Unauthorized`: Missing or malformed token, or invalid user role.
- `403 Forbidden`: Teacher does not have access to the student.
- `404 Not Found`: The specified student does not exist.
- `500 Internal Server Error`: Unexpected database or server error.

## 9. POST `/chat/history`

**Description**:  
Returns the chat history (user questions and assistant responses) for a specific session.
Only the user who created the session can access its history.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- The response contains a list of user/assistant message pairs in chronological order.
-

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "session_id": "d4253f7e-8eaf-4f59-a324-33c24d7b105a"
}
```

### Responses:

- `200 OK`:

```json
[
  {
    "user_question": "How can I help my student with phonics?",
    "assistant": "You can use word-building games and daily reading practice to improve phonics skills."
  },
  {
    "user_question": "What about reading comprehension?",
    "assistant": "Ask open-ended questions after reading and encourage your student to retell the story in their own words."
  }
]
```

- `401 Unauthorized`: Missing or malformed token, or invalid user role.
- `403 Forbidden`: Teacher does not have access to the student.
- `404 Not Found`: The specified student does not exist.
- `500 Internal Server Error`: Unexpected database or server error.

## 10. GET `/chat/last_active_students`

**Description**:  
Returns a list of up to 10 most recently active students for the authenticated teacher or parent.
For each student, provides the latest session and timestamp of the last message sent in that session.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Returns only students assigned to the requesting user.
- Sorted by most recent activity (last message) descending.

### Headers:

```
Authorization: Bearer <access_token>
```

### Responses:

- `200 OK`:

```json
[
  {
    "student_id": 25,
    "first_name": "Jan",
    "last_name": "Kowalski",
    "session_id": "7dba01a5-5d23-4e41-97d3-0b2e21e51a2c",
    "last_message_time": "2025-05-27T11:58:34.255000"
  },
  {
    "student_id": 18,
    "first_name": "Maria",
    "last_name": "Nowak",
    "session_id": "a1e9c11f-3b7a-4df7-9e2b-6c4c53ae2b8b",
    "last_message_time": "2025-05-26T16:13:45.923000"
  }
  // ...up to 10 results
]
```

- `401 Unauthorized`: Missing or malformed token, or invalid user role.
- `403 Forbidden`
- `500 Internal Server Error`: Unexpected database or server error.

## 10. POST `/chat/todays_sessions

**Description**:
Returns all intervention sessions (planned, in progress, or completed) scheduled for the selected student for today.
Grouping is done by intervention_id, and each entry includes progress, status, and timing for today's sessions only.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- For teachers and parents, you must provide student_id in the request body.
- For students, student_id is resolved automatically from the token.
- All times and dates are in UTC.

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "student_id": 12
}
```

### Responses:

- `200 OK`:

```json
{
  "date": "2025-05-27",
  "weekday": "Tuesday",
  "is_today": true,
  "in_current_month": true,
  "sessions": [
    {
      "intervention_id": "blend-game-quiz",
      "entries": [
        {
          "session_id": "74899a64-0f5f-43fd-a951-ae62c4931c5c",
          "game_id": "blend-game-quiz",
          "total_questions": 6,
          "progress": [
            { "step": 1, "correct": true },
            { "step": 2, "correct": true },
            { "step": 3, "correct": false }
          ],
          "status": "in_progress",
          "duration_seconds": null,
          "still_in_progress": true,
          "correct_percent": 67
        },
        {
          "session_id": "fbac2421-82b6-4fa7-ad58-37fc9368da74",
          "game_id": "blend-game-quiz",
          "total_questions": 6,
          "progress": [],
          "status": "planned",
          "duration_seconds": null,
          "still_in_progress": false,
          "correct_percent": 0
        }
      ]
    }
  ]
}
```

- `400 Bad Request`: No student_id provided (for teacher/parent), or could not determine student for this request.
- `401 Unauthorized`: Invalid or missing token, or user not found.
- `404 Not Found`: Student not found.
- `500 Internal Server Error`: Unexpected error during generation.

---
