import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Calendar, User, Clock, CheckCircle } from 'lucide-react';
import testAssignmentService, { AssessmentSession } from '../../services/testAssignmentService';

const TestResultsPage: React.FC = () => {
  const [selectedStudentId, setSelectedStudentId] = useState<number | null>(null);

  // For now, we'll use a mock list of student IDs
  // In a real implementation, you'd fetch this from a students service
  const studentIds = [1, 2, 3, 4, 5];

  const {
    data: sessionsData,
    isLoading,
    error,
  } = useQuery<{ sessions: AssessmentSession[] } | null>({
    queryKey: ['allStudentSessions', selectedStudentId],
    queryFn: async () => {
      if (selectedStudentId) {
        return await testAssignmentService.getStudentSessions(selectedStudentId);
      }
      // If no student selected, return null
      return null;
    },
    enabled: !!selectedStudentId,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'planned':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'in_progress':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'planned':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatTestTitle = (assessmentId: string) => {
    return assessmentId
      .replace(/-/g, ' ')
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const sessions = sessionsData?.sessions || [];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Test Results</h1>

      {/* Student Selector */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">Select Student</label>
        <select
          value={selectedStudentId || ''}
          onChange={e => setSelectedStudentId(e.target.value ? parseInt(e.target.value) : null)}
          className="w-full max-w-xs p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">Choose a student...</option>
          {studentIds.map(id => (
            <option key={id} value={id}>
              Student {id}
            </option>
          ))}
        </select>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="text-center py-8">
          <p className="text-red-600">Error loading test results: {(error as Error).message}</p>
        </div>
      )}

      {/* No Student Selected */}
      {!selectedStudentId && !isLoading && (
        <div className="text-center py-8">
          <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Please select a student to view their test results.</p>
        </div>
      )}

      {/* No Results */}
      {selectedStudentId && !isLoading && sessions.length === 0 && (
        <div className="text-center py-8">
          <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No test sessions found for this student.</p>
        </div>
      )}

      {/* Results Grid */}
      {selectedStudentId && sessions.length > 0 && (
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
          {sessions.map((session: AssessmentSession) => (
            <div
              key={session.session_id}
              className="bg-white rounded-lg border border-gray-200 shadow-sm p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {formatTestTitle(session.assessment_id)}
                  </h3>
                  <div className="flex items-center text-sm text-gray-600 mb-2">
                    <Calendar className="w-4 h-4 mr-1" />
                    {formatDate(session.date)}
                  </div>
                </div>
                <div className="flex items-center space-x-2">{getStatusIcon(session.status)}</div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}
                  >
                    {session.status.charAt(0).toUpperCase() + session.status.slice(1)}
                  </span>
                  <span className="text-sm text-gray-500">
                    ID: {session.session_id.slice(0, 8)}...
                  </span>
                </div>

                {session.status === 'completed' && (
                  <div className="pt-3 border-t border-gray-100">
                    <button
                      onClick={() => {
                        // Navigate to detailed results view
                        window.location.href = `/dashboard/tests/results/${session.session_id}`;
                      }}
                      className="w-full px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                    >
                      View Detailed Results
                    </button>
                  </div>
                )}

                {session.status === 'planned' && (
                  <div className="pt-3 border-t border-gray-100">
                    <p className="text-xs text-gray-500">
                      Test scheduled for {formatDate(session.date)}
                    </p>
                  </div>
                )}

                {session.status === 'in_progress' && (
                  <div className="pt-3 border-t border-gray-100">
                    <p className="text-xs text-blue-600 font-medium">Test in progress...</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TestResultsPage;
