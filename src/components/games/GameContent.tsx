import React from 'react';
import { gameWebSocket } from '@games/common/websocket';
import { GameStats } from '@games/common/types';
import { getGameDefinition } from './GameRegistry';
import logger from '../../utils/logger';

interface GameContentProps {
  gameId: string;
  studentId: string;
  sessionId?: string; // Dodano sessionId z API
  sessionDetails?: unknown; // Dodano sessionDetails do wznowienia stanu
  onFinish: (result: GameStats & { startTime: number; endTime: number }) => void;
  onFeedback?: (isCorrect: boolean) => void;
}

export const GameContent: React.FC<GameContentProps> = ({
  gameId,
  studentId,
  sessionId,
  sessionDetails,
  onFinish,
  onFeedback,
}) => {
  const handleGameFinishInternal = (result: GameStats & { startTime: number; endTime: number }) => {
    const statsToStoreAndSend: GameStats & { startTime: number; endTime: number } = {
      sessionId: sessionId, // Używaj prawdziwego sessionId z API
      gameId: result.gameId,
      studentId: result.studentId,
      correctAnswers: result.correctAnswers,
      incorrectAnswers: result.totalQuestions - result.correctAnswers,
      totalQuestions: result.totalQuestions,
      completionPercentage:
        result.completionPercentage === 100
          ? 100
          : result.completionPercentage !== undefined
            ? result.completionPercentage
            : Math.round((result.correctAnswers / result.totalQuestions) * 100),
      startTime: result.startTime,
      endTime: result.endTime,
      timeInSeconds: result.timeInSeconds,
      progress: result.progress, // Zachowaj progress
    };

    gameWebSocket.sendGameStats(statsToStoreAndSend);
    logger.log('Game ended, stats from GameContent:', statsToStoreAndSend);

    if (onFinish) {
      onFinish(statsToStoreAndSend);
    }
  };

  const handleFeedback = (isCorrect: boolean) => {
    if (onFeedback) {
      onFeedback(isCorrect);
    }
  };

  const gameDefinition = getGameDefinition(gameId);

  if (gameDefinition) {
    const GameComponent = gameDefinition.component;
    const gameProps = {
      ...gameDefinition.defaultProps,
      studentId,
      gameId,
      sessionId,
      sessionDetails,
      onFinish: handleGameFinishInternal,
      onFeedback: handleFeedback,
    };

    return <GameComponent {...gameProps} />;
  }

  return (
    <div style={{ textAlign: 'center', padding: '3rem 0' }}>
      <h2
        style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#4B5563', marginBottom: '1rem' }}
      >
        Not found
      </h2>
      <p style={{ color: '#6B7280' }}>Game with ID: {gameId} not found.</p>
    </div>
  );
};

export default GameContent;
