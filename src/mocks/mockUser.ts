import { UserData } from '../services/authService';
import { USER_ROLES } from '../contants';

// Mock user data for automatic login
export const MOCK_USER: UserData = {
  user_id: 1,
  email: '<EMAIL>',
  role: USER_ROLES.TEACHER,
  role_label: 'Teacher',
  is_logged_in: true,
  avatar_url: 'https://ui-avatars.com/api/?name=Teacher&background=0D8ABC&color=fff',
};

// Mock access token - this is not a real token, just a placeholder
export const MOCK_ACCESS_TOKEN = 'mock-jwt-token.with.three.parts';
