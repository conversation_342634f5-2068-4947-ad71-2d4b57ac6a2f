import React from 'react';
import completedIcon from '../../assets/completed.svg';
import inProgressIcon from '../../assets/in_progress.svg';
import lockerIcon from '../../assets/locker.svg';
import grayTick from '../../assets/grayTick.svg';
import clock from '../../assets/clock.svg';

interface InterventionCardProps {
  title: string;
  image: string;
  progress: {
    current: number;
    total: number;
  };
  status: 'completed' | 'in-progress' | 'locked';
  timeSpent?: string;
}

export const InterventionCard: React.FC<InterventionCardProps> = ({
  title,
  image,
  progress,
  status,
  timeSpent = '∞',
}) => {
  const getBorderAndLabelStyles = () => {
    switch (status) {
      case 'completed':
        return {
          containerClass: 'relative bg-white overflow-hidden rounded-lg border-2 border-[#22C55E]',
          labelClass:
            'w-full h-[40px] top-0 left-0 py-2 flex items-center justify-center gap-[2px] bg-[#22C55E] text-white',
        };
      case 'in-progress':
        return {
          containerClass: 'relative bg-white overflow-hidden rounded-lg border-2 border-[#3B82F6]',
          labelClass:
            'w-full h-[40px] top-0 left-0 py-2 flex items-center justify-center gap-[2px] bg-[#3B82F6] text-white',
        };
      case 'locked':
        return {
          containerClass: 'relative bg-white overflow-hidden rounded-lg border-2 border-[#404042]',
          labelClass:
            'w-full h-[40px] top-0 left-0 py-2 flex items-center justify-center gap-[2px] bg-[#404042] text-[#767572]',
        };
    }
  };

  const { containerClass, labelClass } = getBorderAndLabelStyles();
  const textStyles =
    'font-fira-sans font-medium text-base leading-[20px] tracking-[0%] text-center align-middle';

  return (
    <div className={containerClass}>
      <div className={labelClass}>
        <img
          src={
            status === 'completed'
              ? completedIcon
              : status === 'in-progress'
                ? inProgressIcon
                : lockerIcon
          }
          alt=""
          className="w-5 h-5"
        />
        <span className={textStyles}>
          {status === 'completed'
            ? 'Completed'
            : status === 'in-progress'
              ? 'In Progress'
              : 'Locked'}
        </span>
      </div>
      <div className="w-full overflow-hidden max-h-[215px] flex items-center">
        <img src={image} alt={title} className="w-full h-full object-cover" />
      </div>
      <div className="flex items-center justify-between min-h-[30px] px-5">
        <h3 className="font-fira-sans font-medium text-base leading-[20px] tracking-[0%] align-middle text-[#1C1C1C]">
          {title}
        </h3>
        <div className="flex items-center gap-4">
          <span className="flex items-center gap-1">
            <img className="h-4 w-4" src={grayTick} alt="" />
            <span className="font-fira-sans font-normal text-[12px] leading-none tracking-[0%] align-middle text-black">
              {progress.current}/{progress.total}
            </span>
          </span>
          {timeSpent !== undefined && (
            <span className="font-fira-sans font-normal text-[12px] leading-none tracking-[0%] align-middle text-black flex items-center gap-1">
              <img className="h-4 w-4" src={clock} alt="" />
              {timeSpent}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};
