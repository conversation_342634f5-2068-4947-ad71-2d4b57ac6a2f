import Blend<PERSON>ender<PERSON> from './BlendRenderer';
import Math<PERSON><PERSON><PERSON> from './MathRenderer';
import MathRenderer1 from './MathRenderer1';
import MathRenderer2 from './MathRenderer2';
import MemoryRenderer from './MemoryRenderer';
import WordBuildingRenderer from './WordBuildingRenderer';
import MemoryRenderer1 from './MemoryRenderer1';
import MemoryRenderer2 from './MemoryRenderer2';
import SemanticMappingRenderer from './SemanticMappingRenderer';
import gameRegistry from '../GameRegistry';

gameRegistry.register('blend', BlendRenderer);
gameRegistry.register('math', MathRenderer);
gameRegistry.register('math-game1', MathRenderer1);
gameRegistry.register('math-game2', MathRenderer2); // Przywrócono oryginalny renderer
gameRegistry.register('memory', MemoryRenderer);
gameRegistry.register('wordbuilding', WordBuildingRenderer);
gameRegistry.register('memory-game1', MemoryRenderer1);
gameRegistry.register('memory-game2', MemoryRenderer2);
gameRegistry.register('mastermind-game', MemoryRenderer2); // Nowa gra "Złam kod liczbowy"
gameRegistry.register('support-strategy--semantic-mapping', SemanticMappingRenderer);

export {
  BlendRenderer,
  MathRenderer,
  MathRenderer1,
  MathRenderer2,
  MemoryRenderer,
  MemoryRenderer1,
  MemoryRenderer2,
  WordBuildingRenderer,
  SemanticMappingRenderer,
};

export { default as gameRegistry } from '../GameRegistry';
