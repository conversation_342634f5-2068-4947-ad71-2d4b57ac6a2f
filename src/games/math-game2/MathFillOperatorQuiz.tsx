import React, { useState, useEffect } from 'react';
import { quizData } from './quizData';
import QuizHeader from './QuizHeader';
import QuizQuestion from './QuizQuestion';
import GameResults from '../common/GameResults';
import { GameStats } from '../common/types';
import { gameWebSocket } from '../common/websocket';

interface MathFillOperatorQuizProps {
  studentId?: string;
  gameId?: string;
  sessionId?: string;
  onComplete?: (correct: number, total: number, time: number) => void;
  onFinish?: (result: {
    sessionId?: string;
    totalQuestions: number;
    correctAnswers: number;
    incorrectAnswers: number;
    completionPercentage: number;
    startTime: number;
    endTime: number;
    gameId?: string;
    studentId?: string;
  }) => void;
  onFeedback?: (isCorrect: boolean) => void;
}

type Operator = '+' | '-' | '*' | '/';

// Removed unused function

function checkAnswer(
  { num2, result }: { num2: number; result: number },
  userNum: string,
  userOp: string
) {
  const n1 = Number(userNum);
  const n2 = num2;
  const res = result;
  switch (userOp) {
    case '+':
      return n1 + n2 === res;
    case '-':
      return n1 - n2 === res;
    case '*':
      return n1 * n2 === res;
    case '/':
      return n2 !== 0 && n1 / n2 === res;
    default:
      return false;
  }
}

const MathFillOperatorQuiz: React.FC<MathFillOperatorQuizProps> = ({
  studentId,
  gameId = 'math-game2',
  sessionId,
  onComplete,
  onFinish,
  onFeedback,
}) => {
  const initialState = {
    currentQuestionIndex: 0,
    score: 0,
    userAnswers: Array(quizData.length).fill(null),
    quizCompleted: false,
  };

  const [quizState, setQuizState] = useState(initialState);
  const [userNum, setUserNum] = useState('');
  const [userOp, setUserOp] = useState('');
  const [showFeedback, setShowFeedback] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [animateOut, setAnimateOut] = useState(false);
  const [currentSlot, setCurrentSlot] = useState<number>(() => Math.floor(Math.random() * 2)); // 0: number, 1: operator
  const [currentOp, setCurrentOp] = useState<Operator>(() => {
    const operators: Operator[] = ['+', '-', '*', '/'];
    return operators[Math.floor(Math.random() * operators.length)];
  });
  const [stats, setStats] = useState<GameStats>({
    sessionId: sessionId,
    totalQuestions: quizData.length,
    correctAnswers: 0,
    incorrectAnswers: 0,
    completionPercentage: 0,
    startTime: Date.now(),
    endTime: null,
    gameId: gameId || 'math-game2',
    studentId: studentId || '',
    progress: [],
  });

  // Wszystkie hooki muszą być wywołane przed jakimkolwiek warunkiem
  useEffect(() => {
    gameWebSocket.connect();

    return () => {
      if (!quizState.quizCompleted && quizState.currentQuestionIndex > 0) {
        const partialStats = {
          ...stats,
          endTime: Date.now(),
          completionPercentage: Math.round(
            (quizState.currentQuestionIndex / quizData.length) * 100
          ),
        };

        gameWebSocket.sendGameStats(partialStats);
      }
    };
  }, [quizState.quizCompleted, quizState.currentQuestionIndex, stats, gameId]);

  const question = quizData[quizState.currentQuestionIndex];
  if (!question) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    let correct = false;
    if (currentSlot === 0) {
      // Missing number
      correct = checkAnswer(
        {
          num2: question.equation.num2,
          result: question.equation.result,
        },
        userNum,
        currentOp
      );
    } else {
      // Missing operator
      const n1 = question.equation.num1;
      const n2 = question.equation.num2;
      const res = question.equation.result;
      switch (userOp) {
        case '+':
          correct = n1 + n2 === res;
          break;
        case '-':
          correct = n1 - n2 === res;
          break;
        case '*':
          correct = n1 * n2 === res;
          break;
        case '/':
          correct = n2 !== 0 && n1 / n2 === res;
          break;
        default:
          correct = false;
      }
    }

    setIsCorrect(correct);
    setShowFeedback(true);

    if (onFeedback) {
      onFeedback(correct);
    }

    const newUserAnswers = [...quizState.userAnswers];
    newUserAnswers[quizState.currentQuestionIndex] = correct ? 1 : 0;

    setQuizState(prev => ({
      ...prev,
      userAnswers: newUserAnswers,
      score: correct ? prev.score + 1 : prev.score,
    }));

    setStats(prevStats => {
      const completedQuestions = quizState.currentQuestionIndex + 1;
      const newStats: GameStats = {
        ...prevStats,
        sessionId: sessionId,
        correctAnswers: prevStats.correctAnswers + (correct ? 1 : 0),
        incorrectAnswers: prevStats.incorrectAnswers + (correct ? 0 : 1),
        completionPercentage: Math.round((completedQuestions / quizData.length) * 100),
        gameId: gameId || 'math-game2',
        studentId: studentId || '',
        endTime: Date.now(),
        progress: [
          ...(prevStats.progress || []),
          {
            step: quizState.currentQuestionIndex + 1,
            correct: correct,
          },
        ],
      };

      gameWebSocket.sendGameStats(newStats);
      return newStats;
    });

    setTimeout(() => {
      setAnimateOut(true);

      setTimeout(() => {
        const nextIndex = quizState.currentQuestionIndex + 1;

        if (nextIndex >= quizData.length) {
          setQuizState(prev => ({
            ...prev,
            quizCompleted: true,
          }));

          const endTime = Date.now();
          setStats(prevStats => {
            const updatedStats: GameStats = {
              ...prevStats,
              sessionId: sessionId,
              endTime,
              completionPercentage: 100,
              gameId: gameId || 'math-game2',
              studentId: studentId || '',
              progress: prevStats.progress || [],
            };

            gameWebSocket.sendGameStats(updatedStats);

            if (onComplete) {
              const time = Math.round((endTime - prevStats.startTime) / 1000);
              onComplete(updatedStats.correctAnswers, updatedStats.totalQuestions, time);
            }

            if (onFinish) {
              const timeInSeconds = Math.round((endTime - prevStats.startTime) / 1000);
              const finalStats = {
                ...updatedStats,
                timeInSeconds,
                endTime: endTime, // Upewniamy się, że endTime jest liczbą
                studentId: String(updatedStats.studentId || ''), // Konwersja studentId na string
                progress: updatedStats.progress || [],
              };

              onFinish(finalStats);
            }

            return updatedStats;
          });
        } else {
          setQuizState(prev => ({
            ...prev,
            currentQuestionIndex: nextIndex,
          }));
          setUserNum('');
          setUserOp('');
          setCurrentSlot(Math.floor(Math.random() * 2));
          const operators: Operator[] = ['+', '-', '*', '/'];
          setCurrentOp(operators[Math.floor(Math.random() * operators.length)]);
        }

        setShowFeedback(false);
        setAnimateOut(false);
      }, 500);
    }, 1500);
  };

  const restartQuiz = () => {
    setQuizState({
      ...initialState,
    });

    setStats({
      sessionId: sessionId,
      totalQuestions: quizData.length,
      correctAnswers: 0,
      incorrectAnswers: 0,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: gameId || 'math-game2',
      studentId: studentId || '',
      progress: [],
    });

    setUserNum('');
    setUserOp('');
    setShowFeedback(false);
    setAnimateOut(false);
    setCurrentSlot(Math.floor(Math.random() * 2));
    const operators: Operator[] = ['+', '-', '*', '/'];
    setCurrentOp(operators[Math.floor(Math.random() * operators.length)]);
  };

  if (quizState.quizCompleted) {
    // Konwertujemy GameStats na format oczekiwany przez GameResults
    const finalEndTime = stats.endTime || Date.now();
    const finalStats = {
      ...stats,
      timeInSeconds: Math.round((finalEndTime - stats.startTime) / 1000),
      endTime: finalEndTime, // Upewniamy się, że endTime jest liczbą
      studentId: String(stats.studentId || ''), // Konwersja studentId na string
      completionPercentage: 100,
      progress: stats.progress || [],
    };
    return <GameResults stats={finalStats} onRestart={restartQuiz} />;
  }

  // Przygotowanie danych dla komponentu QuizQuestion
  const questionProps = {
    num1: currentSlot === 0 ? 0 : question.equation.num1, // Jeśli brakuje liczby, pokazujemy 0 jako placeholder
    num2: question.equation.num2,
    correctAnswer:
      currentSlot === 0
        ? question.equation.result -
          question.equation.num2 *
            (currentOp === '+'
              ? 1
              : currentOp === '-'
                ? -1
                : currentOp === '*'
                  ? 1
                  : 1 / question.equation.num2)
        : question.equation.result,
    showFeedback: showFeedback,
    isCorrect: isCorrect,
    onSubmit: (answer: number) => {
      if (currentSlot === 0) {
        setUserNum(answer.toString());
        // Tworzymy sztuczny event i konwertujemy go bezpiecznie na FormEvent
        const mockEvent = { preventDefault: () => {} } as React.FormEvent;
        handleSubmit(mockEvent);
      }
    },
  };

  return (
    <div className="w-full bg-gradient-to-b from-blue-50 to-purple-50 py-8 px-4">
      <div className="max-w-4xl mx-auto overflow-x-auto">
        <QuizHeader
          currentQuestion={quizState.currentQuestionIndex + 1}
          totalQuestions={quizData.length}
          score={quizState.score}
        />

        <div className="w-full flex flex-col md:flex-row gap-8 items-stretch min-h-[500px]">
          <div className="w-full flex flex-col justify-center">
            <div
              className={`transition-all duration-500 ease-in-out ${
                animateOut ? 'opacity-0 scale-95' : 'opacity-100 scale-100'
              }`}
            >
              {currentSlot === 0 ? (
                // Jeśli brakuje liczby, używamy komponentu QuizQuestion
                <QuizQuestion {...questionProps} />
              ) : (
                // Jeśli brakuje operatora, używamy niestandardowego widoku
                <div className="w-full p-6 rounded-xl bg-white shadow-lg">
                  <div className="mb-8">
                    <div className="flex items-center justify-center text-3xl font-bold gap-3 py-4">
                      <span className="text-2xl mx-2">{question.equation.num1}</span>
                      <input
                        type="text"
                        value={userOp}
                        onChange={e => setUserOp(e.target.value)}
                        className="border rounded w-16 text-center mx-2 transition-all duration-300"
                        disabled={showFeedback}
                        maxLength={1}
                      />
                      <span className="text-2xl mx-2">{question.equation.num2}</span>
                      <span className="text-2xl mx-2">=</span>
                      <span className="text-2xl mx-2">{question.equation.result}</span>
                    </div>
                  </div>

                  <div className="flex flex-col items-center">
                    <form
                      onSubmit={handleSubmit}
                      className="flex flex-col items-center gap-6 mt-6 w-full"
                    >
                      <button
                        type="submit"
                        className="bg-blue-500 text-white rounded-lg px-8 py-3 mt-4 hover:bg-blue-600 disabled:opacity-50 font-bold transition-colors"
                        disabled={showFeedback || !userOp}
                      >
                        Check
                      </button>
                    </form>

                    {showFeedback && (
                      <div
                        className={`text-lg font-medium ${isCorrect ? 'text-green-600' : 'text-red-600'} mb-2 animate-fadeIn mt-6`}
                      >
                        {isCorrect ? 'Correct! Great job! 🎉' : 'Not quite. Try again!'}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MathFillOperatorQuiz;
