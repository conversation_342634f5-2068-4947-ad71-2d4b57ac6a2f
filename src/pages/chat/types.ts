import { ReadingAreaDetails, Message, ChatStreamPayload } from '../../services/chatService';
import type { AssessmentResult } from '../../types/assessment';

export type EntryType = 'know' | 'quick' | 'survey';

export interface ChatPageProps {
  studentId?: string;
  studentName?: string;
}

export const assessmentQuestions = [
  'Is there an issue with recognizing words quickly?',
  'Is there an issue with sounding out new words?',
  'Is there an issue with reading slowly or stumbling over words?',
  'Is there an issue with forgetting what you read soon after?',
  'Is there an issue with understanding long sentences or big words?',
  'Is there an issue with spelling or remembering how words look?',
];

export const gradeLevels = ['1st grade', '2nd grade'];

export interface ReadingArea {
  id: number;
  name: string;
  description: string;
}

export type AssessmentAnswer = 'yes' | 'no';

export interface AnsweredQuestion {
  question: string;
  answer?: AssessmentAnswer;
}

export type { ReadingAreaDetails, Message, ChatStreamPayload, AssessmentResult };
