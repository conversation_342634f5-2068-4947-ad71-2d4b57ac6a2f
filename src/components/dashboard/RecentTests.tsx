import React from 'react';

interface TestData {
  name: string;
  surname: string;
  diagnoseDate: string;
  status: 'Complete' | 'Approved' | 'Pending';
}

interface RecentTestsProps {
  tests: TestData[];
}

export const RecentTests: React.FC<RecentTestsProps> = ({ tests }) => {
  const getStatusColor = (status: TestData['status']) => {
    switch (status) {
      case 'Complete':
        return 'text-green-500';
      case 'Approved':
        return 'text-yellow-500';
      case 'Pending':
        return 'text-gray-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg">
      <h2 className="text-xl font-semibold mb-6">Recently performed Tests and Quizzes</h2>
      <table className="w-full">
        <thead>
          <tr className="text-left text-gray-500 border-b">
            <th className="pb-3">Name</th>
            <th className="pb-3">Surname</th>
            <th className="pb-3">Diagnose Date</th>
            <th className="pb-3">Status</th>
            <th className="pb-3"></th>
          </tr>
        </thead>
        <tbody>
          {tests.map((test, index) => (
            <tr key={index} className="border-b last:border-b-0">
              <td className="py-4">{test.name}</td>
              <td>{test.surname}</td>
              <td>{test.diagnoseDate}</td>
              <td>
                <span className={`flex items-center ${getStatusColor(test.status)}`}>
                  <span className="w-1.5 h-1.5 rounded-full bg-current mr-2"></span>
                  {test.status}
                </span>
              </td>
              <td className="text-right">
                <button className="text-gray-400 hover:text-gray-600">
                  <span className="text-xl">...</span>
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
