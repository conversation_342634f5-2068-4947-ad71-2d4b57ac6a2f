import React, { useEffect, useState, useRef, ReactElement } from 'react';
import {
  studentService,
  CalendarData,
  CalendarDay,
  GameSession,
} from '../../services/studentService';
import { date } from 'zod';
import { Popover } from '@headlessui/react';

interface ProgressCalendarProps {
  studentId: string;
}

interface DayProgressDetails {
  sessions: GameSession[];
}

interface TooltipState {
  isVisible: boolean;
  x: number;
  y: number;
  sessions: GameSession[];
}

export const ProgressCalendar: React.FC<ProgressCalendarProps> = ({ studentId }) => {
  const [currentMonthIndex, setCurrentMonthIndex] = useState<number>(1);
  const [_currentYear, _setCurrentYear] = useState(new Date().getFullYear());
  const [currentView, setCurrentView] = useState<'month' | 'week'>('month');
  const [calendarData, setCalendarData] = useState<CalendarData | null>(null);
  const [tooltipState, setTooltipState] = useState<TooltipState | null>(null);
  const calendarRef = useRef<HTMLDivElement>(null);
  const dayRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const tooltipTimeoutRef = useRef<number | null>(null);
  const [hoveredDay, setHoveredDay] = useState<string | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState<'top' | 'bottom'>('bottom');
  const tooltipRef = useRef<HTMLDivElement>(null);

  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  useEffect(() => {
    const fetchCalendarData = async () => {
      try {
        const data = await studentService.getCalendar(studentId);
        setCalendarData(data);
        if (data && data.months.length > 0) {
          setCurrentMonthIndex(1);
          _setCurrentYear(data.months[1].year);
        }
      } catch (error) {
        console.error('Error retrieving calendar data:', error);
      }
    };
    fetchCalendarData();
  }, [studentId]);

  const getDayProgressDetails = (day: CalendarDay): DayProgressDetails => {
    return {
      sessions: day.sessions || [],
    };
  };

  const renderProgressDots = (sessions: GameSession[]) => {
    if (!sessions || sessions.length === 0) {
      return null;
    }

    return sessions.map((session, index) => {
      let status = 'not_started';
      if (session.status === 'completed') {
        status = 'completed';
      } else if (session.status === 'in_progress') {
        status = 'partial';
      }
      return <div key={`dot-${session.session_id}`} className={`dot ${status}`} />;
    });
  };

  const renderTooltipContent = (sessions: GameSession[]) => {
    return (
      <div className="progress-tooltip-inner border" style={{ width: '100%' }}>
        <div
          style={{
            maxHeight: '300px',
            overflowY: 'auto',
            overflowX: 'hidden',
            width: '100%',
            padding: '0',
          }}
        >
          <table
            className="progress-tooltip-table"
            style={{ width: '100%', borderCollapse: 'collapse', tableLayout: 'fixed' }}
          >
            <colgroup>
              <col style={{ width: '25%', minWidth: '90px' }} />
              <col style={{ width: '25%', minWidth: '110px' }} />
              <col style={{ width: '20%', minWidth: '90px' }} />
              <col style={{ width: '17%', minWidth: '80px' }} />
            </colgroup>
            <thead style={{ borderBottom: 'none' }}>
              <tr>
                <th
                  style={{
                    padding: '8px 8px',
                    textAlign: 'left',
                    background: 'white',
                    color: '#222',
                    fontWeight: 600,
                  }}
                ></th>
                <th
                  style={{
                    padding: '8px 8px',
                    textAlign: 'left',
                    background: '#23272F',
                    color: 'white',
                    fontWeight: 600,
                  }}
                >
                  Status
                </th>
                <th
                  style={{
                    padding: '8px 8px',
                    textAlign: 'left',
                    background: '#23272F',
                    color: 'white',
                    fontWeight: 600,
                  }}
                >
                  Score
                </th>
                <th
                  style={{
                    padding: '8px 8px',
                    textAlign: 'left',
                    background: '#23272F',
                    color: 'white',
                    fontWeight: 600,
                  }}
                >
                  Time
                </th>
              </tr>
            </thead>
            <tbody>
              {sessions.map((session, idx) => (
                <tr key={`${session.session_id}-${idx}`} style={{ borderBottom: '1px solid #eee' }}>
                  <td
                    style={{
                      padding: '8px 8px',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      borderBottom: '1px solid white',
                    }}
                  >
                    {formatGameName(session.game_id, idx)}
                  </td>
                  <td
                    style={{
                      padding: '8px 8px',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    <span className={`status ${session.status}`}>
                      {session.status === 'completed'
                        ? 'Completed'
                        : session.status === 'in_progress'
                          ? 'In Progress'
                          : 'Not Started'}
                    </span>
                  </td>
                  <td
                    style={{
                      padding: '8px 8px',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {session.total_questions > 0
                      ? Math.round(
                          (session.progress.filter(p => p.correct).length /
                            session.total_questions) *
                            100
                        )
                      : 0}
                    %
                  </td>
                  <td
                    style={{
                      padding: '8px 8px',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    <span role="img" aria-label="clock">
                      🕒
                    </span>{' '}
                    {formatDuration(session.duration_seconds)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const updateTooltipPosition = (dayElement: HTMLElement) => {
    if (!tooltipRef.current || !calendarRef.current) return;

    const dayRect = dayElement.getBoundingClientRect();
    const calendarRect = calendarRef.current.getBoundingClientRect();
    const tooltipHeight = tooltipRef.current.offsetHeight;
    const spaceBelow = window.innerHeight - dayRect.bottom;
    const spaceAbove = dayRect.top;

    if (spaceBelow < tooltipHeight && spaceAbove > tooltipHeight) {
      setTooltipPosition('top');
    } else {
      setTooltipPosition('bottom');
    }
  };

  const handleDayMouseEnter = (dayKey: string, dayElement: HTMLElement) => {
    setHoveredDay(dayKey);
    updateTooltipPosition(dayElement);
  };

  const handleDayMouseLeave = () => {
    if (tooltipTimeoutRef.current) {
      window.clearTimeout(tooltipTimeoutRef.current);
    }
    setTooltipState(null);
  };

  const renderMonthView = () => {
    if (!calendarData) return null;

    const currentMonthData = calendarData.months[currentMonthIndex];
    if (!currentMonthData) return null;

    const days: ReactElement[] = [];

    days.push(
      <div key="week-header" className="week-header-wrapper">
        {daysOfWeek.map(day => (
          <div key={`header-${day}`} className="day-header">
            {day}
          </div>
        ))}
      </div>
    );

    currentMonthData.weeks.forEach((week, weekIndex) => {
      const isCurrentWeekRow = week.some(day => day.is_today);
      days.push(
        <div
          key={`week-${weekIndex}`}
          className={`week-row ${isCurrentWeekRow ? 'current-week' : ''}`}
        >
          {week.map((day, dayIndex) => {
            const date = new Date(day.date);
            const dayOfWeek = date.getDay();
            const isWeekend = dayOfWeek === 0;
            const dayKey = `${weekIndex}-${dayIndex}`;
            const isHovered = hoveredDay === dayKey;

            return (
              <div
                key={dayKey}
                className={`relative ${isCurrentWeekRow ? 'current-week-day' : ''}`}
                onMouseEnter={e => handleDayMouseEnter(dayKey, e.currentTarget)}
                onMouseLeave={() => setHoveredDay(null)}
              >
                <div
                  className={`calendar-day ${!day.in_current_month ? 'non-month-day' : ''} ${isWeekend ? 'weekend' : ''} ${day.is_today ? 'today' : ''}`}
                  style={{
                    position: 'relative',
                    fontSize: '20px',
                  }}
                >
                  <div className="date-number">
                    {date.getDate()} {day.is_today ? 'Today' : ''}
                  </div>
                  <div className="progress-dots">{renderProgressDots(day.sessions)}</div>
                </div>

                {isHovered && day.sessions && day.sessions.length > 0 && (
                  <div
                    ref={tooltipRef}
                    className={`absolute z-50 w-[450px] max-w-[calc(100vw-80px)] bg-white rounded-lg shadow-lg ${
                      tooltipPosition === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'
                    }`}
                    style={{
                      transform: 'translateX(-50%)',
                      left: '50%',
                    }}
                    onMouseEnter={() => setHoveredDay(dayKey)}
                    onMouseLeave={() => setHoveredDay(null)}
                  >
                    {renderTooltipContent(day.sessions)}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      );
    });

    return days;
  };

  const formatDuration = (seconds: number | null): string => {
    if (!seconds) return '0:00';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatGameName = (gameId: string, index: number): string => {
    return `Intervention ${index + 1}`;
  };

  return (
    <div
      className="calendar-container"
      style={{ position: 'relative', fontSize: '20px' }}
      ref={calendarRef}
    >
      <div className="calendar-header">
        <div className="month-selector">
          <select
            value={currentMonthIndex.toString()}
            onChange={e => setCurrentMonthIndex(Number(e.target.value))}
          >
            {calendarData?.months.map((month, index) => (
              <option key={month.name} value={index.toString()}>
                {month.name} {month.year}
              </option>
            ))}
          </select>
        </div>
        <div className="view-toggle">
          <button
            className={currentView === 'month' ? 'active' : ''}
            onClick={() => setCurrentView('month')}
          >
            Month
          </button>
          <button
            className={currentView === 'week' ? 'active' : ''}
            onClick={() => setCurrentView('week')}
          >
            Week
          </button>
        </div>
      </div>
      <div className="calendar-grid">{renderMonthView()}</div>
    </div>
  );
};
