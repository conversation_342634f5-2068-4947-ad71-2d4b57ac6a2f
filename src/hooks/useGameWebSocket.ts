import { useCallback } from 'react';
import { GameStats } from '@games/common/types';
import {
  useSendGameStats,
  useProcessQueuedMessages,
  useWebSocketQueue,
} from '../services/queries/websocketQueries';
import { gameWebSocket } from '@games/common/websocket';
import logger from '../utils/logger';

/**
 * Hook for managing WebSocket game statistics with React Query
 * Provides a modern, reactive way to send game stats with automatic retry and queue management
 */
export const useGameWebSocket = () => {
  const sendStatsMutation = useSendGameStats();
  const processQueueMutation = useProcessQueuedMessages();
  const { queueLength, hasQueuedMessages } = useWebSocketQueue();

  /**
   * Send game statistics via WebSocket with React Query
   * Automatically handles retries, queuing, and local storage
   */
  const sendGameStats = useCallback(
    async (stats: GameStats): Promise<boolean> => {
      try {
        // Format stats similar to the original WebSocket implementation
        const formattedStats = {
          sessionId: stats.sessionId || 'unknown',
          gameId: stats.gameId,
          studentId: stats.studentId,
          totalQuestions: stats.totalQuestions || 0,
          correctAnswers: stats.correctAnswers || 0,
          incorrectAnswers: stats.incorrectAnswers || 0,
          completionPercentage: stats.completionPercentage || 0,
          startTime: stats.startTime,
          endTime: stats.endTime || Date.now(),
          timeInSeconds:
            stats.timeInSeconds ||
            Math.round((stats.endTime || Date.now()) - stats.startTime) / 1000,
          progress: stats.progress || [],
        };

        logger.log('[useGameWebSocket] Sending stats via React Query:', formattedStats);

        // Use React Query mutation to send stats
        await sendStatsMutation.mutateAsync(formattedStats);
        return true;
      } catch (error) {
        logger.error('[useGameWebSocket] Failed to send stats:', error);
        return false;
      }
    },
    [sendStatsMutation]
  );

  /**
   * Process all queued messages
   */
  const processQueue = useCallback(async () => {
    if (!hasQueuedMessages) {
      logger.log('[useGameWebSocket] No queued messages to process');
      return { total: 0, success: 0 };
    }

    try {
      const result = await processQueueMutation.mutateAsync();
      logger.log('[useGameWebSocket] Queue processed:', result);
      return result;
    } catch (error) {
      logger.error('[useGameWebSocket] Failed to process queue:', error);
      return { total: queueLength, success: 0 };
    }
  }, [processQueueMutation, hasQueuedMessages, queueLength]);

  /**
   * Connect to WebSocket (delegates to original implementation)
   */
  const connect = useCallback(() => {
    gameWebSocket.connect();
  }, []);

  /**
   * Disconnect from WebSocket (delegates to original implementation)
   */
  const disconnect = useCallback(() => {
    gameWebSocket.disconnect();
  }, []);

  /**
   * Reset WebSocket connection state (delegates to original implementation)
   */
  const resetConnectionState = useCallback(() => {
    gameWebSocket.resetConnectionState();
  }, []);

  return {
    // Main functions
    sendGameStats,
    processQueue,
    connect,
    disconnect,
    resetConnectionState,

    // State
    queueLength,
    hasQueuedMessages,
    isLoading: sendStatsMutation.isPending || processQueueMutation.isPending,
    isError: sendStatsMutation.isError || processQueueMutation.isError,
    error: sendStatsMutation.error || processQueueMutation.error,

    // Raw mutations (for advanced usage)
    sendStatsMutation,
    processQueueMutation,
  };
};
