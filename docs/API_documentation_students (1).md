# API Documentation – `students/` Module

**Prefix for all endpoints:** `/students`

**General Description:**  
This API module manages student records associated with a logged-in teacher. It includes functionalities for registering, adding, listing, editing, and deleting student entries.

---

## 1. POST `/students/register`

**Description**: Register a new student user and add them to the teacher's student list.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only users with the `teacher` role can register students.
- If the student already exists, returns 400.

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "age": 10,
  "gender": "female",
  "additional_info": "Loves reading"
}
```

### Responses:

- `201 Created`:

```json
{
  "message": "Student registered successfully",
  "student_user_id": 789,
  "temporary_email": "<EMAIL>",
  "temporary_password": "Abc12345"
}
```

- `400 Bad Request`: Student already registered.
- `403 Forbidden`: User not authorized to register students.
- `500 Internal Server Error`: Failed to register student.

---

## 2. POST `/students/add`

**Description**: Add an already registered student to the teacher's managed list.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only users with the `teacher` role can add students.
- If the student is not yet registered, a message is returned.

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "first_name": "Alice",
  "last_name": "Smith",
  "age": 10,
  "gender": "female",
  "additional_info": "Loves reading"
}
```

### Responses:

- `201 Created`:

```json
{
  "message": "Student added successfully",
  "student_id": 456
}
```

- `200 OK`:

```json
{
  "message": "Student not registered. Please register the student first."
}
```

- `403 Forbidden`: User not authorized to add students.
- `500 Internal Server Error`: Failed to add student.

---

## 3. GET `/students/list`

**Description**: List all students added by the current user.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.

### Headers:

```
Authorization: Bearer <access_token>
```

### Responses:

- `200 OK`:

```json
{
  "students": [
    {
      "id": 456,
      "first_name": "Alice",
      "last_name": "Smith",
      "age": 10,
      "gender": "female",
      "additional_info": "Loves reading",
      "created_at": "2024-01-01T12:00:00"
    }
  ]
}
```

- `500 Internal Server Error`: Failed to list students.

---

## 4. PATCH `/students/edit/{student_id}`

**Description**: Edit an existing student's data.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only fields provided in the request body will be updated.
- Only the owner teacher can edit their students.

### Headers:

```
Authorization: Bearer <access_token>
```

### Body (example with optional fields):

```json
{
  "first_name": "Alicia",
  "age": 11
}
```

### Responses:

- `200 OK`:

```json
{
  "message": "Student updated successfully"
}
```

- `400 Bad Request`: No fields to update.
- `403 Forbidden`: Access denied.
- `500 Internal Server Error`: Failed to update student.

---

## 5. DELETE `/students/delete/{student_id}`

**Description**: Delete a student record.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only the owner teacher can delete their students.

### Headers:

```
Authorization: Bearer <access_token>
```

### Responses:

- `200 OK`:

```json
{
  "message": "Student deleted successfully"
}
```

- `403 Forbidden`: Access denied.
- `500 Internal Server Error`: Failed to delete student.

---

## 6. POST `/students/parent_register`

**Description**: Allows a parent to register a new student under their care.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only users with the `parent` role can register students.
- If the student (based on first and last name) already exists, returns 400.

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "first_name": "John",
  "last_name": "Smith",
  "age": 10,
  "gender": "male",
  "additional_info": "Test additional info by parent"
}
```

### Responses:

- `201 Created`:

```json
{
  "message": "Student registered by parent successfully",
  "student_user_id": 123,
  "temporary_email": "<EMAIL>",
  "temporary_password": "aB8kLm2P"
}
```

- `400 Bad Request`: Student already exists.
- `403 Forbidden`: User not authorized to register students.
- `422 Unprocessable Entity`: Missing or malformed token.
- `500 Internal Server Error`: Unexpected server error.

---

## 7. GET `/students/view/{student_id}`

**Description**: Retrieve detailed information about a specific student using their user_id from the users table.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only the teacher who added the student or the parent who registered them can access the data.
- Returns 404 if the student is not found.
- Returns 403 if the user is not authorized to access the student data.

### Path Parameters:

- student_user_id (integer): User ID of the student (not the row ID from the students table).

### Headers:

```
Authorization: Bearer <access_token>
```

### Responses:

- `200 OK`:

```json
{
  "id": 123,
  "user_id": 456,
  "first_name": "John",
  "last_name": "Smith",
  "age": 10,
  "gender": "male",
  "additional_info": "Needs reading support",
  "teacher_id": 789,
  "parent_id": 1011,
  "created_at": "2024-01-01T12:00:00"
}
```

- `403 Bad Request`: Access denied – user does not have permission to view the student.
- `404 Forbidden`: Student not found.
- `500 Internal Server Error`: Failed to retrieve student data.

---

## 8. POST `/students/data_change_request`

**Description**: Allows a parent to submit a request to correct or delete a student's data.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only users with the parent role can submit such requests.
- Submitted requests are stored in the data_change_requests table for later review by an admin or system moderator.

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "student_id": 123,
  "request_type": "correction",
  "details": "Please correct the spelling of the last name to 'Kowalski'."
}
```

- `request_type:` One of `"correction"` or `"deletion"`.
- `details:`Free-text explanation of the requested change.

### Responses:

- `200 OK`:

```json
{
  "message": "Request submitted successfully"
}
```

- `404 Forbidden`: Only parents can submit data change request
- `500 Internal Server Error`: Failed to submit request.

---

## 9. POST `/students/calendar/view`

**Description**: Returns a structured calendar view showing a student's activity across educational game sessions for the previous, current, and next month.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Only teachers and parents can view student session data.
- **Planned**: Access control to restrict calendar data only to users assigned to the student (e.g., teacher or parent).

### Headers:

```
Authorization: Bearer <access_token>
```

### Body:

```json
{
  "student_id": 3
}
```

### Responses:

- `200 OK`:

```json
{
  "months": [
    {
      "name": "May",
      "year": 2025,
      "weeks": [
        [
          {
            "date": "2025-05-12",
            "weekday": "Monday",
            "is_today": true,
            "in_current_month": true,
            "sessions": [
              {
                "session_id": "abc-123",
                "game_id": "blend-game",
                "total_questions": 6,
                "progress": [
                  { "step": 1, "correct": true },
                  { "step": 2, "correct": true },
                  { "step": 3, "correct": false }
                ],
                "status": "in_progress",
                "duration_seconds": null,
                "still_in_progress": true
              }
            ]
          }
        ]
      ]
    }
  ]
}
```

- `401 Unauthorized`: Invalid or missing access token.
- `403 Forbidden`: User does not have access to the requested student.
- `500 Internal Server Error`: Failed to generate calendar view.

---

## 10. GET `/students/stats`

**Description**:
Returns global system statistics, including the number of students, teachers, parents, chat sessions, assigned interventions, user chat questions, and available interventions in math and reading.
Also returns the most popular intervention assigned in the system.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- For any authenticated user (teacher, parent, student).
- Data covers all users and interventions in the system.

### Headers:

```
Authorization: Bearer <access_token>
```

### Responses:

- `200 OK`:

```json
{
  "total_students": 11,
  "average_age": 10,
  "total_parents": 2,
  "total_teachers": 15,
  "total_chat_sessions": 1,
  "total_assigned_interventions": 50,
  "total_user_questions": 0,
  "total_math_interventions": 6,
  "total_reading_interventions": 24,
  "total_interventions": 30,
  "most_popular_intervention": {
    "game_id": "math-game",
    "title": "Math Game",
    "count": 30
  },
  "best_student": {
    "student_id": 7,
    "first_name": "Emily",
    "last_name": "Johnson",
    "average_score": 85
  }
}
```

- **total_students:** Total number of students in the system.
- **average_age:** Average age of all students (rounded to the nearest integer).
- **total_parents:** Total number of parent accounts in the system.
- **total_teachers:** Total number of teacher accounts in the system.
- **total_chat_sessions**: Total number of chat sessions created in the system.
- **total_assigned_interventions:** Total number of assigned interventions (game_sessions records).
- **total_user_questions:** Number of user questions sent to chat (messages where sender = "user_question").
- **total_math_interventions:** Number of possible intervention activities available in the "math" domain.
- **total_reading_interventions:** Number of possible intervention activities available in the "reading" domain.
- **total_interventions:** Number of all possible intervention activities.
- **most_popular_intervention:**
  - **game_id:** The ID of the most frequently assigned intervention.
  - **count:** Number of times this intervention has been assigned.
- **best_student:**
  - **student_id:** ID of the student with the best average score.
  - **first_name:** First name of the best student.
  - **last_name:** Last name of the best student.
  - **average_score:** Average score (percentage) from all completed interventions.

* `401 Unauthorized`: Invalid or missing access token.
* `403 Forbidden`: User does not have access to the requested student.
* `500 Internal Server Error`: Failed to generate calendar view.

---

## 11. GET `/students/stats_my_students`

**Description**:
Returns a list of all students assigned to the authenticated teacher or parent, including per-student statistics: number of assigned interventions, number of completed interventions, and average score from completed interventions.

**Notes:**

- Requires `Authorization: Bearer <access_token>` header.
- Available to users with the role of teacher (role_id=1) or parent (role_id=2)
- If the authenticated user has no students assigned, returns an empty list.
- Statistics are calculated per student for all assigned interventions.

### Headers:

```
Authorization: Bearer <access_token>
```

### Responses:

- `200 OK`:

```json
{
  "user_id": 42,
  "role": "teacher",
  "total_students": 2,
  "students": [
    {
      "student_id": 101,
      "full_name": "Adam Kowalski",
      "intervention_starts": "May 20, 2025",
      "intervention_completed": "4/6",
      "avg_score_progress": "89%",
      "interventions_above_30": {
        "count": 3,
        "percent": 75
      },
      "interventions_days_in_a_row": 2,
      "last_intervention": "2 days ago"
    },
    {
      "student_id": 102,
      "full_name": "Ola Nowak",
      "intervention_starts": "May 10, 2025",
      "intervention_completed": "5/7",
      "avg_score_progress": "78%",
      "interventions_above_30": {
        "count": 2,
        "percent": 40
      },
      "interventions_days_in_a_row": 1,
      "last_intervention": "today"
    }
  ]
}
```

- **user_id:** ID of the current authenticated user (teacher or parent).
- **role:** "teacher" or "parent" (based on the current user).
- **total_students:** Total number of students assigned to this user.
- **students:** List of students with statistics:
  - **student_id:** ID of the student.
  - **full_name:** Full name of the student (first name + last name).
  - **intervention_starts:** The date when the student's first intervention was assigned (formatted as "Month DD, YYYY"). If the student has no interventions, this will be null.
  - **intervention_completed**: String with the number of completed interventions vs assigned, e.g., "4/6"
  - **avg_score_progress:** Average completion score for all completed interventions, as a percentage string (e.g., "89%"). If no completed interventions, returns "0%".
  - **interventions_above_30:** Object with:
    - **count:** Number of completed interventions with at least 30% completion.
    - **percent:** What percent of completed interventions are above 30%.
  - **interventions_days_in_a_row::** Maximum number of consecutive days with at least one completed intervention.
  - **last_intervention:** How many days ago the last intervention was completed by this student (e.g., "2 days ago", "today", or "never").

* `401 Unauthorized`: Invalid or missing access token.
* `403 Forbidden`: User does not have access to the requested student.
* `500 Internal Server Error`: Failed to generate calendar view.

---
