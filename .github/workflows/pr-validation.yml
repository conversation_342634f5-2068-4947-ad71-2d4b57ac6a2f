name: Pull Request Validation

on:
  pull_request:
    branches: [main, production]

jobs:
  validate-build:
    runs-on: [self-hosted, yubu]

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run lint
        run: npm run lint

      - name: Run prettier check
        run: npm run format:check

      - name: Run husky
        run: npm run prepare

      - name: Build
        run: npm run build
        env:
          NODE_ENV: production
