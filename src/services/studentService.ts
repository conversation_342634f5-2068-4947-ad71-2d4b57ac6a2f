import { StudentsStats } from 'src/store/studenstStore';
import { api } from './api';

export interface Student {
  id: number;
  first_name: string;
  last_name: string;
  age: number;
  gender: string;
  additional_info?: string;
  email: string;
  password: string;
  role_id: number;
}

// Interfejs dla studentów zwracanych przez API /students/list
export interface StudentListItem {
  id: number;
  first_name: string;
  last_name: string;
  age: number;
  gender: string;
  additional_info?: string;
  created_at?: string;
}

export interface StudentsListResponse {
  students: StudentListItem[];
}

// Interface for student registration response
export interface StudentRegistrationResponse {
  student_user_id: number;
  temporary_email: string;
  temporary_password: string;
  message: string;
}

export interface SessionProgress {
  step: number;
  correct: boolean;
}

export interface GameSession {
  session_id: string;
  game_id: string;
  total_questions: number;
  progress: SessionProgress[];
  status: 'in_progress' | 'completed' | 'not_started';
  duration_seconds: number | null;
  still_in_progress: boolean;
}

export interface CalendarDay {
  date: string;
  weekday: string;
  is_today: boolean;
  in_current_month: boolean;
  sessions: GameSession[];
}

export interface CalendarMonth {
  name: string;
  year: number;
  weeks: CalendarDay[][];
}

export interface CalendarData {
  months: CalendarMonth[];
}

export interface MyStudentStats {
  student_id: number;
  full_name: string;
  intervention_starts: string | null;
  intervention_completed: string;
  avg_score_progress: string;
  interventions_above_30: {
    count: number;
    percent: number;
  };
  interventions_days_in_a_row: number;
  last_intervention: string;
}

export interface MyStudentsStatsResponse {
  user_id: number;
  role: string;
  total_students: number;
  students: MyStudentStats[];
}

export const studentService = {
  getStudents: async (): Promise<StudentsListResponse> => {
    const response = await api.get('/students/list');
    return response.data as StudentsListResponse;
  },

  addStudent: async (data: Omit<Student, 'id'>): Promise<StudentRegistrationResponse> => {
    const response = await api.post('/students/add', data);
    return response.data as StudentRegistrationResponse;
  },

  editStudent: async (id: number, data: Partial<Student>) => {
    const response = await api.patch(`/students/edit/${id}`, data);
    return response.data as Student;
  },

  deleteStudent: async (id: number) => {
    const response = await api.delete(`/students/delete/${id}`);
    return response.data as { message: string };
  },

  registerStudentByParent: async (
    data: Omit<Student, 'id'>
  ): Promise<StudentRegistrationResponse> => {
    const response = await api.post('/students/parent_register', data);
    return response.data as StudentRegistrationResponse;
  },

  registerStudentByTeacher: async (
    data: Omit<Student, 'id'>
  ): Promise<StudentRegistrationResponse> => {
    const response = await api.post('/students/teacher_register', data);
    return response.data as StudentRegistrationResponse;
  },

  getCalendar: async (studentId: string) => {
    const response = await api.post('/students/calendar/view', { student_id: parseInt(studentId) });
    return response.data as CalendarData;
  },
  getStudentsStatistics: async () => {
    const response = await api.get<StudentsStats>(`/students/stats`);
    return response.data as StudentsStats;
  },

  getMyStudentsStats: async () => {
    const response = await api.get<MyStudentsStatsResponse>(`/students/stats_my_students`);
    return response.data as MyStudentsStatsResponse;
  },
};
