import {
  <PERSON>lendT<PERSON>,
  MathTask,
  MathTask1,
  <PERSON>Task2,
  <PERSON>Task,
  <PERSON>Task1,
  MemoryTask2,
  MastermindTask,
  SemanticMappingTask,
  GameTask,
} from '../types';
import logger from '../../../utils/logger';
import { mathTasks1 } from './mathTasks1';
import { mathTasks2 } from './mathTasks2';
import { mastermindTasks } from './mastermindTasks';
import { semanticMappingTasks } from './semanticMappingTasks';

export const blendTasks: BlendTask[] = [
  {
    type: 'blend',
    image:
      'https://img.freepik.com/premium-vector/kids-drawing-cartoon-vector-illustration-earth-globe-icon-isolated-white-background_760559-3002.jpg?w=740',
    word: '__obe',
    options: ['cl', 'pl', 'gl', 'gr'],
    answer: 'gl',
  },
  {
    type: 'blend',
    image:
      'https://img.freepik.com/premium-vector/kids-drawing-cartoon-vector-illustration-pencil-icon-isolated-white-background_760559-3003.jpg?w=740',
    word: '__ock',
    options: ['cl', 'bl', 'gl', 'fl'],
    answer: 'bl',
  },
  {
    type: 'blend',
    image:
      'https://img.freepik.com/premium-vector/kids-drawing-cartoon-vector-illustration-pencil-icon-isolated-white-background_760559-3003.jpg?w=740',
    word: '__ane',
    options: ['pl', 'bl', 'cl', 'fl'],
    answer: 'pl',
  },
  {
    type: 'blend',
    image:
      'https://img.freepik.com/premium-vector/kids-drawing-cartoon-vector-illustration-pencil-icon-isolated-white-background_760559-3003.jpg?w=740',
    word: '__og',
    options: ['fr', 'dr', 'fl', 'gr'],
    answer: 'fr',
  },
  {
    type: 'blend',
    image:
      'https://img.freepik.com/premium-vector/kids-drawing-cartoon-vector-illustration-pencil-icon-isolated-white-background_760559-3003.jpg?w=740',
    word: '__ape',
    options: ['gr', 'dr', 'fr', 'tr'],
    answer: 'gr',
  },
];

export const mathTasks: MathTask[] = [
  {
    type: 'math',
    id: 1,
    visualObjects: {
      total: 5,
      subtract: 0,
      objectType: 'mushroom',
    },
    equation: {
      num1: 2,
      num2: 3,
      result: 5,
    },
  },
  {
    type: 'math',
    id: 2,
    visualObjects: {
      total: 5,
      subtract: 2,
      objectType: 'donut',
    },
    equation: {
      num1: 5,
      num2: 2,
      result: 3,
    },
  },
  {
    type: 'math',
    id: 3,
    visualObjects: {
      total: 12,
      subtract: 0,
      objectType: 'flower',
    },
    equation: {
      num1: 3,
      num2: 4,
      result: 12,
    },
  },
  {
    type: 'math',
    id: 4,
    visualObjects: {
      total: 10,
      subtract: 5,
      objectType: 'pencil',
    },
    equation: {
      num1: 10,
      num2: 2,
      result: 5,
    },
  },
  {
    type: 'math',
    id: 5,
    visualObjects: {
      total: 15,
      subtract: 0,
      objectType: 'backpack',
    },
    equation: {
      num1: 7,
      num2: 8,
      result: 15,
    },
  },
  {
    type: 'math',
    id: 6,
    visualObjects: {
      total: 9,
      subtract: 4,
      objectType: 'calculator',
    },
    equation: {
      num1: 9,
      num2: 4,
      result: 5,
    },
  },
];

export const memoryTasks: MemoryTask[] = [
  {
    type: 'memory',
    cards: [
      { id: 1, value: 'A', matched: false },
      { id: 2, value: 'A', matched: false },
      { id: 3, value: 'B', matched: false },
      { id: 4, value: 'B', matched: false },
      { id: 5, value: 'C', matched: false },
      { id: 6, value: 'C', matched: false },
      { id: 7, value: 'D', matched: false },
      { id: 8, value: 'D', matched: false },
      { id: 9, value: 'E', matched: false },
      { id: 10, value: 'E', matched: false },
      { id: 11, value: 'F', matched: false },
      { id: 12, value: 'F', matched: false },
    ],
  },
];

export const memoryTasks1: MemoryTask1[] = [
  {
    type: 'memory-game1',
    targetValue: '5',
    operation: '+',
    cards: [
      { id: 1, value: '2', matched: false },
      { id: 2, value: '3', matched: false },
      { id: 3, value: '4', matched: false },
      { id: 4, value: '1', matched: false },
      { id: 5, value: '6', matched: false },
      { id: 6, value: '-1', matched: false },
      { id: 7, value: '0', matched: false },
      { id: 8, value: '5', matched: false },
      { id: 9, value: '7', matched: false },
      { id: 10, value: '-2', matched: false },
      { id: 11, value: '8', matched: false },
      { id: 12, value: '-3', matched: false },
    ],
    matchPairs: [
      { num1: '2', num2: '3' },
      { num1: '4', num2: '1' },
      { num1: '6', num2: '-1' },
      { num1: '0', num2: '5' },
      { num1: '7', num2: '-2' },
      { num1: '8', num2: '-3' },
    ],
  },
  {
    type: 'memory-game1',
    targetValue: '10',
    operation: '×',
    cards: [
      { id: 1, value: '5', matched: false },
      { id: 2, value: '2', matched: false },
      { id: 3, value: '10', matched: false },
      { id: 4, value: '1', matched: false },
      { id: 5, value: '20', matched: false },
      { id: 6, value: '0.5', matched: false },
      { id: 7, value: '4', matched: false },
      { id: 8, value: '2.5', matched: false },
      { id: 9, value: '1', matched: false },
      { id: 10, value: '10', matched: false },
      { id: 11, value: '3', matched: false },
      { id: 12, value: '3.33', matched: false },
    ],
    matchPairs: [
      { num1: '5', num2: '2' },
      { num1: '10', num2: '1' },
      { num1: '20', num2: '0.5' },
      { num1: '4', num2: '2.5' },
      { num1: '1', num2: '10' },
      { num1: '3', num2: '3.33' },
    ],
  },
  {
    type: 'memory-game1',
    targetValue: '8',
    operation: '-',
    cards: [
      { id: 1, value: '10', matched: false },
      { id: 2, value: '2', matched: false },
      { id: 3, value: '12', matched: false },
      { id: 4, value: '4', matched: false },
      { id: 5, value: '9', matched: false },
      { id: 6, value: '1', matched: false },
      { id: 7, value: '15', matched: false },
      { id: 8, value: '7', matched: false },
      { id: 9, value: '13', matched: false },
      { id: 10, value: '5', matched: false },
      { id: 11, value: '11', matched: false },
      { id: 12, value: '3', matched: false },
    ],
    matchPairs: [
      { num1: '10', num2: '2' },
      { num1: '12', num2: '4' },
      { num1: '9', num2: '1' },
      { num1: '15', num2: '7' },
      { num1: '13', num2: '5' },
      { num1: '11', num2: '3' },
    ],
  },
];

/**
 * @param gameType
 * @returns
 */
export const memoryTasks2: MemoryTask2[] = [
  {
    type: 'memory-game2',
    result: '12',
    secondNumber: '3',
    cards: [
      { id: 1, value: '9', matched: false, type: 'number' },
      { id: 2, value: '+', matched: false, type: 'operator' },
      { id: 3, value: '4', matched: false, type: 'number' },
      { id: 4, value: '×', matched: false, type: 'operator' },
      { id: 5, value: '15', matched: false, type: 'number' },
      { id: 6, value: '-', matched: false, type: 'operator' },
      { id: 7, value: '36', matched: false, type: 'number' },
      { id: 8, value: '÷', matched: false, type: 'operator' },
      { id: 9, value: '6', matched: false, type: 'number' },
      { id: 10, value: '+', matched: false, type: 'operator' },
      { id: 11, value: '24', matched: false, type: 'number' },
      { id: 12, value: '÷', matched: false, type: 'operator' },
    ],
    matchPairs: [
      { number: '9', operator: '+', secondNumber: '3', result: '12' },
      { number: '4', operator: '×', secondNumber: '3', result: '12' },
      { number: '15', operator: '-', secondNumber: '3', result: '12' },
      { number: '36', operator: '÷', secondNumber: '3', result: '12' },
      { number: '6', operator: '+', secondNumber: '6', result: '12' },
      { number: '24', operator: '÷', secondNumber: '2', result: '12' },
    ],
  },
  {
    type: 'memory-game2',
    result: '10',
    secondNumber: '5',
    cards: [
      { id: 1, value: '5', matched: false, type: 'number' },
      { id: 2, value: '+', matched: false, type: 'operator' },
      { id: 3, value: '2', matched: false, type: 'number' },
      { id: 4, value: '×', matched: false, type: 'operator' },
      { id: 5, value: '15', matched: false, type: 'number' },
      { id: 6, value: '-', matched: false, type: 'operator' },
      { id: 7, value: '50', matched: false, type: 'number' },
      { id: 8, value: '÷', matched: false, type: 'operator' },
    ],
    matchPairs: [
      { number: '5', operator: '+', secondNumber: '5', result: '10' },
      { number: '2', operator: '×', secondNumber: '5', result: '10' },
      { number: '15', operator: '-', secondNumber: '5', result: '10' },
      { number: '50', operator: '÷', secondNumber: '5', result: '10' },
    ],
  },
];

export { semanticMappingTasks };
export { mastermindTasks };

export const getTasksByType = (gameType: string): GameTask[] => {
  switch (gameType) {
    case 'blend':
      return blendTasks;
    case 'math':
      return mathTasks;
    case 'math-game1':
      return mathTasks1;
    case 'math-game2':
      return memoryTasks2; // Używamy memoryTasks2 dla math-game2 (tymczasowo)
    case 'memory':
      return memoryTasks;
    case 'memory-game1':
      return memoryTasks1;
    case 'memory-game2':
      return memoryTasks2;
    case 'mastermind-game':
      return mastermindTasks; // Nowy typ gry "Złam kod liczbowy"
    case 'support-strategy--semantic-mapping':
      return semanticMappingTasks;
    default:
      logger.error(`Unknown game type: ${gameType}`);
      return [];
  }
};
