import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useAssignedInterventions } from '../../services/queries/interventionQueries';
import { DashboardButton } from '../../components/dashboard/DashboardButton';
import { ChatPage } from '../chat/ChatPage';
import { InterventionsGrid } from '../../components/students/InterventionsGrid';
import TestsGrid from '../../components/tests/TestsGrid';

import { Student } from '../../services/studentService';
// import interventionService from '../../services/interventionService';

import progressIcon from '../../assets/u_progress.svg';
import documentsIcon from '../../assets/documents2.svg';

export const StudentDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState<'dashboard' | 'chat'>('dashboard');
  const [studentData, setStudentData] = useState<Partial<Student> | null>(null);
  const [studentName, setStudentName] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);

  // Pobieranie przypisanych interwencji za pomocą hooka useAssignedInterventions
  const {
    data: interventions,
    isLoading: interventionsLoading,
    isError: interventionsError,
    refetch: refetchInterventions,
  } = useAssignedInterventions(id ? Number(id) : undefined);

  // Get student name from location state if available
  useEffect(() => {
    if (location.state?.studentName) {
      setStudentName(location.state.studentName);
    }
  }, [location.state]);

  useEffect(() => {
    if (location.pathname.includes('/chat')) {
      setActiveTab('chat');
    } else {
      setActiveTab('dashboard');
    }
  }, [location.pathname]);

  useEffect(() => {
    if (id) {
      // Note: getStudent API removed - using student ID from URL
      setStudentData({
        id: Number(id),
        first_name: 'Student',
        last_name: `#${id}`,
        age: 0,
        gender: '',
        email: '',
        password: '',
        role_id: 0,
      });
      setLoading(false);
    }
  }, [id]);

  const baseTabStyle = 'font-fira-sans text-[24px] leading-[32px] tracking-[0%] align-middle';
  const activeTabStyle = `${baseTabStyle} border-b border-[#5D5D5D] px-4 py-2 rounded-t-lg text-[#5E6C84] bg-white font-medium`;
  const inactiveTabStyle = `${baseTabStyle} border-transparent px-4 py-2 text-[#767572] hover:text-[#5E6C84] hover:border-gray-300 opacity-60`;

  return (
    <div className="space-y-8">
      <div className="border-b border-[#5D5D5D]">
        <nav className="-mb-px flex gap-[10px]">
          <button
            onClick={() => {
              setActiveTab('dashboard');
              navigate(`/dashboard/student/${id}`, { replace: true });
            }}
            className={`inline-flex items-center ${activeTab === 'dashboard' ? activeTabStyle : inactiveTabStyle}`}
          >
            {loading && !studentName
              ? 'Loading...'
              : studentName ||
                (studentData ? `${studentData.first_name} ${studentData.last_name}` : 'Dashboard')}
          </button>
          <button
            onClick={() => {
              setActiveTab('chat');
              navigate(`/dashboard/student/${id}/chat`);
            }}
            className={`inline-flex items-center ${activeTab === 'chat' ? activeTabStyle : inactiveTabStyle}`}
          >
            Chat Ai
          </button>
        </nav>
      </div>

      {activeTab === 'dashboard' && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <DashboardButton
              icon={progressIcon}
              text="Student progress"
              url={`/dashboard/student/${id}/progress`}
              onClick={() => navigate(`/dashboard/student/${id}/progress`)}
            />
            <DashboardButton
              icon={documentsIcon}
              text="Documents"
              url={`/dashboard/student/${id}/documents`}
              onClick={() => navigate(`/dashboard/student/${id}/documents`)}
            />
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-6">Interventions</h2>
            {interventionsLoading ? (
              <div className="text-center py-8">Loading interventions...</div>
            ) : interventions && interventions.length > 0 ? (
              <InterventionsGrid interventions={interventions} />
            ) : (
              <div className="text-center py-8 text-gray-500">No assigned interventions</div>
            )}
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-6">Tests</h2>
            {id ? <TestsGrid studentId={Number(id)} /> : null}
          </div>
        </>
      )}

      {activeTab === 'chat' && (
        <div className="p-4">
          <ChatPage studentId={id} studentName={studentName} />
        </div>
      )}
    </div>
  );
};
