import React, { useState, useEffect } from 'react';
import { GameRendererProps } from '../types';
import { MemoryTask2, MastermindTask } from '../types';
import { KeySquare } from 'lucide-react';
import { gameWebSocket } from '../../common/websocket';
import logger from '../../../utils/logger';

interface GuessHistoryItem {
  guess: string;
  feedback: string;
  correctPlace: number;
  correctDigit: number;
  wrong: number;
}

const MemoryRenderer2: React.FC<GameRendererProps> = props => {
  const {
    task,
    onAnswer,
    showFeedback = false,
    currentTaskIndex = 0,
    totalTasks = 1,
    sessionId,
    stats = {
      correctAnswers: 0,
      incorrectAnswers: 0,
      totalQuestions: 1,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: '',
      studentId: '',
    },
  } = props;

  const [secret, setSecret] = useState<string>('');
  const [guess, setGuess] = useState('');
  const [history, setHistory] = useState<GuessHistoryItem[]>([]);
  const [attempts, setAttempts] = useState(0);
  const [won, setWon] = useState(false);
  const [difficulty, setDifficulty] = useState(3); // Domyślnie 3-cyfrowa liczba

  useEffect(() => {
    // Ustaw trudność na podstawie właściwości zadania (jeśli dostępne)
    if (task.type === 'mastermind-game') {
      const mastermindTask = task as MastermindTask;
      if (mastermindTask.difficulty) {
        setDifficulty(mastermindTask.difficulty);
      }
    }
  }, [task]);

  useEffect(() => {
    // Wylosuj liczbę bez powtarzających się cyfr
    const generateSecret = () => {
      const digits: string[] = [];
      while (digits.length < difficulty) {
        const d = Math.floor(Math.random() * 10).toString();
        if (digits.length === 0 && d === '0') continue; // nie zaczynaj od 0
        if (!digits.includes(d)) digits.push(d);
      }
      return digits.join('');
    };

    const newSecret = generateSecret();
    // Generated secret number

    setSecret(newSecret);
    setHistory([]);
    setAttempts(0);
    setWon(false);
    setGuess('');

    // Resetuj statystyki
    if (stats) {
      stats.correctAnswers = 0;
      stats.incorrectAnswers = 0;
      stats.startTime = Date.now();
      stats.endTime = null;
      stats.progress = [];
    }
  }, [difficulty, stats]);

  const checkGuess = (g: string, s: string) => {
    let correctPlace = 0;
    let correctDigit = 0;

    // Sprawdź cyfry na właściwych pozycjach
    for (let i = 0; i < s.length; i++) {
      if (g[i] === s[i]) correctPlace++;
    }

    // Sprawdź cyfry na niewłaściwych pozycjach
    // Najpierw zlicz wystąpienia każdej cyfry w tajnej liczbie
    const secretDigits: { [key: string]: number } = {};
    for (const digit of s) {
      secretDigits[digit] = (secretDigits[digit] || 0) + 1;
    }

    // Odejmij cyfry, które są już na właściwych pozycjach
    for (let i = 0; i < s.length; i++) {
      if (g[i] === s[i] && secretDigits[g[i]] > 0) {
        secretDigits[g[i]]--;
      }
    }

    // Policz cyfry na niewłaściwych pozycjach
    for (let i = 0; i < s.length; i++) {
      if (g[i] !== s[i] && secretDigits[g[i]] > 0) {
        correctDigit++;
        secretDigits[g[i]]--;
      }
    }

    const wrong = s.length - correctPlace - correctDigit;
    return { correctPlace, correctDigit, wrong };
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (guess.length !== difficulty || /[^0-9]/.test(guess)) return;

    setAttempts(a => a + 1);
    const res = checkGuess(guess, secret);

    let feedback = '';
    if (res.correctPlace === difficulty) {
      feedback = '🎉 Brawo! Złamano kod!';
      setWon(true);

      // Aktualizuj statystyki
      if (stats) {
        const currentTime = Date.now();

        if (!stats.progress) {
          stats.progress = [];
        }

        stats.progress.push({
          step: attempts + 1,
          correct: true,
        });

        const updatedStats = {
          ...stats,
          sessionId: sessionId,
          correctAnswers: 1,
          totalQuestions: 1,
          completionPercentage: 100,
          endTime: currentTime,
          timeInSeconds: Math.round((currentTime - stats.startTime) / 1000),
          progress: [...stats.progress],
        };

        try {
          gameWebSocket.sendGameStats(updatedStats);
          // Stats sent after winning
        } catch (error) {
          logger.error('[MemoryRenderer2] Failed to send stats:', error);
        }

        Object.assign(stats, updatedStats);
      }

      if (onAnswer) onAnswer(true, attempts + 1);
    } else {
      feedback = `${res.correctPlace} ✔️ na miejscu, ${res.correctDigit} 🔄 nie na miejscu, ${res.wrong} ❌ brak`;

      // Aktualizuj statystyki dla niepoprawnej próby
      if (stats) {
        const currentTime = Date.now();

        if (!stats.progress) {
          stats.progress = [];
        }

        stats.progress.push({
          step: attempts + 1,
          correct: false,
        });

        const updatedStats = {
          ...stats,
          sessionId: sessionId,
          incorrectAnswers: (stats.incorrectAnswers || 0) + 1,
          endTime: currentTime,
          timeInSeconds: Math.round((currentTime - stats.startTime) / 1000),
          progress: [...stats.progress],
        };

        try {
          gameWebSocket.sendGameStats(updatedStats);
          // Stats sent after incorrect attempt
        } catch (error) {
          logger.error('[MemoryRenderer2] Failed to send stats:', error);
        }

        Object.assign(stats, updatedStats);
      }
    }

    setHistory(h => [...h, { guess, feedback, ...res }]);
    setGuess('');
  };

  const handleReset = () => {
    // Wylosuj nową liczbę bez powtarzających się cyfr
    const generateSecret = () => {
      const digits: string[] = [];
      while (digits.length < difficulty) {
        const d = Math.floor(Math.random() * 10).toString();
        if (digits.length === 0 && d === '0') continue; // nie zaczynaj od 0
        if (!digits.includes(d)) digits.push(d);
      }
      return digits.join('');
    };

    const newSecret = generateSecret();
    // Generated new secret number

    setSecret(newSecret);
    setHistory([]);
    setAttempts(0);
    setWon(false);
    setGuess('');

    // Resetuj statystyki
    if (stats) {
      stats.correctAnswers = 0;
      stats.incorrectAnswers = 0;
      stats.startTime = Date.now();
      stats.endTime = null;
      stats.progress = [];
    }
  };

  const changeDifficulty = (newDifficulty: number) => {
    if (newDifficulty >= 3 && newDifficulty <= 5) {
      setDifficulty(newDifficulty);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="w-full mb-8">
        <div className="flex items-center justify-center mb-4">
          <KeySquare className="w-8 h-8 text-indigo-600 mr-2" />
          <h1 className="text-3xl md:text-4xl font-bold text-indigo-700">Number Code Breaker</h1>
        </div>

        <div className="flex justify-between items-center px-2">
          <div className="text-sm md:text-base text-gray-700 font-medium">
            Level: <span className="text-indigo-700">{currentTaskIndex + 1}</span> / {totalTasks}
          </div>

          <div className="bg-gradient-to-r from-indigo-500 to-indigo-700 rounded-full h-2 w-1/2 mx-2">
            <div
              className="bg-gradient-to-r from-indigo-300 to-indigo-500 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${((currentTaskIndex + 1) / totalTasks) * 100}%` }}
            ></div>
          </div>

          <div className="text-sm md:text-base text-gray-700 font-medium">
            Attempts: <span className="text-indigo-700">{attempts}</span>
          </div>
        </div>
      </div>

      <div className="w-full p-6 rounded-xl bg-white shadow-lg transition-all duration-500 ease-in-out">
        <div className="mb-6 text-center">
          <div className="flex justify-center space-x-8">
            <div className="bg-indigo-100 p-3 rounded-lg shadow-md min-w-[120px] text-center">
              <div className="text-sm font-medium text-indigo-600 mb-1">Difficulty</div>
              <div className="text-2xl font-bold text-indigo-800">{difficulty} digits</div>
            </div>
            <div className="bg-indigo-100 p-3 rounded-lg shadow-md min-w-[120px] text-center">
              <div className="text-sm font-medium text-indigo-600 mb-1">Attempts</div>
              <div className="text-2xl font-bold text-indigo-800">{attempts}</div>
            </div>
          </div>
        </div>

        <div className="bg-yellow-100 border-l-4 border-yellow-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-yellow-600"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Secret number (for testing): <span className="font-bold">{secret}</span>
              </p>
            </div>
          </div>
        </div>

        <div className="mb-6 text-center">
          <p className="text-gray-700 mb-4">
            The computer has selected a {difficulty}-digit number with no repeating digits. Guess it
            in as few attempts as possible!
          </p>

          <div className="bg-indigo-50 p-4 rounded-lg mb-4">
            <h3 className="font-bold text-indigo-800 mb-2">Hints after each attempt:</h3>
            <ul className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-700">
              <li className="flex items-center justify-center">
                <span className="bg-green-100 text-green-800 rounded-full w-6 h-6 flex items-center justify-center mr-2">
                  ✔️
                </span>
                Digit in the correct position
              </li>
              <li className="flex items-center justify-center">
                <span className="bg-yellow-100 text-yellow-800 rounded-full w-6 h-6 flex items-center justify-center mr-2">
                  🔄
                </span>
                Digit present but in wrong position
              </li>
              <li className="flex items-center justify-center">
                <span className="bg-red-100 text-red-800 rounded-full w-6 h-6 flex items-center justify-center mr-2">
                  ❌
                </span>
                Digit not in the number
              </li>
            </ul>
          </div>
        </div>

        <div className="flex justify-center gap-4 mb-6">
          <button
            onClick={() => changeDifficulty(3)}
            className={`px-4 py-2 rounded-lg font-medium transition-all ${difficulty === 3 ? 'bg-indigo-600 text-white shadow-md' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
          >
            3 digits
          </button>
          <button
            onClick={() => changeDifficulty(4)}
            className={`px-4 py-2 rounded-lg font-medium transition-all ${difficulty === 4 ? 'bg-indigo-600 text-white shadow-md' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
          >
            4 digits
          </button>
          <button
            onClick={() => changeDifficulty(5)}
            className={`px-4 py-2 rounded-lg font-medium transition-all ${difficulty === 5 ? 'bg-indigo-600 text-white shadow-md' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
          >
            5 digits
          </button>
        </div>

        <form onSubmit={handleSubmit} className="flex gap-2 justify-center mb-6">
          <input
            type="text"
            maxLength={difficulty}
            pattern={`[0-9]{${difficulty}}`}
            value={guess}
            onChange={e => {
              const val = e.target.value.replace(/[^0-9]/g, '').slice(0, difficulty);
              setGuess(val);
            }}
            disabled={won}
            className="w-32 text-center text-2xl border-2 border-indigo-400 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-indigo-300"
            placeholder={'_'.repeat(difficulty)}
          />
          <button
            type="submit"
            disabled={won || guess.length !== difficulty}
            className="px-4 py-2 bg-indigo-600 text-white font-bold rounded-lg shadow hover:bg-indigo-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Check
          </button>
        </form>

        <div className="mb-6 text-center">
          <div className="inline-block bg-indigo-100 px-4 py-2 rounded-lg">
            <span className="text-gray-700">Current attempt: </span>
            <span className="font-bold text-indigo-800 text-lg">{attempts + (won ? 0 : 1)}</span>
          </div>
        </div>

        <div className="overflow-y-auto max-h-64 mb-6 rounded-lg border border-gray-200">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-indigo-50">
              <tr>
                <th
                  scope="col"
                  className="px-4 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider"
                >
                  Attempt
                </th>
                <th
                  scope="col"
                  className="px-4 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider"
                >
                  Your Number
                </th>
                <th
                  scope="col"
                  className="px-4 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider"
                >
                  Hints
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {history.map((item, idx) => (
                <tr
                  key={idx}
                  className={item.feedback.includes('Brawo') ? 'bg-green-50' : 'hover:bg-gray-50'}
                >
                  <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                    {idx + 1}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm font-mono font-bold text-indigo-600">
                    {item.guess}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <span className="bg-green-100 text-green-800 rounded-full w-5 h-5 flex items-center justify-center text-xs mr-1">
                          {item.correctPlace}
                        </span>
                        <span className="ml-1">✔️</span>
                      </span>
                      <span className="flex items-center">
                        <span className="bg-yellow-100 text-yellow-800 rounded-full w-5 h-5 flex items-center justify-center text-xs mr-1">
                          {item.correctDigit}
                        </span>
                        <span className="ml-1">🔄</span>
                      </span>
                      <span className="flex items-center">
                        <span className="bg-red-100 text-red-800 rounded-full w-5 h-5 flex items-center justify-center text-xs mr-1">
                          {item.wrong}
                        </span>
                        <span className="ml-1">❌</span>
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {won && (
          <div className="mb-6 p-6 bg-gradient-to-r from-green-50 to-green-100 rounded-xl border border-green-200 text-center shadow-lg">
            <div className="text-5xl mb-3">🎉</div>
            <h3 className="text-2xl font-bold text-green-800 mb-2">
              Congratulations! You cracked the code in {attempts}{' '}
              {attempts === 1 ? 'attempt' : 'attempts'}!
            </h3>
            <p className="text-gray-700">
              Secret number was:{' '}
              <span className="font-mono font-bold text-indigo-700 text-xl">{secret}</span>
            </p>
          </div>
        )}

        <div className="flex justify-center">
          <button
            onClick={handleReset}
            className="px-6 py-3 bg-indigo-600 text-white font-bold rounded-lg shadow-md hover:bg-indigo-700 transition-all hover:shadow-lg transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            New Game
          </button>
        </div>

        {showFeedback && (
          <div className="mt-6 p-4 rounded-lg bg-green-50 border border-green-200 text-center">
            <p className="text-lg font-medium text-green-700">
              Great job! You completed the level in {props.userAnswer} attempts.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MemoryRenderer2;
