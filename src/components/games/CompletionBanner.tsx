import React from 'react';
import { useGameStatsStore } from '../../store/gameStatsStore';

interface CompletionBannerProps {
  gameId: string;
  studentId: string;
}

export const CompletionBanner: React.FC<CompletionBannerProps> = ({ gameId, studentId }) => {
  const latestStats = useGameStatsStore(state => state.getLatestGameStats(gameId, studentId));

  if (!latestStats) {
    return null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div className="bg-green-500 text-white p-8 rounded-xl shadow-2xl max-w-md w-full text-center transform transition-all">
        <div className="mb-4 text-5xl">✓</div>
        <h2 className="text-2xl font-bold mb-4">Completed!</h2>

        <div className="mb-6 text-lg">
          <div className="flex justify-between mb-2">
            <span>Score:</span>
            <span className="font-bold">
              {latestStats.correctAnswers}/{latestStats.totalQuestions}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Time:</span>
            <span className="font-bold">
              {(() => {
                let timeInSeconds = 0;

                if (latestStats.endTime && latestStats.startTime) {
                  timeInSeconds = Math.round((latestStats.endTime - latestStats.startTime) / 1000);
                } else if (latestStats.timeInSeconds) {
                  timeInSeconds = latestStats.timeInSeconds;
                }

                const minutes = Math.floor(timeInSeconds / 60);
                const seconds = (timeInSeconds % 60).toString().padStart(2, '0');

                return `${minutes}:${seconds}`;
              })()}
            </span>
          </div>
        </div>

        <div className="text-sm opacity-75">Redirecting to games page...</div>
      </div>
    </div>
  );
};

export default CompletionBanner;
