import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { studentService, StudentListItem } from '../services/studentService';
import logger from '../utils/logger';
import { AxiosError } from 'axios';

interface StudentsState {
  students: StudentListItem[];
  loading: boolean;
  error: string | null;
  fetchStudents: () => Promise<void>;
  getStudentById: (id: number) => StudentListItem | undefined;
}

export const useStudentsStore = create<StudentsState>()(
  devtools(
    (set, get) => ({
      students: [],
      loading: false,
      error: null,

      fetchStudents: async () => {
        try {
          set({ loading: true, error: null });
          const response = await studentService.getStudents();
          set({
            students: Array.isArray(response.students) ? response.students : [],
            loading: false,
            error: null,
          });
        } catch (error) {
          let errorMessage = 'Failed to load student list';

          if (error instanceof AxiosError) {
            errorMessage = error.response?.data?.message || error.message;
          } else if (error instanceof Error) {
            errorMessage = error.message;
          }

          logger.error('Error fetching students:', error);
          set({
            students: [],
            error: errorMessage,
            loading: false,
          });
        }
      },

      getStudentById: (id: number) => {
        return get().students.find(student => student.id === id);
      },
    }),
    {
      name: 'students-storage',
    }
  )
);

// Export useStudentsStore hook as default
export default useStudentsStore;
