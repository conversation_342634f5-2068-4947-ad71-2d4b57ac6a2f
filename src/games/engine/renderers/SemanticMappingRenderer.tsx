import React, { useState, useEffect } from 'react';
import { GameRendererProps } from '../types';
import { SemanticMappingTask } from '../types';
import { BookOpen } from 'lucide-react';

const SemanticMappingRenderer: React.FC<GameRendererProps> = props => {
  const {
    task,
    onAnswer,
    showFeedback = false,
    userAnswer = null,
    currentTaskIndex = 0,
    totalTasks = 1,
    stats = {
      correctAnswers: 0,
      incorrectAnswers: 0,
      totalQuestions: 1,
      completionPercentage: 0,
      startTime: Date.now(),
      endTime: null,
      gameId: '',
      studentId: '',
    },
  } = props;

  const semanticTask = task as SemanticMappingTask;

  // Stan dla elementów przeciągniętych do odpowiednich miejsc
  const [placedWords, setPlacedWords] = useState<string[]>([]);
  const [placedImages, setPlacedImages] = useState<string[]>([]);

  // Stan dla aktualnie przeciąganego elementu
  const [draggedItem, setDraggedItem] = useState<{ type: 'word' | 'image'; id: string } | null>(
    null
  );

  // Resetowanie stanu przy zmianie zadania
  useEffect(() => {
    setPlacedWords([]);
    setPlacedImages([]);
  }, [semanticTask.id]);

  // Sprawdzanie, czy wszystkie poprawne elementy zostały umieszczone
  const allCorrectItemsPlaced = () => {
    const correctWords = semanticTask.relatedWords
      .filter(word => word.correct)
      .map(word => word.word);
    const correctImages = semanticTask.images.filter(img => img.correct).map(img => img.url);

    const allWordsPlaced = correctWords.every(word => placedWords.includes(word));
    const allImagesPlaced = correctImages.every(img => placedImages.includes(img));

    return allWordsPlaced && allImagesPlaced;
  };

  // Sprawdzanie poprawności odpowiedzi
  useEffect(() => {
    if (placedWords.length > 0 || placedImages.length > 0) {
      const isCorrect = allCorrectItemsPlaced();

      // Jeśli wszystkie poprawne elementy są na miejscu, wysyłamy odpowiedź
      if (isCorrect && !showFeedback) {
        onAnswer(true);
      }
    }
  }, [placedWords, placedImages]);

  // Obsługa rozpoczęcia przeciągania
  const handleDragStart = (type: 'word' | 'image', id: string) => {
    setDraggedItem({ type, id });
  };

  // Obsługa upuszczenia elementu
  const handleDrop = (position: string) => {
    if (!draggedItem || showFeedback) return;

    if (draggedItem.type === 'word') {
      if (!placedWords.includes(draggedItem.id)) {
        setPlacedWords([...placedWords, draggedItem.id]);
      }
    } else {
      if (!placedImages.includes(draggedItem.id)) {
        setPlacedImages([...placedImages, draggedItem.id]);
      }
    }

    setDraggedItem(null);
  };

  // Obsługa usunięcia elementu z mapy
  const handleRemoveItem = (type: 'word' | 'image', id: string) => {
    if (showFeedback) return;

    if (type === 'word') {
      setPlacedWords(placedWords.filter(word => word !== id));
    } else {
      setPlacedImages(placedImages.filter(img => img !== id));
    }
  };

  // Sprawdzanie, czy element jest poprawny
  const isItemCorrect = (type: 'word' | 'image', id: string) => {
    if (type === 'word') {
      const word = semanticTask.relatedWords.find(w => w.word === id);
      return word?.correct || false;
    } else {
      const image = semanticTask.images.find(img => img.url === id);
      return image?.correct || false;
    }
  };

  // Obliczanie procentu ukończenia
  const progressPercentage = (currentTaskIndex / totalTasks) * 100;

  return (
    <div className="flex flex-col items-center justify-center p-6 bg-blue-50 rounded-lg shadow-lg max-w-4xl mx-auto">
      {/* Nagłówek z tytułem i postępem */}
      <div className="w-full flex justify-between items-center mb-6">
        <div className="flex items-center">
          <BookOpen className="w-6 h-6 text-blue-600 mr-2" />
          <h2 className="text-2xl font-bold text-blue-700">Semantic Mapping</h2>
        </div>
        <div className="flex items-center">
          <span className="text-blue-700 font-semibold mr-2">
            Score: {stats.correctAnswers}/{stats.correctAnswers + stats.incorrectAnswers}
          </span>
        </div>
      </div>

      {/* Pasek postępu */}
      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-6">
        <div
          className="bg-blue-600 h-2.5 rounded-full"
          style={{ width: `${progressPercentage}%` }}
        ></div>
      </div>

      {/* Główny obszar gry */}
      <div className="w-full mb-6">
        {/* Centralne słowo */}
        <div className="flex justify-center mb-8">
          <div className="bg-blue-600 text-white text-2xl font-bold py-4 px-8 rounded-full shadow-lg">
            {semanticTask.targetWord}
          </div>
        </div>

        {/* Definicja słowa (jeśli istnieje) */}
        {semanticTask.definition && (
          <div className="text-center mb-6 italic text-gray-600">
            &ldquo;{semanticTask.definition}&rdquo;
          </div>
        )}

        {/* Obszar mapy semantycznej */}
        <div
          className="relative bg-white rounded-xl p-8 min-h-[300px] border-2 border-dashed border-blue-300"
          onDragOver={e => e.preventDefault()}
          onDrop={() => handleDrop('map')}
        >
          {/* Centralne słowo w mapie */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="bg-blue-600 text-white text-xl font-bold py-3 px-6 rounded-full shadow">
              {semanticTask.targetWord}
            </div>
          </div>

          {/* Umieszczone słowa */}
          <div className="flex flex-wrap justify-center gap-4 mb-6">
            {placedWords.map((wordId, index) => {
              const word = semanticTask.relatedWords.find(w => w.word === wordId);
              if (!word) return null;

              // Obliczanie pozycji dla słów (rozmieszczenie w okręgu)
              const angle = index * (360 / placedWords.length) * (Math.PI / 180);
              const radius = 120; // Promień okręgu
              const left = 50 + Math.cos(angle) * radius;
              const top = 50 + Math.sin(angle) * radius;

              return (
                <div
                  key={wordId}
                  className={`absolute cursor-pointer px-4 py-2 rounded-lg shadow ${
                    showFeedback
                      ? word.correct
                        ? 'bg-green-100 text-green-800 border border-green-500'
                        : 'bg-red-100 text-red-800 border border-red-500'
                      : 'bg-blue-100 text-blue-800 border border-blue-300'
                  }`}
                  style={{
                    left: `${left}%`,
                    top: `${top}%`,
                    transform: 'translate(-50%, -50%)',
                  }}
                  onClick={() => !showFeedback && handleRemoveItem('word', wordId)}
                >
                  <div className="flex items-center">
                    <span>{word.word}</span>
                    {showFeedback && <span className="ml-2">{word.correct ? '✓' : '✗'}</span>}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {word.type === 'related'
                      ? 'Related'
                      : word.type === 'category'
                        ? 'Category'
                        : word.type === 'synonym'
                          ? 'Synonym'
                          : 'Antonym'}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Umieszczone obrazki */}
          <div className="flex flex-wrap justify-center gap-4">
            {placedImages.map((imageUrl, index) => {
              const image = semanticTask.images.find(img => img.url === imageUrl);
              if (!image) return null;

              // Obliczanie pozycji dla obrazków (rozmieszczenie w większym okręgu)
              const angle = (index * (360 / placedImages.length) + 30) * (Math.PI / 180);
              const radius = 180; // Większy promień dla obrazków
              const left = 50 + Math.cos(angle) * radius;
              const top = 50 + Math.sin(angle) * radius;

              return (
                <div
                  key={imageUrl}
                  className={`absolute cursor-pointer rounded-lg shadow p-1 ${
                    showFeedback
                      ? image.correct
                        ? 'bg-green-100 border border-green-500'
                        : 'bg-red-100 border border-red-500'
                      : 'bg-white border border-blue-300'
                  }`}
                  style={{
                    left: `${left}%`,
                    top: `${top}%`,
                    transform: 'translate(-50%, -50%)',
                  }}
                  onClick={() => !showFeedback && handleRemoveItem('image', imageUrl)}
                >
                  <img src={imageUrl} alt={image.alt} className="w-16 h-16 object-cover rounded" />
                  {showFeedback && (
                    <div
                      className={`absolute -top-2 -right-2 w-6 h-6 flex items-center justify-center rounded-full ${
                        image.correct ? 'bg-green-500' : 'bg-red-500'
                      } text-white text-sm`}
                    >
                      {image.correct ? '✓' : '✗'}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Dostępne elementy do przeciągania */}
      <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Słowa */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-3 text-blue-700">Words</h3>
          <div className="flex flex-wrap gap-2">
            {semanticTask.relatedWords.map(word => {
              const isPlaced = placedWords.includes(word.word);

              return (
                <div
                  key={word.word}
                  className={`px-3 py-1 rounded-md cursor-pointer transition-all ${
                    isPlaced ? 'opacity-50 bg-gray-100' : 'bg-blue-100 hover:bg-blue-200'
                  }`}
                  draggable={!isPlaced && !showFeedback}
                  onDragStart={() =>
                    !isPlaced && !showFeedback && handleDragStart('word', word.word)
                  }
                  onClick={() => {
                    if (!isPlaced && !showFeedback) {
                      handleDragStart('word', word.word);
                      handleDrop('map');
                    }
                  }}
                >
                  {word.word}
                </div>
              );
            })}
          </div>
        </div>

        {/* Obrazki */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-3 text-blue-700">Images</h3>
          <div className="flex flex-wrap gap-2">
            {semanticTask.images.map(image => {
              const isPlaced = placedImages.includes(image.url);

              return (
                <div
                  key={image.url}
                  className={`p-1 rounded-md cursor-pointer transition-all ${
                    isPlaced
                      ? 'opacity-50 bg-gray-100'
                      : 'bg-white hover:bg-blue-50 border border-blue-200'
                  }`}
                  draggable={!isPlaced && !showFeedback}
                  onDragStart={() =>
                    !isPlaced && !showFeedback && handleDragStart('image', image.url)
                  }
                  onClick={() => {
                    if (!isPlaced && !showFeedback) {
                      handleDragStart('image', image.url);
                      handleDrop('map');
                    }
                  }}
                >
                  <img src={image.url} alt={image.alt} className="w-12 h-12 object-cover rounded" />
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Komunikat informacyjny */}
      <div className="mt-6 text-center text-gray-600">
        {!showFeedback ? (
          <p>
            Drag or click words and images to create an association map for the word &ldquo;
            {semanticTask.targetWord}&rdquo;
          </p>
        ) : (
          <div
            className={`p-4 rounded-lg w-full ${
              allCorrectItemsPlaced() ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
            }`}
          >
            <p className="text-xl font-bold">
              {allCorrectItemsPlaced()
                ? 'Great! You created a correct association map!'
                : 'Good try! Check which elements are correct.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SemanticMappingRenderer;
