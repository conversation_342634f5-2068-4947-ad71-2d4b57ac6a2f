import { QuizQuestion } from '@games/common/types';

export const quizQuestions: QuizQuestion[] = [
  {
    id: 1,
    type: 'subtraction',
    visualObjects: {
      total: 3,
      subtract: 1,
      objectType: 'donut',
    },
    equation: {
      num1: 3,
      num2: 1,
      result: 2,
    },
  },
  {
    id: 2,
    type: 'subtraction',
    visualObjects: {
      total: 6,
      subtract: 2,
      objectType: 'flower',
    },
    equation: {
      num1: 6,
      num2: 2,
      result: 4,
    },
  },
  {
    id: 3,
    type: 'subtraction',
    visualObjects: {
      total: 9,
      subtract: 7,
      objectType: 'pencil',
    },
    equation: {
      num1: 9,
      num2: 7,
      result: 2,
    },
  },
  {
    id: 4,
    type: 'subtraction',
    visualObjects: {
      total: 5,
      subtract: 0,
      objectType: 'backpack',
    },
    equation: {
      num1: 5,
      num2: 0,
      result: 5,
    },
  },
  {
    id: 5,
    type: 'subtraction',
    visualObjects: {
      total: 8,
      subtract: 3,
      objectType: 'calculator',
    },
    equation: {
      num1: 8,
      num2: 3,
      result: 5,
    },
  },
  {
    id: 6,
    type: 'subtraction',
    visualObjects: {
      total: 10,
      subtract: 5,
      objectType: 'mushroom',
    },
    equation: {
      num1: 10,
      num2: 5,
      result: 5,
    },
  },
];
