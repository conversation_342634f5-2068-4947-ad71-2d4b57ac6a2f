import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';

import { LoginPage } from './pages/auth/LoginPage';
import { RegisterPage } from './pages/auth/RegisterPage';
import { DashboardLayout } from './components/layout/DashboardLayout';
import { DashboardPage } from './pages/dashboard/DashboardPage';
import { StudentsPage } from './pages/dashboard/StudentsPage';
import { StudentDetailsPage } from './pages/dashboard/StudentDetailsPage';
import { StudentProgressPage } from './pages/dashboard/StudentProgressPage';
import { DocumentsPage } from './pages/dashboard/DocumentsPage';
import { DocumentChatPage } from './pages/dashboard/DocumentChatPage';
import { DocumentDetailsPage } from './pages/dashboard/DocumentDetailsPage';
import { GamesPage } from './components/dashboard/GamesPage';
import { GameDetailsPage } from './pages/games/GameDetailsPage';
import { ChatPage } from './pages/chat/ChatPage';
import { AvatarCreatorPage } from './pages/dashboard/AvatarCreatorPage';
import { ProfilePage } from './pages/dashboard/ProfilePage';
import { SettingsPage } from './pages/dashboard/SettingsPage';
import NotFoundPage from './pages/NotFoundPage';

import { ActivityPage } from './pages/activities/ActivityPage';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { authService } from './services/authService';
import { Toaster } from 'sonner';
import { useUserStore } from './store/userStore';
import { USER_ROLES } from './contants';
import GameExerciseLayout from './components/games/GameExerciseLayout';
import AssessmentExecution from './components/assessments/AssessmentExecution';
import AssessmentResults from './components/assessments/AssessmentResults';

export const App: React.FC = () => {
  const isAuthenticated = !!authService.getAccessToken();
  useUserStore();

  return (
    <div className="animate-fade-in min-h-screen">
      <BrowserRouter>
        <Toaster richColors position="bottom-right" />
        <Routes>
          <Route
            path="/"
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />

          {/* Trasa dla aktywności */}
          <Route
            path="/activities/:activityId"
            element={
              <ProtectedRoute allowedRoles={[USER_ROLES.STUDENT]}>
                <ActivityPage />
              </ProtectedRoute>
            }
          />

          {/* Dashboard routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<DashboardPage />} />
            <Route path="analytics" element={<div>Analytics Dashboard</div>} />
            <Route path="statistics" element={<div>Statistics Dashboard</div>} />

            {/* Document routes */}
            <Route
              path="documents"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.TEACHER]}>
                  <DocumentsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="document/:documentId"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.TEACHER]}>
                  <DocumentDetailsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="student/:studentId/documents/:documentId/chat"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.TEACHER]}>
                  <DocumentChatPage />
                </ProtectedRoute>
              }
            />

            {/* Trasa dla gier w trybie ćwiczeń */}
            <Route
              path="games/exercise/:activityId"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.STUDENT]}>
                  <GameExerciseLayout />
                </ProtectedRoute>
              }
            />

            {/* Dashboard routes */}
            <Route
              path="students"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.TEACHER]}>
                  <StudentsPage />
                </ProtectedRoute>
              }
            />

            {/* Strony tylko dla nauczycieli */}
            <Route
              path="students-list"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.TEACHER]}>
                  <StudentsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="student/:id"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.TEACHER]}>
                  <StudentDetailsPage />
                </ProtectedRoute>
              }
            >
              <Route path="chat" element={<ChatPage />} />
              <Route path="user/:userId/chat/:chatId" element={<ChatPage />} />
            </Route>
            <Route
              path="student/:id/progress"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.TEACHER]}>
                  <StudentProgressPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="student/:id/documents"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.TEACHER]}>
                  <DocumentsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="games"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.TEACHER]}>
                  <GamesPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="games/:gameId"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.TEACHER]}>
                  <GameDetailsPage />
                </ProtectedRoute>
              }
            />
            {/* Intervention routes */}
            <Route
              path="interventions"
              element={
                <ProtectedRoute>
                  <GamesPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="interventions/:gameId"
              element={
                <ProtectedRoute>
                  <GameDetailsPage />
                </ProtectedRoute>
              }
            />
            {/* Word-Building Puzzles game route */}
            <Route
              path="interventions/support-strategy--word-building-puzzles"
              element={
                <ProtectedRoute>
                  <GameDetailsPage />
                </ProtectedRoute>
              }
            />

            {/* Assessment routes */}
            <Route
              path="assessments/:assessmentId/session/:sessionId"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.STUDENT]}>
                  <AssessmentExecution />
                </ProtectedRoute>
              }
            />
            <Route
              path="assessments/:assessmentId/results/:sessionId"
              element={
                <ProtectedRoute allowedRoles={[USER_ROLES.STUDENT]}>
                  <AssessmentResults />
                </ProtectedRoute>
              }
            />

            {/* Tests routes */}
            <Route
              path="tests"
              element={
                <ProtectedRoute>
                  <GamesPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="tests/:gameId"
              element={
                <ProtectedRoute>
                  <GameDetailsPage />
                </ProtectedRoute>
              }
            />
            <Route path="settings" element={<SettingsPage />} />
            <Route path="profile" element={<ProfilePage />} />
            <Route path="settings/avatar-creator" element={<AvatarCreatorPage />} />
            <Route path="help" element={<div>Help page</div>} />
            <Route path="chat" element={<ChatPage />} />
            <Route path="user/:userId/chat/:chatId" element={<ChatPage />} />
          </Route>
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </BrowserRouter>
    </div>
  );
};

export default App;
