import { MathTask2 } from '../engine/types';

export const quizData: MathTask2[] = [
  // Do<PERSON>wanie
  {
    type: 'math-game2',
    id: 1,
    visualObjects: {
      total: 5,
      subtract: 0,
      objectType: 'donut',
    },
    equation: {
      num1: 3,
      num2: 2,
      result: 5, // 3 + 2 = 5
    },
  },
  {
    type: 'math-game2',
    id: 2,
    visualObjects: {
      total: 9,
      subtract: 0,
      objectType: 'flower',
    },
    equation: {
      num1: 4,
      num2: 5,
      result: 9, // 4 + 5 = 9
    },
  },
  // Odejmowanie
  {
    type: 'math-game2',
    id: 3,
    visualObjects: {
      total: 2,
      subtract: 0,
      objectType: 'pencil',
    },
    equation: {
      num1: 7,
      num2: 5,
      result: 2, // 7 - 5 = 2
    },
  },
  {
    type: 'math-game2',
    id: 4,
    visualObjects: {
      total: 4,
      subtract: 0,
      objectType: 'backpack',
    },
    equation: {
      num1: 10,
      num2: 6,
      result: 4, // 10 - 6 = 4
    },
  },
  // M<PERSON>żenie
  {
    type: 'math-game2',
    id: 5,
    visualObjects: {
      total: 12,
      subtract: 0,
      objectType: 'calculator',
    },
    equation: {
      num1: 3,
      num2: 4,
      result: 12, // 3 × 4 = 12
    },
  },
  {
    type: 'math-game2',
    id: 6,
    visualObjects: {
      total: 20,
      subtract: 0,
      objectType: 'mushroom',
    },
    equation: {
      num1: 5,
      num2: 4,
      result: 20, // 5 × 4 = 20
    },
  },
  // Dzielenie
  {
    type: 'math-game2',
    id: 7,
    visualObjects: {
      total: 3,
      subtract: 0,
      objectType: 'donut',
    },
    equation: {
      num1: 9,
      num2: 3,
      result: 3, // 9 ÷ 3 = 3
    },
  },
  {
    type: 'math-game2',
    id: 8,
    visualObjects: {
      total: 4,
      subtract: 0,
      objectType: 'flower',
    },
    equation: {
      num1: 12,
      num2: 3,
      result: 4, // 12 ÷ 3 = 4
    },
  },
];
