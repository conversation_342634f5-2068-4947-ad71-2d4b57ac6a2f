import React, { useState } from 'react';
import { useUserStore } from '../../store/userStore';
import { authService } from '../../services/authService';

export const ProfilePage: React.FC = () => {
  const { user } = useUserStore();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <h1 className="text-3xl font-bold text-gray-900 mb-8">User Profile</h1>

      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <div className="mt-1 text-gray-900">{user.email}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Role</label>
                <div className="mt-1 text-gray-900">{user.role_label}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <div className="mt-1">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      user.is_logged_in
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {user.is_logged_in ? 'Logged in' : 'Logged out'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Statistics</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Student ID</label>
                <div className="mt-1 text-gray-900">{user.student?.id || user.user_id}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">User ID</label>
                <div className="mt-1 text-gray-900">{user.user_id}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Last Login Date</label>
                <div className="mt-1 text-gray-900">-</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div
          className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {success && (
        <div
          className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <span className="block sm:inline">{success}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Documents</h2>
        <div className="space-y-4">
          <div>
            <a
              href="/api/users/terms-of-service"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primaryBtn hover:text-opacity-90"
            >
              Terms of Service
            </a>
          </div>
          <div>
            <a
              href="/api/users/privacy-policy"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primaryBtn hover:text-opacity-90"
            >
              Privacy Policy
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};
