import logger from 'src/utils/logger';

// Funkcja do utworzenia połączenia WebSocket
export const createWebSocketConnection = (endpoint: string): WebSocket => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const host = window.location.host;
  const wsUrl = `${protocol}//${host}${endpoint}`;

  const socket = new WebSocket(wsUrl);

  socket.onopen = () => {
    logger.log(`WebSocket connection established: ${wsUrl}`);

    // Send authentication token after connection is established
    const token = localStorage.getItem('token');
    if (token) {
      socket.send(JSON.stringify({ type: 'auth', token }));
    }
  };

  socket.onerror = error => {
    logger.error('WebSocket connection error:', error);
  };

  socket.onclose = event => {
    logger.log(`WebSocket connection closed with code: ${event.code}, reason: ${event.reason}`);

    // Automatyczne ponowne połączenie po 5 sekundach
    setTimeout(() => {
      logger.log('Trying to reconnect WebSocket...');
      createWebSocketConnection(endpoint);
    }, 5000);
  };

  return socket;
};

// Funkcja do zamknięcia połączenia WebSocket
export const closeWebSocketConnection = (socket: WebSocket | null) => {
  if (socket && socket.readyState === WebSocket.OPEN) {
    socket.close();
  }
};
