import BlendActivity from '@games/blend-game/BlendActivity';
import { sampleTasks } from '@games/blend-game/data';
import MathQuiz from '@games/math-game/MathQuiz';
import MathQuiz1 from '@games/math-game1/MathQuiz';
import MathQuiz2 from '@games/math-game2/MathQuiz';
import MathFillOperatorQuiz from '@games/math-game2/MathFillOperatorQuiz';
import MemoryGame from '@games/memory-game/MemoryGame';
import WordBuildingActivity from '@games/wordbuilding-game/WordBuildingActivity';
import MemoryGame1 from '@games/memory-game1/MemoryGame1';
import MemoryGame2 from '@games/memory-game2/MemoryGame2';
import SemanticMappingActivity from '@games/semantic-mapping/SemanticMappingActivity';
import Game from '@games/engine/Game';
import { mastermindTasks } from '@games/engine/data';
import { Task, GameStats } from '@games/common/types';
import { GameTask, MastermindTask } from '@games/engine/types';

export interface GameComponent {
  [key: string]: unknown;
  tasks?: Task[] | GameTask[];
  onComplete?: (correct: number, total: number, time: number) => void;
  studentId?: string;
  gameId?: string;
  onFinish?: (result: GameStats & { startTime: number; endTime: number }) => void;
  onFeedback?: (isCorrect: boolean) => void;
}

export interface GameDefinition {
  id: string;
  component: React.ComponentType<GameComponent>;
  defaultProps: Partial<GameComponent>;
}

const gameRegistry: Record<string, GameDefinition> = {
  'blend-game--quiz': {
    id: 'blend-game--quiz',
    component: BlendActivity as unknown as React.ComponentType<GameComponent>,
    defaultProps: {
      tasks: sampleTasks,
    },
  },
  'blend-game--word-building': {
    id: 'blend-game--word-building',
    component: BlendActivity as unknown as React.ComponentType<GameComponent>,
    defaultProps: {
      tasks: sampleTasks, // Using the same tasks as the quiz
    },
  },
  'math-game': {
    id: 'math-game',
    component: MathQuiz as unknown as React.ComponentType<GameComponent>,
    defaultProps: {},
  },
  'memory-game': {
    id: 'memory-game',
    component: MemoryGame as unknown as React.ComponentType<GameComponent>,
    defaultProps: {},
  },
  'support-strategy--word-building-puzzles': {
    id: 'support-strategy--word-building-puzzles',
    component: WordBuildingActivity as unknown as React.ComponentType<GameComponent>,
    defaultProps: {},
  },
  'memory-game1': {
    id: 'memory-game1',
    component: MemoryGame1 as unknown as React.ComponentType<GameComponent>,
    defaultProps: {},
  },
  'memory-game2': {
    id: 'memory-game2',
    component: MemoryGame2 as unknown as React.ComponentType<GameComponent>,
    defaultProps: {},
  },
  'math-game1': {
    id: 'math-game1',
    component: MathQuiz1 as unknown as React.ComponentType<GameComponent>,
    defaultProps: {},
  },
  'math-game2': {
    id: 'math-game2',
    component: MathQuiz2 as unknown as React.ComponentType<GameComponent>,
    defaultProps: {},
  },
  'support-strategy--semantic-mapping': {
    id: 'support-strategy--semantic-mapping',
    component: SemanticMappingActivity as unknown as React.ComponentType<GameComponent>,
    defaultProps: {},
  },
  'mastermind-game': {
    id: 'mastermind-game',
    component: Game as unknown as React.ComponentType<GameComponent>,
    defaultProps: {
      tasks: mastermindTasks,
    },
  },
};

export const getGameDefinition = (gameId: string): GameDefinition | null => {
  return gameRegistry[gameId] || null;
};

export const gameExists = (gameId: string): boolean => {
  return !!gameRegistry[gameId];
};

export const getAllGames = (): GameDefinition[] => {
  return Object.values(gameRegistry);
};

export default {
  getGameDefinition,
  gameExists,
  getAllGames,
};
